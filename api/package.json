{"name": "api", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --watch", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@japa/api-client": "^3.1.0", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^4.0.0", "@japa/runner": "^4.2.0", "@swc/core": "1.11.24", "@types/luxon": "^3.6.2", "@types/node": "^22.15.18", "eslint": "^9.26.0", "hot-hook": "^0.4.0", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "ts-node-maintained": "^10.9.5", "typescript": "~5.8"}, "dependencies": {"@adonisjs/auth": "^9.4.0", "@adonisjs/core": "^6.18.0", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.4.1", "@adonisjs/i18n": "^2.2.1", "@adonisjs/limiter": "^2.4.0", "@adonisjs/lucid": "^21.6.1", "@adonisjs/mail": "^9.2.2", "@adonisjs/redis": "^9.2.0", "@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@rlanz/bull-queue": "^3.1.0", "@vinejs/vine": "^3.0.1", "edge.js": "^6.2.1", "luxon": "^3.7.1", "mysql2": "^3.14.2", "sharp": "^0.33.5", "exceljs": "^4.4.0", "reflect-metadata": "^0.2.2", "socket.io": "^4.8.1"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "prettier": "@adonisjs/prettier-config"}