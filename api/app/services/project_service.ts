import Inquiry from "#models/inquiry";
import Project from "#models/project";
import ProjectGroup from "#models/project_group";
import ProjectRole from "#models/project_role";
import ProjectUser from "#models/project_user";
import Role from "#models/role";
import User from "#models/user";
import UserGroup from "#models/user_group";
import UserInquiry from "#models/user_inquiry";
import UserRole from "#models/user_role";
import { inject } from "@adonisjs/core";

@inject()
export default class ProjectService {
  async checkCollaborator(project_id: number, user: User) {
    // 1. Check if the user is the project owner
    const project = await Project.query().where('id', project_id).firstOrFail();
    if (project.user_id === user.id) return true

    // 2. Check for direct project user access
    const directUserAccess = await ProjectUser.query()
      .where('project_id', project_id)
      .where('user_id', user.id)
      .first()
    if (directUserAccess) return true

    // 3. Check for project roles associated with user's roles
    const userRoles = await UserRole.query().where('user_id', user.id).select('role_id');
    const roleIds = userRoles.map((userRole) => userRole.role_id);

    if (roleIds.length > 0) {
      const hasProjectRole = await ProjectRole.query()
        .whereIn('role_id', roleIds)
        .where('project_id', project_id)
        .first()
      if (!hasProjectRole) return false
    }

    // 4. Check for group-based collaboration
    const projectGroups = await ProjectGroup.query().where('project_id', project_id).select('group_id');
    const groupIds = projectGroups.map((projectGroup) => projectGroup.group_id);

    if (groupIds.length > 0) {
      const userInGroup = await UserGroup.query()
        .where('user_id', user.id)
        .whereIn('group_id', groupIds)
        .first()
      if (userInGroup) return true
    }

    // 5. Check for inquiry-based collaboration
    // Fetch inquiry IDs related to the project first
    const inquiryIds = await Inquiry.query().where('project_id', project_id).select('id');
    const ids = inquiryIds.map((inquiry) => inquiry.id);

    if (ids.length > 0) {
      const userInquiryCollaborator = await UserInquiry.query()
        .where('user_id', user.id)
        .whereIn('inquiry_id', ids)
        .first()
      if (userInquiryCollaborator) return true
    }

    // If none of the above conditions are met, the user is not a collaborator
    return false;
  }

  async addCollaborator(inquiry: Inquiry) {
    // Fetch project owner ID directly if project exists
    const project = await Project.query().where('id', inquiry.project_id).first();
    const projectOwnerId = project?.user_id; // Using optional chaining for conciseness

    // Get IDs of roles with 'all_access'
    const allAccessRoles = await Role.query()
      .where('access_rights', 'like', '%all_access%');
    const allAccessRoleIds = allAccessRoles.map(role => role.id);  

    // Get user IDs associated with these roles
    const userRoles = await UserRole.query()
      .whereIn('role_id', allAccessRoleIds);
    const userIdsFromRoles = userRoles.map(userRole => userRole.user_id);  

    // Combine all user IDs who should be collaborators
    const collaboratorUserIds = new Set<any>(); // Use a Set to automatically handle duplicates

    userIdsFromRoles.forEach(id => collaboratorUserIds.add(id));
    if (projectOwnerId) {
      collaboratorUserIds.add(projectOwnerId);
    }

    // Convert Set to Array for database operations
    const uniqueCollaboratorIds = Array.from(collaboratorUserIds);

    if (uniqueCollaboratorIds.length === 0) {
      return; // No collaborators to add, exit early
    }

    // Prepare data for bulk insertion/creation
    const userInquiryData = uniqueCollaboratorIds.map(userId => ({
      user_id: userId,
      inquiry_id: inquiry.id
    }));

    const projectUserData = uniqueCollaboratorIds.map(userId => ({
      user_id: userId,
      project_id: inquiry.project_id
    }));

    // Perform bulk upsert/insert operations outside the loop
    // This significantly reduces database round trips.
    await UserInquiry.fetchOrCreateMany(['user_id', 'inquiry_id'], userInquiryData);
    await ProjectUser.fetchOrCreateMany(['user_id', 'project_id'], projectUserData);
  }
}
