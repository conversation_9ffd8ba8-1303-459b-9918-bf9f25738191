import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, beforeFetch, beforeFind, beforeSave, belongsTo, column, hasMany, manyToMany } from '@adonisjs/lucid/orm'
import string from '@adonisjs/core/helpers/string'
import db from '@adonisjs/lucid/services/db'
import type { BelongsTo, HasMany, ManyToMany } from '@adonisjs/lucid/types/relations'
import AnswerFlow from './answer_flow.js'
import Group from './group.js'
import Category from './category.js'
import Role from './role.js'
import User from './user.js'
import Attachment from './attachment.js'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

export default class Project extends BaseModel {
  serializeExtras = true

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare user_id: number

  @column()
  declare name: string

  @column()
  declare type: string | null

  @column()
  declare slug: string

  @column()
  declare description: string | null

  @column()
  declare is_archived: boolean

  @column()
  declare brief: string | null

  @column()
  declare status: string | null

  @column()
  declare color: string

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  @beforeCreate()
  @beforeSave()
  static async slugify(project: Project) {
    if (project.slug) return

    // Generate the base slug from the project name
    const baseSlug = string.dashCase(project.name)

    // Fetch all rows that match the base slug or a similar pattern with a postfix
    const rows = await db.from('projects')
        .select('slug')
        .whereRaw('lower(??) = ?', ['slug', baseSlug])
        .orWhereRaw('lower(??) like ?', ['slug', `${baseSlug}-%`])

    // If there are no existing rows, use the base slug
    if (!rows.length) {
        project.slug = baseSlug
        return
    }

    // Extract existing numeric postfixes from the slugs
    const existingSlugs = rows.map(row => row.slug)

    // Find the highest existing postfix number and increment it
    let maxPostfix = 0
    existingSlugs.forEach(slug => {
        const match = slug.match(new RegExp(`${baseSlug}-(\\d+)$`))
        if (match) {
            const postfix = parseInt(match[1], 10)
            if (postfix > maxPostfix) {
                maxPostfix = postfix
            }
        }
    })

    // Set the new slug with an incremented postfix
    project.slug = `${baseSlug}-${maxPostfix + 1}`
  }

  //Relations
  @manyToMany(() => AnswerFlow, {
    pivotTable: 'project_flows',
    pivotForeignKey: 'project_id',
    pivotRelatedForeignKey: 'answer_flow_id',
    pivotTimestamps: true
  })
  declare flows: ManyToMany<typeof AnswerFlow>

  @manyToMany(() => Group, {
    pivotTable: 'project_groups',
    pivotForeignKey: 'project_id',
    pivotRelatedForeignKey: 'group_id',
    pivotTimestamps: true
  })
  declare groups: ManyToMany<typeof Group>

  @manyToMany(() => Category, {
    pivotTable: 'project_categories',
    pivotForeignKey: 'project_id',
    pivotRelatedForeignKey: 'category_id',
    pivotTimestamps: true
  })
  declare categories: ManyToMany<typeof Category>

  @manyToMany(() => Role, {
    pivotTable: 'project_roles',
    pivotForeignKey: 'project_id',
    pivotRelatedForeignKey: 'role_id',
    pivotTimestamps: true
  })
  declare roles: ManyToMany<typeof Role>

  @manyToMany(() => User, {
    pivotTable: 'project_users',
    pivotForeignKey: 'project_id',
    pivotRelatedForeignKey: 'user_id',
    pivotTimestamps: true
  })
  declare user_access: ManyToMany<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'user_id'
  })
  declare user: BelongsTo<typeof User>

  @hasMany(() => Attachment, {
    foreignKey: 'project_id',
  })
  declare attachments: HasMany<typeof Attachment>

  @beforeFetch() 
  @beforeFind() 
  static fetchProject(query: ModelQueryBuilderContract<typeof Project>) { 
    query.preload('attachments', (builder: any) => {
      builder.select(['file_url', 'description', 'name', 'user_id'])
    })
  }
}