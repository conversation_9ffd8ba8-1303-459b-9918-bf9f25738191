import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, beforeFetch, beforeFind, beforeSave, belongsTo, column, hasMany, manyToMany } from '@adonisjs/lucid/orm'
import Project from '#models/project'
import type { BelongsTo, HasMany, ManyToMany } from '@adonisjs/lucid/types/relations'
import Comment from '#models/comment'
import Attachment from '#models/attachment'
import User from '#models/user'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import string from '@adonisjs/core/helpers/string'
import db from '@adonisjs/lucid/services/db'

export default class Inquiry extends BaseModel {
  serializeExtras = true

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare parent_id: number | null

  @column({ serializeAs: null })
  declare project_id: number

  @column({ serializeAs: null })
  declare assign_to: number | null

  @column({ serializeAs: null })
  declare sender_id: number | null

  @column({ serializeAs: null })
  declare submited_by: number | null

  @column({ serializeAs: null })
  declare execution_to: number | null

  @column({ serializeAs: null })
  declare completed_by: number | null

  @column()
  declare title: string

  @column()
  declare slug: string

  @column()
  declare repetition: string | null

  @column()
  declare type: string

  @column()
  declare description: string | null

  @column()
  declare status: string | null

  @column()
  declare notes: string | null

  @column()
  declare notification: string | null

  @column()
  declare conversation_id: string | null

  @column()
  declare index: number

  @column.dateTime({
    autoCreate: false,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare completed_date: DateTime

  @column.dateTime({
    autoCreate: false,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare due_date: DateTime | null

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  //Relations
  @belongsTo(() => Project)
  declare project: BelongsTo<typeof Project>

  @belongsTo(() => User, {
    foreignKey: 'assign_to'
  })
  declare assign: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'sender_id'
  })
  declare sender: BelongsTo<typeof User>
  
  @belongsTo(() => User, {
    foreignKey: 'submited_by'
  })
  declare submited: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'execution_to'
  })
  declare execution: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'completed_by'
  })
  declare completed: BelongsTo<typeof User>

  @hasMany(() => Inquiry, {
    foreignKey: 'parent_id',
  })
  declare sub_inquiries: HasMany<typeof Inquiry>

  @belongsTo(() => Inquiry, {
    foreignKey: 'parent_id',
  })
  declare parent: BelongsTo<typeof Inquiry>

  @hasMany(() => Comment, {
    foreignKey: 'inquiry_id',
  })
  declare comments: HasMany<typeof Comment>

  @manyToMany(() => User, {
    pivotTable: 'user_inquiries',
  })
  declare collaborator: ManyToMany<typeof User>

  @hasMany(() => Attachment, {
    foreignKey: 'inquiry_id',
  })
  declare attachments: HasMany<typeof Attachment>

  @beforeFetch() 
  @beforeFind() 
  static fetchTask(query: ModelQueryBuilderContract<typeof Inquiry>) { 
    query.preload('assign', (builder) => {
      builder.select(['first_name', 'last_name', 'email', 'img_url'])
    })
      .preload('sender', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('execution', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('submited', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('completed', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('collaborator', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .withCount('comments', (builder) => {
        builder.as('totalComments')
      })
      .preload('attachments', (builder) => {
        builder.select(['file_url', 'description', 'name', 'user_id'])
      })
      .preload('project')
      .preload('parent', (builder) => {
        builder.select(['title', 'slug', 'parent_id', 'assign_to', 'execution_to', 'submited_by', 'sender_id', 'completed_by', 'due_date', 'project_id'])
      })
  }

  @beforeCreate()
  @beforeSave()
  static async slugify(inquiry: Inquiry) {
    if (inquiry.slug) return

    // Generate the base slug from the inquiry name
    const baseSlug = string.dashCase(inquiry.title)

    // Fetch all rows that match the base slug or a similar pattern with a postfix
    const rows = await db.from('inquiries')
        .select('slug')
        .whereRaw('lower(??) = ?', ['slug', baseSlug])
        .orWhereRaw('lower(??) like ?', ['slug', `${baseSlug}-%`])

    // If there are no existing rows, use the base slug
    if (!rows.length) {
        inquiry.slug = baseSlug
        return
    }

    // Extract existing numeric postfixes from the slugs
    const existingSlugs = rows.map(row => row.slug)

    // Find the highest existing postfix number and increment it
    let maxPostfix = 0
    existingSlugs.forEach(slug => {
        const match = slug.match(new RegExp(`${baseSlug}-(\\d+)$`))
        if (match) {
            const postfix = parseInt(match[1], 10)
            if (postfix > maxPostfix) {
                maxPostfix = postfix
            }
        }
    })

    // Set the new slug with an incremented postfix
    inquiry.slug = `${baseSlug}-${maxPostfix + 1}`
  }
}