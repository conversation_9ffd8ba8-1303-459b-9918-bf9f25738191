import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column, manyToMany } from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { AccessToken, DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'
import type { ManyToMany } from '@adonisjs/lucid/types/relations'
import Role from './role.js'
// import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Group from './group.js'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare first_name: string

  @column()
  declare last_name: string

  @column()
  declare email: string

  @column()
  declare phone: string | null

  @column()
  declare address: string | null

  @column()
  declare unit: string | null

  @column()
  declare other: string | null

  @column()
  declare short_bio: string | null

  @column()
  declare language: string

  @column()
  declare img_url: string | null

  @column()
  declare is_active: boolean

  @column()
  declare is_display_contact: boolean

  @column({ serializeAs: null })
  declare password: string

  @column.dateTime({
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare loginAt: DateTime

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  static accessTokens = DbAccessTokensProvider.forModel(User)

  currentAccessToken?: AccessToken

  //Relations
  @manyToMany(() => Role, {
    pivotTable: 'user_roles',
    pivotForeignKey: 'user_id',
    pivotRelatedForeignKey: 'role_id',
    pivotTimestamps: true
  })
  declare roles: ManyToMany<typeof Role>

  @manyToMany(() => Group, {
    pivotTable: 'user_groups',
    pivotForeignKey: 'user_id',
    pivotRelatedForeignKey: 'group_id',
    pivotTimestamps: true
  })
  declare groups: ManyToMany<typeof Group>

  // @beforeFetch() 
  // @beforeFind() 
  // static fetchTask(query: ModelQueryBuilderContract<typeof User>) { 
  //   query.preload('roles', (builder) => {
  //     builder.select(['name', 'id'])
  //   })
  //   .preload('groups', (builder) => {
  //     builder.select(['name', 'id'])
  //   })
  // }
}