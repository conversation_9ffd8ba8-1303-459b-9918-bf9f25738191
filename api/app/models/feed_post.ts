import { DateTime } from 'luxon'
import { BaseModel, beforeFetch, beforeFind, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Attachment from './attachment.js'

export default class FeedPost extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare author_id: number

  @column()
  declare isPinned: boolean

  @column()
  declare messages: string | null

  @column()
  declare content: string | null

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  //Relations
  @belongsTo(() => User, {
    foreignKey: 'author_id'
  })
  declare author: BelongsTo<typeof User>

  @hasMany(() => Attachment, {
    foreignKey: 'feed_post_id',
  })
  declare attachments: HasMany<typeof Attachment>

  @beforeFetch() 
  @beforeFind() 
  static fetchTask(query: ModelQueryBuilderContract<typeof FeedPost>) { 
    query.preload('author', (builder) => {
      builder.select(['first_name', 'last_name', 'img_url', 'id'])
    })
    .preload('attachments', (builder) => {
        builder.select(['file_url', 'description', 'name', 'user_id'])
      })
  }
}