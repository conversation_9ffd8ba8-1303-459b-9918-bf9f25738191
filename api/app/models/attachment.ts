import { DateTime } from 'luxon'
import { BaseModel, beforeFetch, beforeFind, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Project from '#models/project'
import FeedPost from './feed_post.js'
import Comment from './comment.js'
import Inquiry from './inquiry.js'

export default class Attachment extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare inquiry_id: number

  @column()
  declare user_id: number

  @column()
  declare project_id: number

  @column()
  declare feed_post_id: number

  @column()
  declare comment_id: number

  @column()
  declare file_url: string

  @column()
  declare name: string

  @column()
  declare description: string

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  // Relations
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Project)
  declare project: BelongsTo<typeof Project>

  @belongsTo(() => FeedPost)
  declare feedPost: BelongsTo<typeof FeedPost>

  @belongsTo(() => Comment)
  declare comments: BelongsTo<typeof Comment>

  @belongsTo(() => Inquiry)
  declare inquiries: BelongsTo<typeof Inquiry>

  @beforeFetch() 
  @beforeFind() 
  static fetchAttcahment(query: ModelQueryBuilderContract<typeof Attachment>) { 
    query.preload('user', (builder: any) => {
      builder.select(['first_name', 'last_name', 'email', 'img_url'])
    })
  }
}