import { DateTime } from 'luxon'
import { BaseModel, beforeFetch, beforeFind, belongsTo, column } from '@adonisjs/lucid/orm'
import User from '#models/user'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Inquiry from './inquiry.js'
import Attachment from './attachment.js'

export default class Comment extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare inquiry_id: number

  @column()
  declare attachment_id: number

  @column()
  declare user_id: number

  @column()
  declare description: string

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  //Relations
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Inquiry)
  declare inquiry: BelongsTo<typeof Inquiry>

  @belongsTo(() => Attachment)
  declare attachment: BelongsTo<typeof Attachment>

  @beforeFetch() 
  @beforeFind() 
  static fetchComment(query: ModelQueryBuilderContract<typeof Comment>) { 
    query.preload('user', (builder) => {
      builder.select(['first_name', 'last_name', 'email', 'img_url'])
    })
    .preload('attachment', (builder) => {
      builder.select(['file_url', 'name', 'description', 'user_id'])
    })
  }
}