import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Category from './category.js'
import Inquiry from './inquiry.js'

export default class CategoryInquiry extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare inquiry_id: number

  @column()
  declare category_id: number

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.setZone('utc').toISO() : value
    },
  })
  declare updated_at: DateTime

  // Relations
  @belongsTo(() => Inquiry, {
    foreignKey: 'inquiry_id'
  })
  declare inquiry: BelongsTo<typeof Inquiry>

  @belongsTo(() => Category, {
    foreignKey: 'category_id'
  })
  declare category: BelongsTo<typeof Category>
}