import app from '@adonisjs/core/services/app'
import { HttpContext, ExceptionHandler } from '@adonisjs/core/http'
import { errors } from '@vinejs/vine';

export default class HttpExceptionHandler extends ExceptionHandler {
  /**
   * In debug mode, the exception handler will display verbose errors
   * with pretty printed stack traces.
   */
  protected debug = !app.inProduction

  /**
   * The method is used for handling errors and returning
   * response to the client
   */
  async handle(error: any, ctx: HttpContext) {
    const { response } = ctx;

    if (error instanceof errors.E_VALIDATION_ERROR) {
      ctx.response.status(422).send(error.messages[0])
      return
    }

    // Handle validation errors
    if (error.code === 'E_VALIDATION_ERROR' && error.messages?.[0]) {
      response.status(422).send({
        message: error.messages[0], // Send the first validation error
      });
      return;
    }
  
    // Handle unauthorized access
    if (error.code === 'E_UNAUTHORIZED_ACCESS') {
      response.status(401).send({
        message: 'Invalid credential',
      });
      return;
    }
  
    // Handle generic errors with messages
    if (error.message) {
      response.status(error.status || 500).send({
        message: error.message,
      });
      return;
    }
    return super.handle(error, ctx)
  }

  /**
   * The method is used to report error to the logging service or
   * the third party error monitoring service.
   *
   * @note You should not attempt to send a response from this method.
   */
  async report(error: unknown, ctx: HttpContext) {
    return super.report(error, ctx)
  }
}
