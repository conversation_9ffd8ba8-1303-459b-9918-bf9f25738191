import db from '@adonisjs/lucid/services/db'
import vine from '@vinejs/vine'
import { FieldContext } from '@vinejs/vine/types'

/**
 * Options accepted by the unique rule
 */
export type Options = {
  table: string,
  column: string
}

/**
 * Implementation
 */
async function unique(
  value: unknown,
  options: Options,
  field: FieldContext
) {
  /**
   * We do not want to deal with non-string
   * values. The "string" rule will handle the
   * the validation.
   */
  if (typeof value !== 'string') {
    return
  }
  
  let row = db.query()
    .select(options.column)
    .from(options.table)
    .where(options.column, value)
  if (field.meta.user_id) row = row.whereNot('id', field.meta.user_id)
  const query = await row.first()

  if (query) {
    field.report(
      'The {{ field }} field is not unique',
      'unique',
      field
    )
  }
}

/**
 * Converting a function to a VineJS rule
 */
export const uniqueRule = vine.createRule(unique)
