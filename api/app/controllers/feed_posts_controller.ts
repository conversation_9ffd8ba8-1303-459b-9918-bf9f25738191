import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import { store, update } from '#validators/feed_post'
import FeedPost from '#models/feed_post'

export default class FeedPostsController {
  /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    try {
      const { order_by, sort_by, limit, page, keyword } = request.all()

      let query = FeedPost.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('messages', 'like', '%' + keyword + '%')
        })
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const feeds = await query
        .paginate(page, limit)

      return response.send(feeds)
    } catch (e) {
      logger.error({ err: e }, 'FeedPostsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    const data = request.all()
    const payload = await store.validate(data)
    try {
      const feed = await FeedPost.create({
        messages: payload.messages,
        content: payload.content,
        author_id: user.id
      })

      const newFeed = await FeedPost.query()
        .where('id', feed.id)
        .first()
      
      return response.send({message: localize(lang, 'Data has been added'), data: newFeed})
    } catch (e) {
      logger.error({ err: e }, 'FeedPostsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const feed = await FeedPost.query()
      .where('id', params.id)
      .first()
    return response.send({data: feed})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await update.validate(data)
    try {
      const feed = await FeedPost.query().where('id', params.id).first()
      if (!feed) return response.status(422).send({message: localize(lang, 'Feed not found')})

      if (payload.messages !== undefined) feed.messages = payload.messages
      if (payload.isPinned !== undefined) feed.isPinned = payload.isPinned
      if (payload.content !== undefined) feed.content = payload.content
      await feed.save()
      
      const updated = await FeedPost.query().where('id', params.id).first()
      return response.send({message: localize(lang, 'Data has been updated'), data: updated})
    } catch (e) {
      logger.error({ err: e }, 'FeedPostsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    try {
      const deleted = await FeedPost.query().where('id', params.id).first()
      if (deleted)  await deleted.delete()
      return response.send({message: localize(lang, 'Data has been deleted'), data: deleted})
    } catch (e) {
      logger.error({ err: e }, 'FeedPostsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}