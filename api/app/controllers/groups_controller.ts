import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import Group from '#models/group'
import { storeGroup, updateGroup } from '#validators/group'

export default class GroupsController {
    /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    try {
      const { 
        order_by, 
        sort_by, 
        limit, 
        page, 
        keyword
      } = request.all()

      let query = Group.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('name', 'like', '%' + keyword + '%')
            .orWhere('description', 'like', '%' + keyword + '%')
        })
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const groups = await query
        .paginate(page, limit)

      return response.send(groups)
    } catch (e) {
      logger.error({ err: e }, 'GroupsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    const data = request.all()
    const payload = await storeGroup.validate(data)
    try {
      const group = await Group.create({
        name: payload.name,
        description: payload.description
      })

      const newGroup = await Group.query()
        .where('id', group.id)
        .first()
      
      return response.send({message: localize(lang, 'Data has been added'), data: newGroup})
    } catch (e) {
      logger.error({ err: e }, 'GroupsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const group = await Group.query()
      .where('id', params.id)
      .first()
    return response.send({data: group})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await updateGroup.validate(data)
    try {
      const group = await Group.query().where('id', params.id).first()
      if (!group) return response.status(422).send({message: localize(lang, 'Group not found')})

      if (payload.name) group.name = payload.name
      if (payload.description !== undefined) group.description = payload.description
      await group.save()
      
      const updated = await Group.query().where('id', params.id).first()
      return response.send({message: localize(lang, 'Data has been updated'), data: updated})
    } catch (e) {
      logger.error({ err: e }, 'GroupsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    try {
      const deleted = await Group.query().where('id', params.id).first()
      if (deleted)  await deleted.delete()
      return response.send({message: localize(lang, 'Data has been deleted'), data: deleted})
    } catch (e) {
      logger.error({ err: e }, 'GroupsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}