import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import AnswerFlow from '#models/answer_flow'
import { store, update } from '#validators/answer_flow'

export default class AnswerFlowsController {
  /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    try {
      const { order_by, sort_by, limit, page, keyword } = request.all()

      let query = AnswerFlow.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('name', 'like', '%' + keyword + '%')
            .orWhere('description', 'like', '%' + keyword + '%')
        })
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const answerFlows = await query
        .paginate(page, limit)

      return response.send(answerFlows)
    } catch (e) {
      logger.error({ err: e }, 'AnswerFlowsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    const data = request.all()
    const payload = await store.validate(data)
    try {
      const answerFlow = await AnswerFlow.create({
        name: payload.name,
        description: payload.description,
        content: payload.content,
        color: payload.color
      })

      const newFlow = await AnswerFlow.query()
        .where('id', answerFlow.id)
        .first()
      
      return response.send({message: localize(lang, 'Data has been added'), data: newFlow})
    } catch (e) {
      logger.error({ err: e }, 'AnswerFlowsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const answerFlow = await AnswerFlow.query()
      .where('id', params.id)
      .first()
    return response.send({data: answerFlow})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await update.validate(data)
    try {
      const answerFlow = await AnswerFlow.query().where('id', params.id).first()
      if (!answerFlow) return response.status(422).send({message: localize(lang, 'Answer flow not found')})

      if (payload.name) answerFlow.name = payload.name
      if (payload.description !== undefined) answerFlow.description = payload.description
      if (payload.content !== undefined) answerFlow.content = payload.content
      if (payload.color !== undefined) answerFlow.color = payload.color
      await answerFlow.save()
      
      const updated = await AnswerFlow.query().where('id', params.id).first()
      return response.send({message: localize(lang, 'Data has been updated'), data: updated})
    } catch (e) {
      logger.error({ err: e }, 'AnswerFlowsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    try {
      const deleted = await AnswerFlow.query().where('id', params.id).first()
      if (deleted)  await deleted.delete()
      return response.send({message: localize(lang, 'Data has been deleted'), data: deleted})
    } catch (e) {
      logger.error({ err: e }, 'AnswerFlowsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}