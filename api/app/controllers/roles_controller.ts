import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import Role from '#models/role'
import { storeRole, updateRole } from '#validators/role'
import UserRole from '#models/user_role'

export default class RolesController {
  /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    try {
      const { order_by, sort_by, limit, page, keyword } = request.all()

      let query = Role.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('name', 'like', '%' + keyword + '%')
            .orWhere('description', 'like', '%' + keyword + '%')
        })
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const roles = await query
        .paginate(page, limit)

      return response.send(roles)
    } catch (e) {
      logger.error({ err: e }, 'RolesController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    const data = request.all()
    const payload = await storeRole.validate(data)
    try {
      const isExist = await Role.query().where('name', payload.name).first()
      if (isExist) return response.status(422).send({message: localize(lang, 'Role already exist')})
      if (!payload.access_rights) payload.access_rights = 'member'

      const role = await Role.create({
        access_rights: payload.access_rights,
        name: payload.name,
        description: payload.description??null
      })

      const newRoles = await Role.query().where('id', role.id).first()
      
      return response.send({message: localize(lang, 'Data has been added'), data: newRoles})
    } catch (e) {
      logger.error({ err: e }, 'RolesController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const role = await Role.query().where('id', params.id).first()
    return response.send({data: role})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await updateRole.validate(data)
    try {
      const role = await Role.query().where('id', params.id).firstOrFail()
      if (payload.name && role.name.toLocaleLowerCase() !== payload.name.toLocaleLowerCase()) {
        const isExist = await Role.query().whereNot('id', role.id).where('name', payload.name).first()
        if (isExist) return response.status(422).send({message: localize(lang, 'Role already exist')})
      }

      await Role.query().where('id', params.id).update(payload)
      const updated = await Role.query().where('id', params.id).first()
      return response.send({message: localize(lang, 'Data has been updated'), data: updated})
    } catch (e) {
      logger.error({ err: e }, 'RolesController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    try {
      const updated = await Role.query().where('id', params.id).first()
      if (updated) {
        const isUsed = await UserRole.query().where('role_id', updated.id).first()
        if (isUsed) return response.status(422).send({message: localize(lang, 'Cannot delete, role is in use by user')})
        await updated.delete()
      }
      return response.send({message: localize(lang, 'Data has been deleted'), data: updated})
    } catch (e) {
      logger.error({ err: e }, 'RolesController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}