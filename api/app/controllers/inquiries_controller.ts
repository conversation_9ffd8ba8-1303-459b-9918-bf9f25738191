import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import { DateTime } from 'luxon'
import { store, update } from '#validators/inquiry'
import db from '@adonisjs/lucid/services/db'
import { io } from '#start/ws'
import Inquiry from '#models/inquiry'
import UserInquiry from '#models/user_inquiry'
import ProjectService from '#services/project_service'
const projectService = new ProjectService()

export default class InquiriesController {
  /**
   * Display a list of resource
   */
  async index({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({ message: localize(lang, 'Invalid credentials') })
    try {
      const {
        order_by,
        sort_by,
        limit,
        page,
        keyword,
        type,
        status,
        project_id,
        is_collaborator,
        is_upcoming,
        is_overDue,
        is_my_inquiry,
        is_due_this_week,
        is_due_next_week,
      } = request.all()

      const checkCollaborator = await projectService.checkCollaborator(project_id, user)
      if (!checkCollaborator) return response.status(403).send({message: localize(lang, 'Permission Denied: You are not authorized to access this.')})

      let query = Inquiry.query().whereNull('parent_id')

      if (project_id) {
        query = query.where('project_id', project_id)
      } else {
        if (is_collaborator) {
          const collaborator = await UserInquiry.query().where('user_id', user.id)
          const inquiry_ids = collaborator.map((collab) => collab.inquiry_id)
          query = query.whereIn('id', inquiry_ids)
        } else {
          query = query.where('assign_to', user.id)
        }
      }
      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('title', 'like', '%' + keyword + '%')
            .orWhere('description', 'like', '%' + keyword + '%')
        })
      }
      if (is_my_inquiry) query = query.where('assign_to', user.id)
      if (status) query = query.where('status', status)
      if (type) query = query.where('type', type)
      const now = DateTime.now()
      const dateNow = now.toISODate()
      if (is_overDue) query = query.where('due_date', '<', dateNow)
      if (is_upcoming) {
        query = query.where((builder) => {
          builder.where('start_date', '>', dateNow)
            .orWhere('due_date', '>', dateNow)
            .orWhereNull('due_date')
        })
      }

      if (is_due_this_week && !is_due_next_week) {
        const firstDayOfWeek = now.startOf('week').toISODate()
        const lastDayOfWeek = now.endOf('week').toISODate()
        query = query.whereBetween('due_date', [firstDayOfWeek, lastDayOfWeek])
      }

      if (is_due_next_week && !is_due_this_week) {
        const nextWeek = now.plus({ weeks: 1 })
        const firstDayOfWeek = nextWeek.startOf('week').toISODate()
        const lastDayOfWeek = nextWeek.endOf('week').toISODate()
        query = query.whereBetween('due_date', [firstDayOfWeek, lastDayOfWeek])
      }
      if (is_due_this_week && is_due_next_week) {
        const firstDayOfWeek = now.startOf('week').toISODate()
        const nextWeek = now.plus({ weeks: 1 })
        const lastDayOfWeek = nextWeek.endOf('week').toISODate()
        query = query.whereBetween('due_date', [firstDayOfWeek, lastDayOfWeek])
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const inquiry = await query
        .paginate(page, limit)

      return response.send(inquiry)
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.index')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credentials') })
    const payload = await store.validate(data)
    const due_date = request.input('due_date')
      ? DateTime.fromISO(request.input('due_date'))
      : undefined
    const completed_date = request.input('completed_date')
      ? DateTime.fromISO(request.input('completed_date'))
      : undefined
    try {
      const totalIndex = await db
        .from('inquiries')
        .where('project_id', payload.project_id)
        .where('type', payload.type)
        .count('* as total')

      const index = totalIndex[0].total + 1

      const inquiry = await Inquiry.create({
        title: payload.title,
        description: payload.description,
        type: payload.type,
        status: payload.status ? payload.status : 'incompleted',
        project_id: payload.project_id,
        parent_id: payload.parent_id,
        notes: payload.notes,
        notification: payload.notification,
        due_date: due_date,
        completed_date: completed_date,
        assign_to: payload.assign_to,
        conversation_id: payload.conversation_id,
        execution_to: payload.execution_to,
        submited_by: payload.submited_by,
        index: index,
        sender_id: auth.user?.id
      })

      let collaboratorIds = request.input('collaborator_ids')
      if (collaboratorIds) {
        collaboratorIds =
          typeof collaboratorIds === 'string' ? JSON.parse(collaboratorIds) : collaboratorIds
        await inquiry.related('collaborator').attach(collaboratorIds)
      }

      await projectService.addCollaborator(inquiry)

      const newInquiry = await Inquiry.query()
        .where('id', inquiry.id)
        .preload('collaborator', (builder) => {
          builder.select(['first_name', 'last_name', 'email', 'img_url'])
        })
        .first()

      const roomId = `${newInquiry?.project.slug}`
      const channel = 'inquiry_add'
      io?.to(roomId).emit(channel, newInquiry)

      return response.send({ message: localize(lang, 'Data has been added'), data: newInquiry })
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.store')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const decode = decodeURIComponent(params.id)
    const inquiry = await Inquiry.query()
      .where('id', decode)
      .orWhere('slug', decode)
      .preload('sub_inquiries')
      .preload('comments')
      .preload('attachments')
      .preload('collaborator', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('assign', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('sender', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('completed', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .first()
    return response.send({ data: inquiry })
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credentials') })
    const payload = await update.validate(data)

    const due_date = request.input('due_date') ? DateTime.fromISO(request.input('due_date')) : null
    const completed_date = request.input('completed_date')
      ? DateTime.fromISO(request.input('completed_date'))
      : null
    try {
      const inquiry = await Inquiry.query().where('id', params.id).first()
      if (!inquiry) return response.status(404).send({ message: localize(lang, 'Inquiry not found') })

      if (payload.title) inquiry.title = payload.title
      if (payload.description !== undefined) inquiry.description = payload.description
      if (payload.notes !== undefined) inquiry.notes = payload.notes
      if (payload.notification !== undefined) inquiry.notification = payload.notification
      if (payload.repetition !== undefined) inquiry.repetition = payload.repetition
      if (payload.type) inquiry.type = payload.type
      if (payload.status) inquiry.status = payload.status
      if (payload.project_id) inquiry.project_id = payload.project_id
      if (payload.parent_id !== undefined) inquiry.parent_id = payload.parent_id
      if (payload.sender_id !== undefined) inquiry.sender_id = payload.sender_id
      if (payload.submited_by !== undefined) inquiry.submited_by = payload.submited_by
      if (payload.execution_to !== undefined) inquiry.execution_to = payload.execution_to
      if (payload.completed_by !== undefined) inquiry.completed_by = payload.completed_by
      if (payload.conversation_id !== undefined) inquiry.conversation_id = payload.conversation_id
      inquiry.due_date = due_date
      if (completed_date) inquiry.completed_date = completed_date
      await inquiry.save()

      let collaboratorIds = request.input('collaboratorIds')
      if (collaboratorIds) {
        collaboratorIds =
          typeof collaboratorIds === 'string' ? JSON.parse(collaboratorIds) : collaboratorIds
        await inquiry?.related('collaborator').detach()
        await inquiry?.related('collaborator').attach(collaboratorIds)
      }

      const updatedInquiry = await Inquiry.query()
        .where('id', params.id)
        .preload('sub_inquiries')
        .preload('comments')
        .preload('attachments')
        .preload('collaborator', (builder) => {
          builder.select(['first_name', 'last_name', 'email', 'img_url'])
        })
        .first()

      const roomId = `${updatedInquiry?.project.slug}`
      const channel = 'inquiry_update'
      io?.to(roomId).emit(channel, updatedInquiry)
      return response.send({ message: 'Data has been updated', data: updatedInquiry })
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.update')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({ message: localize(lang, 'Invalid credentials') })
    let isHaveAccess = false
    try {
      const updated = await Inquiry.query().where('id', params.id).preload('project').first()
      if (updated) {
        if (user.id === updated.project.user_id) isHaveAccess = true
        if (!isHaveAccess)  return response.status(403).send({message: localize(lang, 'Permission Denied: You are not authorized to access this.')})

        await updated.delete()
      }

      const roomId = `${updated?.project.slug}`
      const channel = 'inquiry_delete'
      io?.to(roomId).emit(channel, updated)
      return response.send({ message: 'Data has been deleted', data: updated })
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.destroy')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  async reorder({ request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const {inquiry_ids, type, inquiry_id} = request.all()
      if (!inquiry_ids) return response.status(422).send({ message: localize(lang, 'Inquiries ids not found') })
      
      const inquiry = await Inquiry.query().where('id', inquiry_id).preload('project').firstOrFail()

      const arrInquiries = typeof inquiry_ids === 'string' ? JSON.parse(inquiry_ids) : inquiry_ids

      for (const [i, element] of arrInquiries.entries()) {
        await Inquiry.query()
          .where('id', element)
          .update({ type: type, index: i + 1 })
      }

      const all = await Inquiry.query()
        .where('project_id', inquiry.project_id)
        .orderBy('index', 'asc')

      const updated = await Inquiry.query()
        .whereIn('id', arrInquiries)
        .orderBy('index', 'asc')

      const data = {
        new_inquiries: updated,
        old_inquiries: inquiry
      }

      const data2 = {
        new_inquiries: all,
        old_inquiries: inquiry
      }

      const roomId = `${inquiry.project.slug}`
      const channel = 'inquiry_reorder'
      io?.to(roomId).emit(channel, data)

      const roomId2 = `${inquiry.project.slug}`
      const channel2 = 'inquiry_reorder_all'
      io?.to(roomId2).emit(channel2, data2)

      return response.send({ message: 'Data has been reorder', data: updated })
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.reorder')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  async assign({ request, response, params, auth }: HttpContext) {
    const lang = getLang(request)
    const user_id = request.input('assign_to')
    const user = auth.user
    if (!user) return response.status(401).send({ message: localize(lang, 'Invalid credentials') })
    let isHaveAccess = false
    try {
      const inquiry = await Inquiry.query().where('id', params.id).preload('project').first()
      if (!inquiry) return response.status(404).send({ message: localize(lang, 'Inquiry not found') })
      if (user.id === inquiry.project.user_id) isHaveAccess = true
      if (!isHaveAccess)  return response.status(422).send({message: localize(lang, 'Permission Denied: You are not authorized to access this.')})

      if (user_id) {
        inquiry.assign_to = user_id
      } else {
        inquiry.assign_to = null
      }
      await inquiry.save()

      if (user_id) {
        let collaborator = await UserInquiry.query()
          .where('inquiry_id', inquiry.id)
          .where('user_id', user_id)
          .first()

          if (!collaborator) {
            collaborator = new UserInquiry()
            collaborator.user_id = user_id
            collaborator.inquiry_id = inquiry.id
            await collaborator.save()
          }
      }

      const updatedInquiry = await Inquiry.query()
        .where('id', params.id)
        .preload('sub_inquiries')
        .preload('comments')
        .preload('attachments')
        .preload('collaborator', (builder) => {
          builder.select(['first_name', 'last_name', 'email', 'img_url'])
        })
        .firstOrFail()

      const roomId = `${updatedInquiry.project.slug}`
      const channel = 'inquiry_update'
      io?.to(roomId).emit(channel, updatedInquiry)

      return response.send({ message: 'Data has been updated', data: updatedInquiry })
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.assign')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  async setCompleted({ request, response, params, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({ message: localize(lang, 'Invalid credentials') })
    try {
      const dateNow = DateTime.now().toFormat('yyyy-MM-dd HH:mm')
      const inquiry = await Inquiry.query().where('id', params.id).firstOrFail()
      if (inquiry.status === 'completed') {
        await Inquiry.query().where('id', params.id).update({
          status: 'incompleted',
          completed_date: null,
          completed_by: null
        })
      } else {
        await Inquiry.query().where('id', params.id).update({
          status: 'completed',
          completed_date: dateNow,
          completed_by: user.id
        })
      }

      const updatedInquiry = await Inquiry.query()
        .where('id', params.id)
        .preload('sub_inquiries')
        .preload('comments')
        .preload('attachments')
        .preload('collaborator', (builder) => {
          builder.select(['first_name', 'last_name', 'email', 'img_url'])
        })
        .first()

      const roomId = `${updatedInquiry?.project.slug}`
      const channel = 'inquiry_update'
      io?.to(roomId).emit(channel, updatedInquiry)
      return response.send({ message: 'Data has been updated', data: updatedInquiry })
    } catch (e) {
      logger.error({ err: e }, 'ProjectsController.getStarred')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  async calendar({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credentials')})
    try {
      const { month, years, weeks, day, project_id } = request.all()

      let query = Inquiry.query()

      // query = query.where('assign_to', user.id)

      if (project_id) query = query.where('project_id', project_id)

      const now = DateTime.now()
      let start:any = now.startOf('day').toISODate()
      let end:any = now.endOf('day').toISODate()

      if (day) {
        const newDate = new Date(day)
        const dateTime = DateTime.fromJSDate(newDate)
        start = dateTime.startOf('day').toISODate()
        end = dateTime.endOf('day').plus({days: 1}).toISODate()
      }

      if (years && month) {
        const dateTime = DateTime.fromObject({
          year: years,
          month: month
        });

        start = dateTime.startOf('month').toISODate()
        end = dateTime.endOf('month').plus({days: 1}).toISODate()
      }

      if (years && weeks) {
        const dateTime = DateTime.fromObject({
          weekYear: years,
          weekNumber: weeks
        });

        start = dateTime.startOf('week').toISODate()
        end = dateTime.endOf('week').plus({days: 1}).toISODate()
      }

      console.log(start)
      console.log(end)

      query = query.where((builder) => {
        builder.where((subQuery) => {
          subQuery
            .whereNotNull('due_date')
            .where('due_date', '>', start)
            .andWhere('due_date', '<', end);
        })
        .orWhere((subQuery) => {
          subQuery
            .whereNull('due_date')
            .where('created_at', '>', start)
            .andWhere('created_at', '<', end);
        });
      });

      const inquiries = await query
        .preload('project')
        .orderByRaw('COALESCE(created_at, due_date) ASC')

      return response.send(inquiries)
    } catch (e) {
      logger.error({ err: e }, 'InquiriesController.calendar')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}