import User from '#models/user'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { update } from '#validators/auth'

export default class AuthController {
  async login({ request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const { email, password } = request.all()
      const user = await User.verifyCredentials(email, password)
      if (!user)
        return response.status(422).send({ message: localize(lang, 'Invalid credentials') })
      user.loginAt = DateTime.now()
      await user.save()

      const token = await User.accessTokens.create(user, ['*'], {
        expiresIn: '30 days',
      })

      return response.send({ data: token })
    } catch (e) {
      logger.error('AuthController.login', e)
      return response.status(422).send({ message: localize(lang, 'Invalid credentials') })
    }
  }

  async profile({ auth, response }: HttpContext) {
    const user = auth.getUserOrFail()
    const newUser = await User.query()
      .where('id', user.id)
      .preload('roles', (builder) => {
        builder.select(['name', 'id'])
      })
      .preload('groups', (builder) => {
        builder.select(['name', 'id'])
      })
      .first()
    return response.send({ data: newUser })
  }

  async logout({ auth, response, request }: HttpContext) {
    const lang = getLang(request)
    const user = auth.getUserOrFail()
    const token = user.currentAccessToken
    await User.accessTokens.delete(user, token.identifier)
    return response.send({ message: localize(lang, 'Logout success') })
  }

  async changePassword({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.getUserOrFail()
    const { old_password, new_password, confirm_password } = request.all()
    try {
      const isPasswordConfirmed = await hash.verify(user.password, old_password)
      if (!isPasswordConfirmed) {
        return response.status(422).send({ message: localize(lang, 'Your old password is wrong') })
      }

      // check consistency password
      if (new_password !== confirm_password) {
        return response
          .status(422)
          .send({ message: localize(lang, 'Password and confirmation password not same') })
      }

      user.password = new_password
      await user.save()

      return response.send({ message: localize(lang, 'Your password has been changed') })
    } catch (e) {
      logger.error({ err: e }, 'AuthController.changePassword')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }

  async update({ request, auth, response }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const currentUser = auth.user
    if (!currentUser) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const payload = await update.validate(data, {
      meta: {
        user_id: currentUser.id,
      },
    })

    try {
      await User.query().where('id', currentUser.id).update(payload)
      let user = await User.query()
        .where('id', currentUser.id)
        .preload('roles', (builder) => {
          builder.select(['name', 'id'])
        })
        .preload('groups', (builder) => {
          builder.select(['name', 'id'])
        })
        .first()

      return response.send({ message: localize(lang, 'Your profile has been updated'), data: user })
    } catch (e) {
      logger.error('AuthController.update', e)
      return response
        .status(422)
        .send({ message: localize(lang, 'You can not update your profile') })
    }
  }
}