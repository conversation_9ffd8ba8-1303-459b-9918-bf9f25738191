import type { HttpContext } from '@adonisjs/core/http'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import logger from '@adonisjs/core/services/logger'
import drive from '@adonisjs/drive/services/main'
import { parse } from 'node:path'

export default class UploadsController {
  async upload({request, response}: HttpContext) {
    const lang = getLang(request)
    try {
      const fileUpload = request.file('file')
      const folderName = request.input('folder')

      if (!fileUpload) return response.status(422).send({ message: localize(lang, 'File not found') })
      if (!fileUpload.isValid) {
        return response.status(413).send({ message: localize(lang, 'File size not allowed') })
      }

      // get file information
      const type = fileUpload.type

      // create folder
      let folderType = type + 's'
      // create hierarki folder
      if (folderName) {
        folderType = folderName + '/' + folderType
      }

      let name = parse(fileUpload.clientName).name
      name = name.replace(/\s+/g, '-').toLowerCase()
      name = name.replace(/[^a-zA-Z0-9\-_.]/g, '')
      const fileName = `${name}-${new Date().getTime()}.${fileUpload.extname}`
      const key = folderType + '/' + fileName

      await fileUpload.moveToDisk(key)

      const url = await drive.use('s3').getUrl(key)

      return response.send({data: url})
    } catch (e) {
      logger.error({ err: e }, 'UploadsController.upload')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}