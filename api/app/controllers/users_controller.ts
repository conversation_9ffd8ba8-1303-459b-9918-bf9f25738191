import type { HttpContext } from '@adonisjs/core/http'
import { getLang } from '../helpers/language.js'
import { localize } from '../helpers/localization.js'
import User from '#models/user'
import logger from '@adonisjs/core/services/logger'
import { storeUser, updateUser } from '#validators/user'
import ProjectUser from '#models/project_user'
import UserInquiry from '#models/user_inquiry'

export default class UsersController {
    /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    try {
      const { 
        order_by,
        sort_by,
        limit,
        page,
        keyword,
        is_active,
        project_id,
        exclude_project_id,
        inquiry_id,
        exclude_inquiry_id
      } = request.all()

      let query = User.query()

      if (project_id || exclude_project_id) {
        const projectId = project_id || exclude_project_id;
        const projectUsers = await ProjectUser.query().where('project_id', projectId);
        const userIds = projectUsers.map((userAccess) => userAccess.user_id);

        const conditionMethod = project_id ? 'whereIn' : 'whereNotIn';
        query = query[conditionMethod]('id', userIds);
      }

      if (inquiry_id || exclude_inquiry_id) {
        const projectId = inquiry_id || exclude_inquiry_id;
        const userInquiries = await UserInquiry.query().where('inquiry_id', projectId);
        const userIds = userInquiries.map((userAccess) => userAccess.user_id);

        const conditionMethod = inquiry_id ? 'whereIn' : 'whereNotIn';
        query = query[conditionMethod]('id', userIds);
      }

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('first_name', 'like', '%' + keyword + '%')
            .orWhere('last_name', 'like', '%' + keyword + '%')
            .orWhere('email', 'like', '%' + keyword + '%')
            .orWhere('phone', 'like', '%' + keyword + '%')
            .orWhere('unit', 'like', '%' + keyword + '%')
            .orWhere('address', 'like', '%' + keyword + '%')
        })
      }

      if (is_active !== undefined)  query = query.where('is_active', is_active)

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const users = await query
        .preload('roles', (builder) => {
          builder.select(['name', 'id'])
        })
        .preload('groups', (builder) => {
          builder.select(['name', 'id'])
        })
        .paginate(page, limit)

      return response.send(users)
    } catch (e) {
      logger.error({ err: e }, 'UsersController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await storeUser.validate(data)
    try {
      if (payload.password !== payload.password_confirmation) {
        return response.status(422).send({message: localize(lang, 'Password and Confirmation Password are not same')})
      }
      const users = await User.create({
        first_name: payload.first_name,
        last_name: payload.last_name,
        email: payload.email,
        password: payload.password,
        phone: payload.phone,
        address: payload.address,
        is_display_contact: payload.is_display_contact??true,
        unit: payload.unit,
        language: payload.language ?? 'no',
        other: payload.other ?? null,
        is_active: true,
        img_url: payload.img_url ?? null,
        short_bio: payload.short_bio,
      })

      if (payload.role_ids) {
        const role_ids = typeof payload.role_ids === 'string' ? JSON.parse(payload.role_ids) : payload.role_ids
        if (role_ids.length > 0){
          await users.related('roles').attach(role_ids)
        }
      }

      if (payload.group_ids) {
        const group_ids = typeof payload.group_ids === 'string' ? JSON.parse(payload.group_ids) : payload.group_ids
        if (group_ids.length > 0){
          await users.related('groups').attach(group_ids)
        }
      }

      const savedUser = await User.query()
        .where('id', users.id)
        .preload('roles', (builder) => {
          builder.select(['name', 'id'])
        })
        .preload('groups', (builder) => {
          builder.select(['name', 'id'])
        })
        .firstOrFail()

      return response.send({message: localize(lang, 'Data has been added'), data: savedUser})
    } catch (e) {
      logger.error({ err: e }, 'UsersController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const user = await User.query()
        .where('id', params.id)
        .preload('roles', (builder) => {
          builder.select(['name', 'id'])
        })
        .preload('groups', (builder) => {
          builder.select(['name', 'id'])
        })
        .first()

      return response.send({data: user})
    } catch (e) {
      logger.error({ err: e }, 'UsersController.show')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await updateUser.validate(data, {
      meta: {
        user_id: params.id
      }
    })

    try {
      const users = await User.query().where('id', params.id).first()
      if (users) {
        if (payload.password && payload.password !== payload.password_confirmation) {
          return response.status(422).send({message: localize(lang, 'Password and Confirmation Password are not same')})
        }

        if (payload.first_name) users.first_name = payload.first_name
        if (payload.last_name) users.last_name = payload.last_name
        if (payload.email) users.email = payload.email
        if (payload.password) users.password = payload.password
        if (payload.phone) users.phone = payload.phone
        if (payload.address !== undefined) users.address = payload.address
        if (payload.is_display_contact !== undefined) users.is_display_contact = payload.is_display_contact
        if (payload.unit !== undefined) users.unit = payload.unit
        if (payload.language) users.language = payload.language
        if (payload.other !== undefined) users.other = payload.other
        if (payload.is_active !== undefined) users.is_active = payload.is_active
        if (payload.img_url !== undefined) users.img_url = payload.img_url
        if (payload.short_bio !== undefined) users.short_bio = payload.short_bio
        await users.save()

        if (payload.role_ids) {
          const role_ids = typeof payload.role_ids === 'string' ? JSON.parse(payload.role_ids) : payload.role_ids
          await users.related('roles').detach()
          await users.related('roles').attach(role_ids)
        }

        if (payload.group_ids) {
          const group_ids = typeof payload.group_ids === 'string' ? JSON.parse(payload.group_ids) : payload.group_ids
          await users.related('groups').detach()
          await users.related('groups').attach(group_ids)
        }
      }

      const updatedUser = await User.query()
        .where('id', params.id)
        .preload('roles', (builder) => {
          builder.select(['name', 'id'])
        })
        .preload('groups', (builder) => {
          builder.select(['name', 'id'])
        })
        .first()

      return response.send({message: localize(lang, 'Data has been updated'), data: updatedUser})
    } catch (e) {
      logger.error({ err: e }, 'UsersController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, response, request, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const findUser = await User.query().where('id', params.id).first()
    if (findUser) await findUser.delete()
    
    return response.send({message: localize(lang, 'Data has been deleted'), data: findUser})
  }
}