import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import Attachment from '#models/attachment'
import { store, update } from '#validators/attachment'
import { io } from '#start/ws'
import { basename } from 'node:path'
import Comment from '#models/comment'
import drive from '@adonisjs/drive/services/main'
import env from '#start/env'
import Inquiry from '#models/inquiry'

export default class AttachmentsController {
  /**
   * Display a list of resource
   */
  async index({request, response}: HttpContext) {
    const lang = getLang(request)
    try {
      const { order_by, sort_by, limit, page, keyword, inquiry_id, project_id, comment_id, feed_post_id } = request.all()

      let query = Attachment.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('file_url', 'like', '%' + keyword + '%')
            .where('description', 'like', '%' + keyword + '%')
        })
      }

      if (inquiry_id) query = query.where('inquiry_id', inquiry_id)
      if (comment_id) query = query.where('comment_id', comment_id)
      if (project_id) query = query.where('project_id', project_id)
      if (feed_post_id) query = query.where('feed_post_id', feed_post_id)

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const attachment = await query
        .preload('inquiries')
        .preload('comments')
        .preload('feedPost')
        .preload('project')
        .paginate(page, limit)

      return response.send(attachment)
    } catch (e) {
      logger.error({ err: e }, 'AttachmentsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const payload = await store.validate(data)

    try {
      let project_id = payload.project_id
      if (!project_id && payload.inquiry_id) {
        const task = await Inquiry.query().where('id', payload.inquiry_id).firstOrFail()
        project_id = task.project_id
      }
      const attachment = await Attachment.create({
        inquiry_id: payload.inquiry_id,
        user_id: auth.user?.id,
        file_url: payload.file_url,
        project_id: project_id,
        description: payload.description,
        comment_id: payload.comment_id,
        feed_post_id: payload.feed_post_id,
        name: payload.name ? payload.name : basename(payload.file_url)
      })

      const newAttachment = await Attachment.query()
      .where('id', attachment.id)
      .preload('inquiries')
      .preload('project')
      .preload('comments')
      .preload('feedPost')
      .first()

      if (newAttachment && newAttachment.project) {
        const roomId = `${newAttachment?.project.slug}`
        const channel = 'attachment_add'
        io?.to(roomId).emit(channel, newAttachment)
      }

      return response.send({message: localize(lang, 'Data has been added'), data: newAttachment})
    } catch (e) {
      logger.error({ err: e }, 'AttachmentsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const attachment = await Attachment.query()
      .where('id', params.id)
      .preload('inquiries')
      .preload('comments')
      .preload('feedPost')
      .preload('project')
      .first()
    return response.send({data: attachment})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const payload = await update.validate(data)
    try {
      await Attachment.query().where('id', params.id).update(payload)
      const updated = await Attachment.query()
        .where('id', params.id)
        .preload('comments')
        .preload('feedPost')
        .preload('inquiries')
        .preload('project')
        .first()

      const roomId = `${updated?.project.slug}`
      const channel = 'attachment_update'
      io?.to(roomId).emit(channel, updated)
      return response.send({message: 'Data has been updated', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'AttachmentsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const updated = await Attachment.query()
        .where('id', params.id)
        .preload('comments')
        .preload('feedPost')
        .preload('inquiries')
        .preload('project')
        .first()

      if (updated) {
        await updated.delete()
        await Comment.query().where('description', updated.file_url).delete()
        const isAws = updated.file_url.includes('https://desidia.s3.amazonaws.com/')
        let key = updated.file_url
        if (!isAws) {
          key = updated.file_url.split(`${env.get('MINIO_BUCKET')}/`)[1]
          await drive.use('s3').delete(key)
        }
      }

      const roomId = `${updated?.project.slug}`
      const channel = 'attachment_delete'
      io?.to(roomId).emit(channel, updated)
      return response.send({message: 'Data has been deleted', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'AttachmentsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}