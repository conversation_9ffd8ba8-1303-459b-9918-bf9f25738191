import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import Project from '#models/project'
import { storeProjectValidator, updateProjectValidator } from '#validators/project'
import { io } from '#start/ws'
import { randColor } from '../helpers/random_color.js'
import ProjectCategory from '#models/project_category'
import ProjectRole from '#models/project_role'
import ProjectFlow from '#models/project_flow'
import ProjectGroup from '#models/project_group'
import ProjectUser from '#models/project_user'
import KanbanColumn from '#models/kanban_column'

export default class ProjectsController {
  /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const user = auth.user
    const lang = getLang(request)
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credentials') })
    try {
      const { 
        order_by, 
        sort_by, 
        limit, 
        page, 
        keyword, 
        status,
        is_archived
      } = request.all()

      let query = Project.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
          .where('name', 'like', '%' + keyword + '%') // Search by project name
        })
      }

      if (status) {
        const arrStatus = typeof status === 'string' ? status.split(",") : status
        query = query.whereIn('status', arrStatus)
      }

      if (is_archived) {
        query = query.where('is_archived', is_archived)
      } else {
        query = query.where('is_archived', 0)
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const project = await query
        .preload('user', (builder) => {
          builder.select(['first_name', 'last_name', 'email','img_url'])
        })
        .preload('categories')
        .preload('roles', (builder) => {
          builder.select(['id', 'name'])
        })
        .preload('flows')
        .preload('groups')
        .paginate(page, limit)

      return response.send(project)
    } catch (e) {
      logger.error({ err: e }, 'ProjectsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const payload = await storeProjectValidator.validate(data)
    try {
      const project = await Project.create({
        name: payload.name,
        description: payload.description,
        status: payload.status ?? null,
        user_id: auth.user?.id,
        brief: payload.brief,
        type: payload.type ?? null,
        color: randColor(),
      })

      if (payload.role_ids) {
        const role_ids = typeof payload.role_ids === 'string' ? JSON.parse(payload.role_ids) : payload.role_ids
        if (role_ids.length) {
          const data = role_ids.map((id: string) => ({
            role_id: id,
            project_id: project.id,
          }))

          await ProjectRole.fetchOrCreateMany(['role_id', 'project_id'], data)
        }
      }

      if (payload.user_access_ids) {
        const user_access_ids = typeof payload.user_access_ids === 'string' ? JSON.parse(payload.user_access_ids) : payload.user_access_ids
        if (user_access_ids.length) {
          const data = user_access_ids.map((id: string) => ({
            user_id: id,
            project_id: project.id,
          }))

          await ProjectUser.fetchOrCreateMany(['user_id', 'project_id'], data)
        }
      }

      if (payload.category_ids) {
        const category_ids = typeof payload.category_ids === 'string' ? JSON.parse(payload.category_ids) : payload.category_ids
        if (category_ids.length) {
          const data = category_ids.map((id: string) => ({
            category_id: id,
            project_id: project.id,
          }))

          await ProjectCategory.fetchOrCreateMany(['category_id', 'project_id'], data)
        }
      }

      if (payload.flow_ids) {
        const flow_ids = typeof payload.flow_ids === 'string' ? JSON.parse(payload.flow_ids) : payload.flow_ids
        if (flow_ids.length) {
          const data = flow_ids.map((id: string) => ({
            answer_flow_id: id,
            project_id: project.id,
          }))

          await ProjectFlow.fetchOrCreateMany(['answer_flow_id', 'project_id'], data)
        }
      }

      if (payload.group_ids) {
        const group_ids = typeof payload.group_ids === 'string' ? JSON.parse(payload.group_ids) : payload.group_ids
        if (group_ids.length) {
          const data = group_ids.map((id: string) => ({
            group_id: id,
            project_id: project.id,
          }))

          await ProjectGroup.fetchOrCreateMany(['group_id', 'project_id'], data)
        }
      }

      await KanbanColumn.create({
        index: 1,
        name: 'Pre Case',
        description: 'Pre Case',
        project_id: project.id
      })

      const newProject = await Project.query()
        .where('id', project.id)
        .preload('user', (builder) => {
          builder.select(['first_name', 'last_name', 'email', 'img_url'])
        })
        .preload('categories')
        .preload('roles', (builder) => {
          builder.select(['id', 'name'])
        })
        .preload('user_access', (builder) => {
          builder.select(['id', 'first_name', 'last_name', 'email'])
        })
        .preload('flows')
        .preload('groups')
        .first()

      const roomId = `${newProject?.slug}`
      const channel = 'project_add'
      io?.to(roomId).emit(channel, newProject)

      return response.send({message: localize(lang, 'Data has been added'), data: newProject})
    } catch (e) {
      logger.error({ err: e }, 'ProjectsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response, auth, request }: HttpContext) {
    const user = auth.user
    const lang = getLang(request)
    console.log(user)
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credentials') })

    const decode = decodeURIComponent(params.id)
    const project = await Project.query()
      .where('id', decode)
      .orWhere('slug', decode)
      .preload('user', (builder) => {
        builder.select(['first_name', 'last_name', 'email', 'img_url'])
      })
      .preload('categories')
      .preload('roles', (builder) => {
        builder.select(['id', 'name'])
      })
      .preload('flows')
      .preload('groups')
      .first()

    return response.send({data: project})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credentials') })

    const payload = await updateProjectValidator.validate(data)
    try {
      const project = await Project.query().where('id', params.id).first()
      if (!project) return response.status(422).send({message: localize(lang, 'Event not found')})

      if (payload.name) project.name = payload.name
      if (payload.description !== undefined) project.description = payload.description
      if (payload.brief !== undefined) project.brief = payload.brief
      if (payload.status !== undefined) project.status = payload.status
      if (payload.type !== undefined) project.type = payload.type
      if (payload.color) project.color = payload.color
      if (payload.is_archived !== undefined) project.is_archived = payload.is_archived
      await project.save()

      if (payload.role_ids) {
        const role_ids = typeof payload.role_ids === 'string' ? JSON.parse(payload.role_ids) : payload.role_ids
        await project.related('roles').detach()
        await project.related('roles').attach(role_ids)
      }

      if (payload.category_ids) {
        const category_ids = typeof payload.category_ids === 'string' ? JSON.parse(payload.category_ids) : payload.category_ids
        await project.related('categories').detach()
        await project.related('categories').attach(category_ids)
      }

      if (payload.flow_ids) {
        const flow_ids = typeof payload.flow_ids === 'string' ? JSON.parse(payload.flow_ids) : payload.flow_ids
        await project.related('flows').detach()
        await project.related('flows').attach(flow_ids)
      }

      if (payload.user_access_ids) {
        const user_access_ids = typeof payload.user_access_ids === 'string' ? JSON.parse(payload.user_access_ids) : payload.user_access_ids
        await project.related('user_access').detach()
        await project.related('user_access').attach(user_access_ids)
      }

      if (payload.group_ids) {
        const group_ids = typeof payload.group_ids === 'string' ? JSON.parse(payload.group_ids) : payload.group_ids
        await project.related('groups').detach()
        await project.related('groups').attach(group_ids)
      }

      if (payload.user_access_ids) {
        const user_access_ids = typeof payload.user_access_ids === 'string' ? JSON.parse(payload.user_access_ids) : payload.user_access_ids
        await project.related('user_access').detach()
        await project.related('user_access').attach(user_access_ids)
      }

      const updated = await Project.query()
        .where('id', params.id)
        .preload('user', (builder) => {
          builder.select(['first_name', 'last_name', 'email', 'img_url'])
        })
        .preload('categories')
        .preload('roles', (builder) => {
          builder.select(['id', 'name'])
        })
        .preload('user_access', (builder) => {
          builder.select(['id', 'first_name', 'last_name', 'email'])
        })
        .preload('flows')
        .preload('groups')
        .first()

      const roomId = `${updated?.slug}`
      const channel = 'project_update'
      io?.to(roomId).emit(channel, updated)
      return response.send({message: 'Data has been updated', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'ProjectsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const updated = await Project.query().where('id', params.id).first()
      if (updated) await updated.delete()

      const roomId = `${updated?.slug}`
      const channel = 'project_delete'
      io?.to(roomId).emit(channel, updated)
      return response.send({message: 'Data has been deleted', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'ProjectsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

}