import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import Category from '#models/category'
import { storeCategory, updateCategory } from '#validators/category'

export default class CategoriesController {
  /**
   * Display a list of resource
   */
  async index({request, response, auth}: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    try {
      const { order_by, sort_by, limit, page, keyword } = request.all()

      let query = Category.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('name', 'like', '%' + keyword + '%')
        })
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const categories = await query
        .paginate(page, limit)

      return response.send(categories)
    } catch (e) {
      logger.error({ err: e }, 'CategoriesController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })
    const data = request.all()
    const payload = await storeCategory.validate(data)
    try {
      const category = await Category.create({
        name: payload.name,
        description: payload.description
      })

      const newCategory = await Category.query()
        .where('id', category.id)
        .first()
      
      return response.send({message: localize(lang, 'Data has been added'), data: newCategory})
    } catch (e) {
      logger.error({ err: e }, 'CategoriesController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const category = await Category.query()
      .where('id', params.id)
      .first()
    return response.send({data: category})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    const data = request.all()
    const payload = await updateCategory.validate(data)
    try {
      const category = await Category.query().where('id', params.id).first()
      if (!category) return response.status(422).send({message: localize(lang, 'Category not found')})

      if (payload.name) category.name = payload.name
      if (payload.description !== undefined) category.description = payload.description
      await category.save()
      
      const updated = await Category.query().where('id', params.id).first()
      return response.send({message: localize(lang, 'Data has been updated'), data: updated})
    } catch (e) {
      logger.error({ err: e }, 'CategoriesController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const user = auth.user
    if (!user) return response.status(401).send({message: localize(lang, 'Invalid credential') })

    try {
      const deleted = await Category.query().where('id', params.id).first()
      if (deleted)  await deleted.delete()
      return response.send({message: localize(lang, 'Data has been deleted'), data: deleted})
    } catch (e) {
      logger.error({ err: e }, 'CategoriesController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}