import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { getLang } from '../helpers/language.js'
import Comment from '#models/comment'
import { store, update } from '#validators/comment'
import { io } from '#start/ws'
import Attachment from '#models/attachment'
import UserInquiry from '#models/user_inquiry'
import Inquiry from '#models/inquiry'

export default class CommentsController {
  /**
   * Display a list of resource
   */
  async index({request, response}: HttpContext) {
    const lang = getLang(request)
    try {
      const { order_by, sort_by, limit, page, keyword, inquiry_id } = request.all()

      let query = Comment.query()

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('description', 'like', '%' + keyword + '%')
        })
      }

      if (inquiry_id) query = query.where('inquiry_id', inquiry_id)

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('created_at', 'asc')
      }

      const comment = await query.paginate(page, limit)

      return response.send(comment)
    } catch (e) {
      logger.error({ err: e }, 'CommentsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response, auth }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const payload = await store.validate(data)
    try {
      let description = payload.description

      if (payload.attachment_id) {
        const attachment = await Attachment.query().where('id', payload.attachment_id).first()
        if (attachment) description = attachment.name
      }
      const comment = await Comment.create({
        inquiry_id: payload.inquiry_id,
        user_id: auth.user?.id,
        description: description,
        attachment_id: payload.attachment_id
      })

      const newComment = await Comment.query()
        .where('id', comment.id)
        .preload('inquiry')
        .firstOrFail()

      let collaborator = await UserInquiry.query().where({user_id: newComment.user_id, inquiry_id: newComment.inquiry_id}).first()
      if (!collaborator) {
        collaborator = new UserInquiry()
        collaborator.user_id = newComment.user_id
        collaborator.inquiry_id = newComment.inquiry_id
        await collaborator.save()
      }

      const updatedInquiry = await Inquiry.query()
        .where('id', newComment.inquiry_id)
        .preload('project')
        .preload('sub_inquiries')
        .preload('comments')
        .preload('attachments')
        .preload('collaborator', (builder) => {
          builder.select(['first_name', 'last_name', 'email','img_url'])
        })
        .first()

      const roomId = `${updatedInquiry?.project.slug}`
      const channel = 'inquiry_update'
      io?.to(roomId).emit(channel, updatedInquiry)

      const roomId2 = `${newComment?.inquiry.slug}`
      const channel2 = 'comment_add'
      io?.to(roomId2).emit(channel2, newComment)
      return response.send({message: localize(lang, 'Data has been added'), data: newComment})
    } catch (e) {
      logger.error({ err: e }, 'CommentsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const comment = await Comment.query().where('id', params.id).first()
    return response.send({data: comment})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    const data = request.all()
    const payload = await update.validate(data)
    try {
      await Comment.query().where('id', params.id).update(payload)
      const updated = await Comment.query()
        .where('id', params.id)
        .preload('inquiry')
        .firstOrFail()

      const updatedInquiry = await Inquiry.query()
        .where('id', updated.inquiry_id)
        .preload('project')
        .preload('sub_inquiries')
        .preload('comments')
        .preload('attachments')
        .preload('collaborator', (builder) => {
          builder.select(['first_name', 'last_name', 'email','img_url'])
        })
        .first()

      const roomId = `${updatedInquiry?.project.slug}`
      const channel = 'inquiry_update'
      io?.to(roomId).emit(channel, updatedInquiry)

      const roomId2 = `${updated?.inquiry.slug}`
      const channel2 = 'comment_update'
      io?.to(roomId2).emit(channel2, updated)

      return response.send({message: 'Data has been updated', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'CommentsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const updated = await Comment.query()
        .where('id', params.id)
        .where('id', params.id)
        .preload('inquiry')
        .first()

      if (updated) await updated.delete()

      const roomId = `${updated?.inquiry.slug}`
      const channel = 'comment_delete'
      io?.to(roomId).emit(channel, updated)
      return response.send({message: 'Data has been deleted', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'CommentsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }
}