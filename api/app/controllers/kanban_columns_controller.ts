import type { HttpContext } from '@adonisjs/core/http'
import { getLang } from '../helpers/language.js'
import KanbanColumn from '#models/kanban_column'
import logger from '@adonisjs/core/services/logger'
import { localize } from '../helpers/localization.js'
import { io } from '#start/ws'
import Inquiry from '#models/inquiry'

export default class KanbanColumnsController {
  /**
   * Display a list of resource
   */
  async index({request, response}: HttpContext) {
    const lang = getLang(request)
    try {
      const { order_by, sort_by, limit, page, keyword, project_id } = request.all()

      if (!project_id) return response.status(404).send({message: localize(lang, 'Project not found')})

      let query = KanbanColumn.query().where('project_id', project_id)

      if (keyword) {
        query = query.where((builder) => {
          builder
            .where('name', 'like', '%' + keyword + '%')
        })
      }

      if (order_by && sort_by) {
        query = query.orderBy(order_by, sort_by)
      } else {
        query = query.orderBy('index', 'asc')
      }

      const column = await query.paginate(page, limit)

      return response.send(column)
    } catch (e) {
      logger.error({ err: e }, 'KanbanColumnsController.index')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Handle form submission for the create action
   */
  async store({ request, response }: HttpContext) {
    const lang = getLang(request)
    const {name, description, project_id, directions, current_index} = request.all()
    
    try {
      if (!project_id) return response.status(422).send({message: localize(lang, 'Required project ID')})
      if (name) {
        const isExist = await KanbanColumn.query().where('project_id', project_id).where('name', name).first()
        if (isExist) return response.status(422).send({message: localize(lang, 'Kanban column already exist')})
      } else {
        return response.status(422).send({message: localize(lang, 'Insert name')})
      }

      const lastIndex = await KanbanColumn.query().where('project_id', project_id).orderBy('index', 'desc').first()
      const index = lastIndex ? lastIndex.index + 1 : 1

      const column = new KanbanColumn()
      column.project_id = project_id
      column.name = name
      column.description = description
      column.index = index
      await column.save()

      const newColumn = await KanbanColumn.query().where('id', column.id).preload('project').firstOrFail()
      newColumn.current_index = current_index
      newColumn.directions = directions

      const roomId = `${newColumn?.project.slug}`
      const channel = 'kanban_add'
      io?.to(roomId).emit(channel, newColumn)

      return response.send({message: localize(lang, 'Data has been added'), data: newColumn})
    } catch (e) {
      logger.error({ err: e }, 'KanbanColumnsController.store')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const column = await KanbanColumn.query().where('id', params.id).first()
    return response.send({data: column})
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    const {name, description} = request.all()
    try {
      const column = await KanbanColumn.query().where('id', params.id).preload('project').first()
      if (!column) return response.status(422).send({message: localize(lang, 'Kanban column not found')})
      let isChangeName = false
      const oldColumnName = column.name
      if (name) {
        if (name.toLocaleLowerCase() !== column.name.toLocaleLowerCase()) {
          const isExist = await KanbanColumn.query().whereNot('id', column.id).where('project_id', column.project_id).where('name', name).first()
          if (isExist) return response.status(422).send({message: localize(lang, 'Kanban column already exist')})
          isChangeName = true
          column.name = name
        }
      }
      if (description) column.description = description
      await column.save()

      const roomId = `${column.project.slug}`
      const channel = 'kanban_update'
      io?.to(roomId).emit(channel, column)

      if (isChangeName) {
        await Inquiry.query()
          .where('project_id', column.project_id)
          .where('type', oldColumnName)
          .update({
            type: column.name
          })

        const inquiries =  await Inquiry.query()
        .where('project_id', column.project_id)
        .where('type', column.name)
        const channelInquiry = 'inquiry_update_bulk'

        io?.to(roomId).emit(channelInquiry, inquiries)
      }

      return response.send({message: 'Data has been updated', data: column})
    } catch (e) {
      logger.error({ err: e }, 'KanbanColumnsController.update')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  /**
   * Delete record
   */
  async destroy({ params, request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const updated = await KanbanColumn.query()
        .where('id', params.id)
        .preload('project')
        .first()

      if (updated) await updated.delete()

      const roomId = `${updated?.project.slug}`
      const channel = 'kanban_delete'
      io?.to(roomId).emit(channel, updated)
      return response.send({message: 'Data has been deleted', data: updated})
    } catch (e) {
      logger.error({ err: e }, 'KanbanColumnsController.destroy')
      return response.status(422).send({message: localize(lang, 'Something went wrong, please try again later')})
    }
  }

  async reorder({ request, response }: HttpContext) {
    const lang = getLang(request)
    try {
      const column_ids = request.input('column_ids')

      if (!column_ids) return response.status(422).send({ message: localize(lang, 'Column not found') })

      const arrColumn = typeof column_ids === 'string' ? JSON.parse(column_ids) : column_ids

      for (const [i, element] of arrColumn.entries()) {
        await KanbanColumn.query()
          .where('id', element)
          .update({ index: i + 1 })
      }

      const updated = await KanbanColumn.query().whereIn('id', arrColumn).orderBy('index', 'asc')

      const updatedColumn = await KanbanColumn.query().where('project_id', updated[0].id).preload('project').first()
      const roomId = `${updatedColumn?.project.slug}`
      const channel = 'kanban_reorder'
      io?.to(roomId).emit(channel, updated)

      return response.send({ message: 'Data has been reorder', data: updated })
    } catch (e) {
      logger.error({ err: e }, 'KanbanColumnsController.reorder')
      return response
        .status(422)
        .send({ message: localize(lang, 'Something went wrong, please try again later') })
    }
  }
}