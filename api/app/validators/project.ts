import vine from '@vinejs/vine'

export const storeProjectValidator = vine
  .compile(
  vine.object({
    name: vine.string(),
    description: vine.string().trim().nullable().optional(),
    type: vine.string().trim().nullable().optional(),
    color: vine.string().trim().nullable().optional(),
    status: vine.string().trim().nullable().optional(),
    brief: vine.string().trim().nullable().optional(),
    role_ids: vine.string().trim().nullable().optional(),
    category_ids: vine.string().trim().nullable().optional(),
    flow_ids: vine.string().trim().nullable().optional(),
    group_ids: vine.string().trim().nullable().optional(),
    is_archived: vine.boolean().optional(),
    user_access_ids: vine.string().trim().nullable().optional(),
  })
)

export const updateProjectValidator = vine
  .compile(
  vine.object({
    name: vine.string().optional(),
    description: vine.string().trim().nullable().optional(),
    type: vine.string().trim().nullable().optional(),
    color: vine.string().trim().nullable().optional(),
    status: vine.string().trim().nullable().optional(),
    brief: vine.string().trim().nullable().optional(),
    role_ids: vine.string().trim().nullable().optional(),
    category_ids: vine.string().trim().nullable().optional(),
    flow_ids: vine.string().trim().nullable().optional(),
    group_ids: vine.string().trim().nullable().optional(),
    is_archived: vine.boolean().optional(),
    user_access_ids: vine.string().trim().nullable().optional(),
  })
)