import vine from '@vinejs/vine'

export const store = vine
  .compile(
  vine.object({
    isPinned: vine.boolean().optional(),
    messages: vine.string().trim().nullable().optional(),
    content: vine.string().trim().nullable().optional()
  })
)

export const update = vine
  .compile(
  vine.object({
    isPinned: vine.boolean().optional(),
    messages: vine.string().trim().nullable().optional(),
    content: vine.string().trim().nullable().optional()
  })
)