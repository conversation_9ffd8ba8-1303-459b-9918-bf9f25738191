import vine from '@vinejs/vine'

export const store = vine
  .compile(
  vine.object({
    title: vine.string().trim(),
    description: vine.string().trim().nullable().optional(),
    parent_id: vine.number().nullable().optional(),
    project_id: vine.number(),
    assign_to: vine.number().nullable().optional(),
    status: vine.string().trim().nullable().optional(),
    sender_id: vine.number().nullable().optional(),
    submited_by: vine.number().nullable().optional(),
    execution_to: vine.number().nullable().optional(),
    completed_by: vine.number().nullable().optional(),
    repetition: vine.string().trim().nullable().optional(),
    notes: vine.string().trim().nullable().optional(),
    notification: vine.string().trim().nullable().optional(),
    conversation_id: vine.string().trim().nullable().optional(),
    type: vine.string().trim(),
  })
)

export const update = vine
  .compile(
  vine.object({
    title: vine.string().trim().optional(),
    description: vine.string().trim().nullable().optional(),
    parent_id: vine.number().nullable().optional(),
    project_id: vine.number().optional(),
    assignTo: vine.number().nullable().optional(),
    status: vine.string().trim().nullable().optional(),
    sender_id: vine.number().nullable().optional(),
    submited_by: vine.number().nullable().optional(),
    execution_to: vine.number().nullable().optional(),
    completed_by: vine.number().nullable().optional(),
    repetition: vine.string().trim().nullable().optional(),
    notes: vine.string().trim().nullable().optional(),
    notification: vine.string().trim().nullable().optional(),
    conversation_id: vine.string().trim().nullable().optional(),
    type: vine.string().trim().nullable().optional(),
  })
)