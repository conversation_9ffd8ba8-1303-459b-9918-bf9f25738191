import vine, { VineString } from '@vinejs/vine'
import { Options, uniqueRule } from '../rules/unique.js'


declare module '@vinejs/vine' {
  interface VineString {
    unique(options: Options): this
  }
}

VineString.macro('unique', function (this: VineString, options: Options) {
  return this.use(uniqueRule(options))
})

/**
 * Validates the profile update action
 */
export const update = vine
  .withMetaData<{ user_id: number }>()
  .compile(
  vine.object({
    first_name: vine.string().trim().optional(),
    last_name: vine.string().trim().optional(),
    phone: vine.string().unique({
      table: 'users',
      column: 'phone'
    }).optional(),
    email: vine.string().unique({
      table: 'users',
      column: 'email'
    }).optional(),
    password: vine.string().trim().minLength(8).alphaNumeric().optional(),
    address: vine.string().trim().nullable().optional(),
    unit: vine.string().trim().nullable().optional(),
    other: vine.string().trim().nullable().optional(),
    short_bio: vine.string().trim().nullable().optional(),
    is_display_contact: vine.boolean().optional(),
    language: vine.string().trim().nullable().optional(),
    img_url: vine.string().trim().nullable().optional(),
  })
)