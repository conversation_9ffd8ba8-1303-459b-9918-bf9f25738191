import vine from '@vinejs/vine'

export const store = vine
  .compile(
  vine.object({
    name: vine.string().trim(),
    description: vine.string().trim().nullable().optional(),
    content: vine.string().trim().nullable().optional(),
    color: vine.string().trim().nullable().optional(),
  })
)

export const update = vine
  .compile(
  vine.object({
    name: vine.string().trim().optional(),
    description: vine.string().trim().nullable().optional(),
    content: vine.string().trim().nullable().optional(),
    color: vine.string().trim().nullable().optional(),
  })
)