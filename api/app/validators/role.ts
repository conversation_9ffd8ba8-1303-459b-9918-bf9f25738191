import vine from '@vinejs/vine'

export const storeRole = vine
  .compile(
  vine.object({
    name: vine.string().trim(),
    description: vine.string().trim().nullable(),
    access_rights: vine.string().trim()
  })
)

export const updateRole = vine
  .compile(
  vine.object({
    name: vine.string().trim().optional(),
    description: vine.string().trim().nullable(),
    access_rights: vine.string().trim()
  })
)