import vine, { VineString } from '@vinejs/vine'
import { uniqueRule, Options } from '../rules/unique.js'

declare module '@vinejs/vine' {
  interface VineString {
    unique(options: Options): this
  }
}

VineString.macro('unique', function (this: VineString, options: Options) {
  return this.use(uniqueRule(options))
})

/**
 * Validates the profile update action
 */
export const storeUser = vine
  .compile(
  vine.object({
    first_name: vine.string().trim(),
    last_name: vine.string().trim(),
    phone: vine.string().unique({
      table: 'users',
      column: 'phone'
    }).optional(),
    email: vine.string().unique({
      table: 'users',
      column: 'email'
    }).optional(),
    role_ids: vine.string().trim(),
    group_ids: vine.string().trim().optional(),
    password: vine.string().trim().minLength(8).alphaNumeric(),
    password_confirmation: vine.string().trim().minLength(8).alphaNumeric(),
    address: vine.string().trim().nullable().optional(),
    unit: vine.string().trim().nullable().optional(),
    other: vine.string().trim().nullable().optional(),
    short_bio: vine.string().trim().nullable().optional(),
    is_display_contact: vine.boolean().optional(),
    language: vine.string().trim().nullable().optional(),
    img_url: vine.string().trim().nullable().optional(),
  })
)

/**
 * Validates the profile update action
 */
export const updateUser = vine
  .withMetaData<{ user_id: number }>()
  .compile(
  vine.object({
    first_name: vine.string().trim().optional(),
    last_name: vine.string().trim().optional(),
    phone: vine.string().unique({
      table: 'users',
      column: 'phone'
    }).optional(),
    email: vine.string().unique({
      table: 'users',
      column: 'email'
    }).optional(),
    role_ids: vine.string().trim().optional(),
    group_ids: vine.string().trim().optional(),
    password: vine.string().trim().minLength(8).alphaNumeric().optional(),
    password_confirmation: vine.string().trim().minLength(8).alphaNumeric().optional(),
    address: vine.string().trim().nullable().optional(),
    unit: vine.string().trim().nullable().optional(),
    other: vine.string().trim().nullable().optional(),
    short_bio: vine.string().trim().nullable().optional(),
    is_display_contact: vine.boolean().optional(),
    is_active: vine.boolean().optional(),
    language: vine.string().trim().nullable().optional(),
    img_url: vine.string().trim().nullable().optional(),
  })
)