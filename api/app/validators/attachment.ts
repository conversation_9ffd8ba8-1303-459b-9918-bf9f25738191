import vine from '@vinejs/vine'

export const store = vine
  .compile(
  vine.object({
    inquiry_id: vine.number().optional(),
    feed_post_id: vine.number().optional(),
    comment_id: vine.number().optional(),
    file_url: vine.string().trim(),
    project_id: vine.number().optional(),
    description: vine.string().trim().optional(),
    name: vine.string().trim().optional()
  })
)

export const update = vine
  .compile(
  vine.object({
    file_url: vine.string().trim().optional(),
    project_id: vine.number().optional(),
    feed_post_id: vine.number().optional(),
    comment_id: vine.number().optional(),
    description: vine.string().trim().optional(),
    name: vine.string().trim().optional()
  })
)