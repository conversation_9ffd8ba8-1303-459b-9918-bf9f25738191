import env from '#start/env'
import { defineConfig, services } from '@adonisjs/drive'

const driveConfig = defineConfig({
  default: env.get('DRIVE_DISK'),

  /**
   * The services object can be used to configure multiple file system
   * services each using the same or a different driver.
   */
  // services: { 
  //   s3: services.s3({
  //     credentials: {
  //       accessKeyId: env.get('AWS_ACCESS_KEY_ID'),
  //       secretAccessKey: env.get('AWS_SECRET_ACCESS_KEY'),
  //     },
  //     region: env.get('AWS_REGION'),
  //     bucket: env.get('S3_BUCKET'),
  //     visibility: 'public',
  //   }),
  // },
  services: { 
    s3: services.s3({
      credentials: {
        accessKeyId: env.get('MINIO_ACCESS_KEY_ID'),
        secretAccessKey: env.get('MINIO_SECRET_ACCESS_KEY'),
      },
      region: env.get('MINIO_REGION'),
      bucket: env.get('MINIO_BUCKET'),
      endpoint: env.get('MINIO_ENDPOINT'),
      visibility: 'public',
      forcePathStyle: true,
      apiVersion:'v4'
    }),
  },
})

export default driveConfig

declare module '@adonisjs/drive/types' {
  export interface DriveDisks extends InferDriveDisks<typeof driveConfig> {}
}