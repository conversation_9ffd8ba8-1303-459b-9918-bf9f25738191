import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('first_name').nullable()
      table.string('last_name').nullable()
      table.string('email', 255).notNullable().unique()
      table.string('password').notNullable()
      table.string('phone', 255).nullable()
      table.string('address').nullable()
      table.string('unit').nullable()
      table.boolean('is_active').defaultTo(0)
      table.text('other').nullable()
      table.text('short_bio').nullable()
      table.boolean('is_display_contact').defaultTo(0)
      table.string('language').defaultTo('en')
      table.string('img_url').nullable()
      table.timestamp('login_at', { useTz: true }).nullable()
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}