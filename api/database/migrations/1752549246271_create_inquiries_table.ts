import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'inquiries'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('parent_id').nullable().unsigned().references('id').inTable('inquiries').onDelete('CASCADE')
      table.integer('project_id').nullable().unsigned().references('id').inTable('projects').onDelete('CASCADE')
      table.integer('assign_to').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('sender_id').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('submited_by').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('execution_to').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('completed_by').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.string('title').notNullable()
      table.string('slug').nullable()
      table.string('repetition').defaultTo('none')
      table.text('description').nullable()
      table.string('status').nullable()
      table.text('notes').nullable()
      table.text('notification').nullable()
      table.string('conversation_id').nullable()
      table.string('type').nullable()
      table.integer('index').nullable()
      table.dateTime('completed_date', { useTz: true }).nullable()
      table.dateTime('due_date', { useTz: true }).nullable()
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}