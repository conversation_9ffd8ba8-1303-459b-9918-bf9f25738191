import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'project_roles'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('role_id').nullable().unsigned().references('id').inTable('roles').onDelete('CASCADE')
      table.integer('project_id').nullable().unsigned().references('id').inTable('projects').onDelete('CASCADE')
      table.unique(['role_id', 'project_id'])
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}