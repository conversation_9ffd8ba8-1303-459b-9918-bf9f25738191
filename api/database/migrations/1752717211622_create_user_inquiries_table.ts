import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_inquiries'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('inquiry_id').nullable().unsigned().references('id').inTable('inquiries').onDelete('CASCADE')
      table.integer('user_id').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.unique(['user_id', 'inquiry_id'])
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}