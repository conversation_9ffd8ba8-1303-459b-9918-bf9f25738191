import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'project_users'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('user_id').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('project_id').nullable().unsigned().references('id').inTable('projects').onDelete('CASCADE')
      table.unique(['user_id', 'project_id'])
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}