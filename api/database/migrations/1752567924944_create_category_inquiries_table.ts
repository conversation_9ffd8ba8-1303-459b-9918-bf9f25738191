import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'category_inquiries'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('inquiry_id').nullable().unsigned().references('id').inTable('inquiries').onDelete('CASCADE')
      table.integer('category_id').nullable().unsigned().references('id').inTable('categories').onDelete('CASCADE')
      table.unique(['inquiry_id', 'category_id'])
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}