import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'comments'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('inquiry_id').nullable().unsigned().references('id').inTable('inquiries').onDelete('CASCADE')
      table.integer('user_id').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('attachment_id').nullable().unsigned().references('id').inTable('attachments').onDelete('CASCADE')
      table.text('description')
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}