import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'attachments'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('inquiry_id').nullable().unsigned().references('id').inTable('inquiries').onDelete('CASCADE')
      table.integer('user_id').nullable().unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('project_id').nullable().unsigned().references('id').inTable('projects').onDelete('CASCADE')
      table.integer('feed_post_id').nullable().unsigned().references('id').inTable('feed_posts').onDelete('CASCADE')
      table.integer('comment_id').nullable().unsigned().references('id').inTable('comments').onDelete('CASCADE')
      table.text('file_url')
      table.text('name').nullable()
      table.text('description').nullable()
      table.timestamp('created_at', { useTz: true }).nullable()
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}