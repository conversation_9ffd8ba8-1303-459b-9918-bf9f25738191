import Role from '#models/role'
import User from '#models/user'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

export default class extends BaseSeeder {
  async run() {
    const roles = new Role()
    roles.name = 'Super Admin'
    roles.description = 'Super Admin Roles'
    roles.access_rights = 'all_access'
    await roles.save()

    const admin = new User()
    admin.first_name = 'Desidia'
    admin.last_name = 'Admin'
    admin.email = '<EMAIL>'
    admin.password = 'DesidiaAdmin2025'
    admin.phone = '6285721812512'
    admin.address = 'Address'
    admin.unit = 'B24'
    admin.language = 'en'
    await admin.save()

    admin.related('roles').attach([roles.id])
  }
}