import app from '@adonisjs/core/services/app'
import { Server } from 'socket.io'
import server from '@adonisjs/core/services/server'

let io: Server | undefined

app.ready(() => {
  io = new Server(server.getNodeServer(), {
    cors: {
      origin: '*',
    },
    allowEIO3: true,
  })

  io.on('connection', (socket) => {
    console.log('A new connection', socket.id)

    // Join
    socket.on('join', async function (roomId) {
      socket.join(roomId)
    })

    // Leave
    socket.on('leave', function (roomId) {
      socket.leave(roomId)
    })

    socket.emit('role_add', 'test')
  })
})

// Export the io instance
export { io }
