/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from './kernel.js'
const AuthController = () => import('#controllers/auth_controller')
const CategoriesController = () => import('#controllers/categories_controller')
const RolesController = () => import('#controllers/roles_controller')
const UsersController = () => import('#controllers/users_controller')
const GroupsController = () => import('#controllers/groups_controller')
const FeedPostsController = () => import('#controllers/feed_posts_controller')
const AnswerFlowsController = () => import('#controllers/answer_flows_controller')
const ProjectsController = () => import('#controllers/projects_controller')
const KanbanColumnsController = () => import('#controllers/kanban_columns_controller')
const InquiriesController = () => import('#controllers/inquiries_controller')
const CommentsController = () => import('#controllers/comments_controller')
const UploadsController = () => import('#controllers/uploads_controller')
const AttachmentsController = () => import('#controllers/attachments_controller')

router.get('/', async () => {
  return {
    hello: 'world',
  }
})

// Auth
router
  .group(() => {
    router.post('login', [AuthController, 'login'])

    // With Credential
    router
      .group(() => {
        router.post('logout', [AuthController, 'logout'])
        router.get('me', [AuthController, 'profile'])
        router.put('update', [AuthController, 'update'])
        router.put('changePassword', [AuthController, 'changePassword'])
      })
      .use(middleware.auth({ guards: ['api'] }))
  })
  .prefix('auth')

  // With Credential
router
  .group(() => {
    // Categories
    router.get('categories', [CategoriesController, 'index'])
    router.get('categories/:id', [CategoriesController, 'show'])
    router.post('categories', [CategoriesController, 'store'])
    router.put('categories/:id', [CategoriesController, 'update'])
    router.delete('categories/:id', [CategoriesController, 'destroy'])

    router.post('uploads', [UploadsController, 'upload'])

    // Roles
    router.get('roles', [RolesController, 'index'])
    router.get('roles/:id', [RolesController, 'show'])
    router.post('roles', [RolesController, 'store'])
    router.put('roles/:id', [RolesController, 'update'])
    router.delete('roles/:id', [RolesController, 'destroy'])

    // Users
    router.get('users', [UsersController, 'index'])
    router.get('users/:id', [UsersController, 'show'])
    router.post('users', [UsersController, 'store'])
    router.put('users/:id', [UsersController, 'update'])
    router.delete('users/:id', [UsersController, 'destroy'])

    // Groups
    router.get('groups', [GroupsController, 'index'])
    router.get('groups/:id', [GroupsController, 'show'])
    router.post('groups', [GroupsController, 'store'])
    router.put('groups/:id', [GroupsController, 'update'])
    router.delete('groups/:id', [GroupsController, 'destroy'])

    // Feed Post
    router.get('feeds', [FeedPostsController, 'index'])
    router.get('feeds/:id', [FeedPostsController, 'show'])
    router.post('feeds', [FeedPostsController, 'store'])
    router.put('feeds/:id', [FeedPostsController, 'update'])
    router.delete('feeds/:id', [FeedPostsController, 'destroy'])

    // Answer Flow
    router.get('answerFlows', [AnswerFlowsController, 'index'])
    router.get('answerFlows/:id', [AnswerFlowsController, 'show'])
    router.post('answerFlows', [AnswerFlowsController, 'store'])
    router.put('answerFlows/:id', [AnswerFlowsController, 'update'])
    router.delete('answerFlows/:id', [AnswerFlowsController, 'destroy'])

    // Projects
    router.get('projects', [ProjectsController, 'index'])
    router.get('projects/:id', [ProjectsController, 'show'])
    router.post('projects', [ProjectsController, 'store'])
    router.put('projects/:id', [ProjectsController, 'update'])
    router.delete('projects/:id', [ProjectsController, 'destroy'])

    // Kanban
    router.get('kanbans', [KanbanColumnsController, 'index'])
    router.get('kanbans/:id', [KanbanColumnsController, 'show'])
    router.post('kanbans', [KanbanColumnsController, 'store'])
    router.put('kanbans/reorder', [KanbanColumnsController, 'reorder'])
    router.put('kanbans/:id', [KanbanColumnsController, 'update'])
    router.delete('kanbans/:id', [KanbanColumnsController, 'destroy'])

    // Inquiries
    router.get('inquiries', [InquiriesController, 'index'])
    router.get('inquiries/calendar', [InquiriesController, 'calendar'])
    router.get('inquiries/:id', [InquiriesController, 'show'])
    router.put('inquiries/:id/assign', [InquiriesController, 'assign'])
    router.put('inquiries/:id/completed', [InquiriesController, 'setCompleted'])
    router.post('inquiries', [InquiriesController, 'store'])
    router.put('inquiries/reorder', [InquiriesController, 'reorder'])
    router.put('inquiries/:id', [InquiriesController, 'update'])
    router.delete('inquiries/:id', [InquiriesController, 'destroy'])

    // Comments
    router.get('comments', [CommentsController, 'index'])
    router.get('comments/:id', [CommentsController, 'show'])
    router.post('comments', [CommentsController, 'store'])
    router.put('comments/:id', [CommentsController, 'update'])
    router.delete('comments/:id', [CommentsController, 'destroy'])

    // Attachments
    router.get('attachments', [AttachmentsController, 'index'])
    router.get('attachments/:id', [AttachmentsController, 'show'])
    router.post('attachments', [AttachmentsController, 'store'])
    router.put('attachments/:id', [AttachmentsController, 'update'])
    router.delete('attachments/:id', [AttachmentsController, 'destroy'])
  }).use(middleware.auth({ guards: ['api'] }))
