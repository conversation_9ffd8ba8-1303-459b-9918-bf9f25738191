/*
|--------------------------------------------------------------------------
| Environment variables service
|--------------------------------------------------------------------------
|
| The `Env.create` method creates an instance of the Env service. The
| service validates the environment variables and also cast values
| to JavaScript data types.
|
*/

import { Env } from '@adonisjs/core/env'

export default await Env.create(new URL('../', import.meta.url), {
  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),
  PORT: Env.schema.number(),
  APP_KEY: Env.schema.string(),
  HOST: Env.schema.string({ format: 'host' }),
  LOG_LEVEL: Env.schema.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']),
  APP_URL: Env.schema.string(),
  FRONTEND_URL: Env.schema.string(),
  /*
  |----------------------------------------------------------
  | Variables for configuring database connection
  |----------------------------------------------------------
  */
  DB_HOST: Env.schema.string({ format: 'host' }),
  DB_PORT: Env.schema.number(),
  DB_USER: Env.schema.string(),
  DB_PASSWORD: Env.schema.string.optional(),
  DB_DATABASE: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for configuring the drive package
  |----------------------------------------------------------
  */
  // DRIVE_DISK: Env.schema.enum(['s3'] as const),
  // AWS_ACCESS_KEY_ID: Env.schema.string(),
  // AWS_SECRET_ACCESS_KEY: Env.schema.string(),
  // AWS_REGION: Env.schema.string(),
  // AWS_BUCKET: Env.schema.string(),
  // AWS_ENDPOINT: Env.schema.string(),

  DRIVE_DISK: Env.schema.enum(['s3'] as const),
  MINIO_ACCESS_KEY_ID: Env.schema.string(),
  MINIO_SECRET_ACCESS_KEY: Env.schema.string(),
  MINIO_REGION: Env.schema.string(),
  MINIO_BUCKET: Env.schema.string(),
  MINIO_ENDPOINT: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for configuring the limiter package
  |----------------------------------------------------------
  */
  LIMITER_STORE: Env.schema.enum(['redis', 'memory'] as const),

  /*
  |----------------------------------------------------------
  | Variables for configuring the mail package
  |----------------------------------------------------------
  */
  MAIL_HOST: Env.schema.string(),
  MAIL_PORT: Env.schema.string(),
  MAIL_PASSWORD: Env.schema.string(),
  MAIL_USERNAME: Env.schema.string(),
  SENDER_NAME: Env.schema.string(),
  SENDER_MAIL: Env.schema.string(),

  REDIS_HOST: Env.schema.string({ format: 'host' }),
  REDIS_PORT: Env.schema.number(),
  REDIS_PASSWORD: Env.schema.string.optional(),

  /*
  |----------------------------------------------------------
  | Variables for @rlanz/bull-queue
  |----------------------------------------------------------
  */
  QUEUE_REDIS_HOST: Env.schema.string({ format: 'host' }),
  QUEUE_REDIS_PORT: Env.schema.number(),
  QUEUE_REDIS_PASSWORD: Env.schema.string.optional()
})
