# Desidia - Project Management & Inquiry System

[![Vue.js](https://img.shields.io/badge/Vue.js-3.5.6-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![Vite](https://img.shields.io/badge/Vite-2.8.1-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.4.7-06B6D4?style=flat-square&logo=tailwindcss)](https://tailwindcss.com/)
[![PWA](https://img.shields.io/badge/PWA-Ready-5A0FC8?style=flat-square)](https://web.dev/progressive-web-apps/)

> **Desidia** is a comprehensive project management and inquiry tracking system designed to streamline team collaboration, project oversight, and administrative workflows. Built with modern web technologies, it provides a robust platform for managing projects, inquiries, users, and organizational resources.

## 🚀 Features

### 📊 **Project Management**
- **Project Creation & Management**: Create, edit, and organize projects with custom colors and descriptions
- **Kanban Boards**: Visual project tracking with drag-and-drop functionality
- **Inquiry System**: Comprehensive inquiry/task management with status tracking
- **Collaboration Tools**: Real-time collaboration with team members
- **File Attachments**: Upload and manage project-related documents

### 👥 **User & Role Management**
- **Multi-Role System**: Admin, Client, Freelancer, and User roles
- **User Access Control**: Granular permissions and project access management
- **Team Collaboration**: Assign users to projects and inquiries
- **Real-time Notifications**: Stay updated with project changes

### 🔧 **Administrative Features**
- **Admin Dashboard**: Comprehensive overview with statistics and metrics
- **User Management**: Create, edit, and manage user accounts
- **Role & Permission Management**: Define and assign user roles
- **Category & Group Management**: Organize projects and users
- **Feed Post Management**: Internal communication and announcements

### 🎨 **Modern UI/UX**
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- **Dark/Light Mode**: User preference-based theme switching
- **Progressive Web App**: Install and use offline
- **Advanced Filtering**: Sort and filter data across all modules
- **Real-time Updates**: Live data synchronization via WebSocket

### 🔍 **Advanced Features**
- **AI Assistant Integration**: Built-in AI assistant for enhanced productivity
- **Search & Filter**: Global search with advanced filtering options
- **Export Capabilities**: Export data to various formats
- **Internationalization**: Multi-language support (EN, ID, NO)
- **Video Integration**: Video.js integration for media content

## 🛠️ Technology Stack

### **Frontend**
- **Vue.js 3.5.6** - Progressive JavaScript framework
- **Vite 2.8.1** - Next-generation frontend tooling
- **Vue Router 4.4.3** - Official router for Vue.js
- **Vuex 4.0.2** - State management pattern + library

### **UI Framework & Styling**
- **TailwindCSS 3.4.7** - Utility-first CSS framework
- **PrimeVue 4.0.5** - Rich UI component library
- **Headless UI** - Unstyled, accessible UI components
- **Heroicons** - Beautiful hand-crafted SVG icons

### **Development Tools**
- **ESLint** - Code linting and formatting
- **Sass** - CSS preprocessor
- **PostCSS** - CSS transformation tool
- **Playwright** - End-to-end testing

### **Additional Libraries**
- **Socket.io** - Real-time bidirectional communication
- **Axios** - HTTP client for API requests
- **Vue I18n** - Internationalization plugin
- **CKEditor 5** - Rich text editor
- **Video.js** - HTML5 video player

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v16.0.0 or higher)
- **npm** (v7.0.0 or higher) or **yarn**
- **Git** for version control

## 🚀 Installation

### 1. Clone the Repository
```bash
git clone https://gitlab.com/planlagt/app.git
cd app
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Environment Configuration
Create a `.env` file in the root directory:
```env
VITE_API_URL=http://localhost:8000
VITE_APP_NAME=Desidia
VITE_APP_VERSION=1.0.0
```

### 4. Start Development Server
```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:4444`

## 📦 Build & Deployment

### Development Build
```bash
npm run build
```

### Production Deployment
```bash
# Build for production
npm run build

# Deploy to AWS S3 (if configured)
npm run deploy
```

### Preview Production Build
```bash
npm run serve
```

## 🏗️ Project Structure

```
src/
├── api/                    # API service modules
├── assets/                 # Static assets (images, styles)
├── components/             # Reusable Vue components
│   ├── admin/             # Admin-specific components
│   ├── global/            # Global/shared components
│   ├── inquiry/           # Inquiry management components
│   ├── kanban/            # Kanban board components
│   └── project/           # Project management components
├── layouts/               # Layout components
├── pages/                 # Page components (routes)
├── store/                 # Vuex store modules
├── router.js              # Vue Router configuration
└── main.js               # Application entry point
```

## 🔧 Configuration

### Vite Configuration
The project uses Vite for build tooling with the following key configurations:
- **PWA Support**: Service worker and offline capabilities
- **Auto-imports**: Automatic component and composable imports
- **Path Aliases**: `@/` points to `src/` directory

### TailwindCSS
Custom configuration includes:
- **Custom Colors**: Brand-specific color palette
- **Typography Plugin**: Enhanced text styling
- **Forms Plugin**: Styled form components

## 🧪 Testing

### Run Tests
```bash
npm run test
# or
yarn test
```

### End-to-End Testing
```bash
npx playwright test
```

## 📱 Progressive Web App (PWA)

Desidia is built as a PWA with the following features:
- **Offline Support**: Core functionality works without internet
- **Install Prompt**: Users can install the app on their devices
- **Background Sync**: Data synchronization when connection is restored
- **Push Notifications**: Real-time updates (when configured)

## 🌐 API Integration

The application integrates with a backend API for:
- **Authentication & Authorization**
- **Project & Inquiry Management**
- **User & Role Management**
- **File Upload & Management**
- **Real-time Communication**

### API Modules
- `auth.js` - Authentication services
- `project.js` - Project management
- `inquiries.js` - Inquiry/task management
- `user.js` - User management
- `kanban.js` - Kanban board operations

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-based Access Control**: Granular permission system
- **Secure Storage**: Encrypted local storage for sensitive data
- **HTTPS Enforcement**: Secure communication protocols

## 🌍 Internationalization

Supports multiple languages:
- **English (EN)** - Default
- **Indonesian (ID)**
- **Norwegian (NO)**

Add new languages by creating translation files in `src/locales/`

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit your changes**: `git commit -m 'Add amazing feature'`
4. **Push to the branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### Development Guidelines
- Follow Vue.js style guide
- Use TypeScript for new components
- Write tests for new features
- Update documentation as needed

## 📞 Support

For support and questions:
- **Issues**: [GitLab Issues](https://gitlab.com/planlagt/app/-/issues)
- **Documentation**: Check the `/docs` folder
- **Email**: Contact the development team

## 🗺️ Roadmap

### Upcoming Features
- [ ] **Mobile App**: React Native companion app
- [ ] **Advanced Analytics**: Enhanced reporting and insights
- [ ] **Third-party Integrations**: Slack, Microsoft Teams, etc.
- [ ] **API Documentation**: Comprehensive API docs
- [ ] **Performance Optimization**: Enhanced loading and caching

## 👨‍💻 Authors

- **Development Team** - Initial work and ongoing maintenance
- **Contributors** - See [GitLab Contributors](https://gitlab.com/planlagt/app/-/graphs/main)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🚀 Project Status

**Active Development** - This project is actively maintained and regularly updated with new features and improvements.

---

**Built with ❤️ using Vue.js and modern web technologies**
