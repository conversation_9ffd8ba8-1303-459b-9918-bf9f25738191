<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Project CRUD</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Project CRUD API Test</h1>
    
    <div class="section">
        <h2>Test Project Creation</h2>
        <button onclick="testCreateProject()">Create Test Project</button>
        <div id="create-result"></div>
    </div>
    
    <div class="section">
        <h2>Test Project Update with User Access</h2>
        <input type="number" id="project-id" placeholder="Project ID" value="6">
        <button onclick="testUpdateProject()">Update Project with Users</button>
        <div id="update-result"></div>
    </div>
    
    <div class="section">
        <h2>Test Get Project Details</h2>
        <input type="number" id="get-project-id" placeholder="Project ID" value="6">
        <button onclick="testGetProject()">Get Project Details</button>
        <div id="get-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api'; // Adjust this to your API base URL
        
        async function apiCall(method, endpoint, data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        // Add your auth headers here if needed
                        // 'Authorization': 'Bearer your-token'
                    }
                };
                
                if (data) {
                    if (method === 'GET') {
                        const params = new URLSearchParams(data);
                        endpoint += '?' + params.toString();
                    } else {
                        options.body = JSON.stringify(data);
                    }
                }
                
                const response = await fetch(API_BASE + endpoint, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        async function testCreateProject() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.innerHTML = 'Creating project...';
            
            const projectData = {
                name: 'Test Project ' + Date.now(),
                description: 'Test project for CRUD verification',
                brief: 'Test brief',
                status: 'active',
                is_archived: false,
                role_ids: '[1]',
                group_ids: '[1]',
                flow_ids: '[1]',
                category_ids: '[1]',
                user_access_ids: '[6,8]' // Test with some user IDs
            };
            
            const result = await apiCall('POST', '/projects', projectData);
            
            if (result.success) {
                resultDiv.innerHTML = `<div class="success">✓ Project created successfully!</div><pre>${JSON.stringify(result.data, null, 2)}</pre>`;
            } else {
                resultDiv.innerHTML = `<div class="error">✗ Error creating project</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
            }
        }
        
        async function testUpdateProject() {
            const projectId = document.getElementById('project-id').value;
            const resultDiv = document.getElementById('update-result');
            
            if (!projectId) {
                resultDiv.innerHTML = '<div class="error">Please enter a project ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Updating project...';
            
            const updateData = {
                name: 'Updated Project ' + Date.now(),
                description: 'Updated description',
                brief: 'Updated brief',
                status: 'active',
                is_archived: false,
                role_ids: '[1]',
                group_ids: '[1]',
                flow_ids: '[1]',
                category_ids: '[1]',
                user_access_ids: '[6,8]' // Updated user access
            };
            
            const result = await apiCall('PUT', `/projects/${projectId}`, updateData);
            
            if (result.success) {
                resultDiv.innerHTML = `<div class="success">✓ Project updated successfully!</div><pre>${JSON.stringify(result.data, null, 2)}</pre>`;
            } else {
                resultDiv.innerHTML = `<div class="error">✗ Error updating project</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
            }
        }
        
        async function testGetProject() {
            const projectId = document.getElementById('get-project-id').value;
            const resultDiv = document.getElementById('get-result');
            
            if (!projectId) {
                resultDiv.innerHTML = '<div class="error">Please enter a project ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Fetching project...';
            
            const result = await apiCall('GET', `/projects/${projectId}`);
            
            if (result.success) {
                resultDiv.innerHTML = `<div class="success">✓ Project fetched successfully!</div><pre>${JSON.stringify(result.data, null, 2)}</pre>`;
            } else {
                resultDiv.innerHTML = `<div class="error">✗ Error fetching project</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
            }
        }
    </script>
</body>
</html>
