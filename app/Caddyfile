# Listen on the port defined by the environment variable $PORT, or default to 80
:{$PORT:80} {
    # Set the root directory to where <PERSON><PERSON> builds your files
    root * /app/dist

    # Enable modern, efficient compression
    encode zstd gzip

    # Configure logging
    log {
        format json
    }

    # Health check endpoint for your hosting service (e.g., Dokploy, Railway)
    respond /health 200

    # --- Caching and File Serving ---
    # The 'route' block allows us to apply different rules to different files.
    route {
        # 1. For versioned assets in the /assets folder:
        #    Cache them for one year and mark them as immutable.
        @assets path /assets/*
        header @assets Cache-Control "public, must-revalidate, proxy-revalidate"

        # Generic static assets (css, js, images, icons)
        @static path_regexp static \.(?:ico|css|js|gif|jpe?g|png)$
        header @static Cache-Control "public, must-revalidate, proxy-revalidate"

        # 2. For the main index.html file:
        #    Do not cache it; always check for a new version.
        @index path /index.html
        header @index Cache-Control "no-cache, max-age=0, must-revalidate"

        # 3. Serve all other files from the file system.
        file_server {
            hide .git .env* # Hide sensitive files
        }
    }

    # --- Security Headers ---
    # These headers will be applied to all responses.
    header {
        # Prevent browsers from guessing the content type
        X-Content-Type-Options "nosniff"

        # Hide the server name for security
        -Server
    }

    # --- SPA Fallback ---
    # Serve index.html only for routes that do not look like a static file (no dot)
    @spa {
        not file              # Only when the requested file does not exist
        path_regexp noext ^[^.]+$  # And the URI has no file extension
    }
    rewrite @spa /index.html
}