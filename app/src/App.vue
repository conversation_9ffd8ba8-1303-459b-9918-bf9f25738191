<template>
	<span class="hidden">{{ $i18n.locale }}</span>
	<Layout>
		<router-view />
	</Layout>

	<!-- Vue-notifications left unchanged -->
	<notifications class="mb-[70px] rounded-lg mr-3 shadow-md" group="app" :ignoreDuplicates="true"
		position="bottom right" :max="3">
		<template #body="props">
			<div
				class="max-w-[26em] w-full bg-white rounded-lg pointer-events-auto border-1 overflow-hidden mt-2 z-[999]">
				<div class="p-4">
					<div class="flex items-start">
						<div class="flex">
							<CheckCircleIcon v-if="props.item.type==='success'" class="h-6 w-6 ml-1 text-green-600"
								aria-hidden="true" />
							<XCircleIcon v-if="props.item.type==='error'" class="h-6 w-6 ml-1 text-red-600"
								aria-hidden="true" />
							<InformationCircleIcon v-if="props.item.type==='warn'||props.item.type==='warning'"
								class="h-6 w-6 ml-1 text-orange-600" aria-hidden="true" />
							<div v-if="props.item.type==='register'">
								<svg style="width:24px;height:24px" viewBox="0 0 24 24">
									<path fill="#3fb14d"
										d="M21.1,12.5L22.5,13.91L15.97,20.5L12.5,17L13.9,15.59L15.97,17.67L21.1,12.5M10,17L13,20H3V18C3,15.79 6.58,14 11,14L12.89,14.11L10,17M11,4A4,4 0 0,1 15,8A4,4 0 0,1 11,12A4,4 0 0,1 7,8A4,4 0 0,1 11,4Z" />
								</svg>
							</div>
						</div>
						<div class="ml-3 w-0 flex-1 pt-0.5">
							<p class="text-sm text-gray-500" v-html="props.item.text" />
						</div>
						<button class="ml-auto" @click="props.close">
							<XIcon class="h-5 w-5 text-gray-400 hover:text-gray-500" />
						</button>
					</div>
				</div>
			</div>
		</template>
	</notifications>

	<!-- UPDATE TOAST -->
	<div aria-live="assertive"
		class="fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:items-start sm:p-6 z-50">
		<div class="w-full flex flex-col items-center space-y-4 sm:items-end">
			<transition enter-active-class="transform ease-out duration-300"
				enter-from-class="translate-y-2 opacity-0 sm:translate-x-2" enter-to-class="opacity-100"
				leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100"
				leave-to-class="opacity-0">
				<div v-if="showUpdateNotification"
					class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black/5">
					<div class="p-4 flex items-center">
						<RefreshIcon class="h-6 w-6 text-primary-500" />
						<div class="ml-3 flex-1">
							<p class="text-sm font-medium text-gray-900">New version available!</p>
							<p class="text-sm text-gray-500">Refresh to get the latest features.</p>
						</div>
						<button @click="refreshPage"
							class="ml-4 px-3 py-1.5 text-xs font-medium rounded-md bg-primary-600 text-white hover:bg-primary-700">
							Refresh
						</button>
						<button @click="showUpdateNotification=false" class="ml-2">
							<XIcon class="h-5 w-5 text-gray-400" />
						</button>
					</div>
				</div>
			</transition>
		</div>
	</div>
</template>

<script>
	import localforage from 'localforage';
	import {
		mapGetters,
		mapActions
	} from 'vuex';
	import useRegisterSW from '@/mixins/useRegisterSW';
	import Layout from '@/layouts/index.vue';
	import {
		CheckCircleIcon,
		XCircleIcon,
		InformationCircleIcon,
		RefreshIcon,
		XIcon
	} from '@heroicons/vue/outline';
	import TButton from '@/components/global/Button.vue';

	export default {
		name: 'App',
		components: {
			CheckCircleIcon,
			XCircleIcon,
			InformationCircleIcon,
			RefreshIcon,
			XIcon,
			Layout,
			TButton
		},
		mixins: [useRegisterSW],
		watch: {
			__needRefresh() {
				if (this.__needRefresh) {
					this.showUpdateNotification = true;
				}
			}
		},
		data() {
			return {
				showUpdateNotification: false
			};
		},

		computed: {
			...mapGetters({
				user: 'auth/user',
				getToken: 'auth/getToken',
				isShowOnboard: 'auth/getIsShowOnboard'
			})
		},

		mounted() {
			this.init();
			this.removeExpiredItems();

			// show toast on SW update
			document.addEventListener('swUpdated', () => {
				this.showUpdateNotification = true;
			});

			// in case the flag was already true
			if (this.__needRefresh) this.showUpdateNotification = true;
		},

		beforeUnmount() {
			document.removeEventListener('swUpdated', () => {});
		},

		methods: {
			...mapActions({
				fetchUser: 'auth/fetchUser',
				setUser: 'auth/setUser',
				setIsShowOnboard: 'auth/setIsShowOnboard'
			}),

			init() {
				const outside = this.$route.query.outside;
				const isOutside = typeof sceneIndex !== 'undefined' && outside === '1';
				if (this.getToken && !this.user && !isOutside) this.fetchUser();
			},

			refreshPage() {
				this.showUpdateNotification = false;
				this.__updateServiceWorker(); // from mixin
			},

			removeExpiredItems() {
				const now = Date.now();
				localforage.keys().then(keys =>
					keys.forEach(k =>
						localforage.getItem(k).then(item => {
							if (item?.expires && item.expires <= now) localforage.removeItem(k);
						})
					)
				);
			}
		}
	};
</script>

<style lang="scss">
	@import '@/assets/scss/main.scss';

	.flex-related {
		display: flex;
		flex-wrap: wrap;
	}

	.related-topics {
		height: 70.25px !important;
	}

	.line-break-related {
		width: 100%;
	}
</style>