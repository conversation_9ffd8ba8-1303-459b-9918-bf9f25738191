.multiselect{
    font-size: 13px !important;
    font-style: normal !important;
    [type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
        --tw-ring-color: white;
    }
}
.multiselect__single {
    font-size: 13px !important;
    font-style: normal !important;
}
.multiselect__tags {
    font-size: 13px !important;
    font-style: normal !important;
    border: 1px solid #d1d5db !important;
    // box-shadow: 2px 2px 10px  #d1d5db!important;
}

.multiselect--active .multiselect__input {
    font-size: 13px !important;
    font-style: normal !important;
    &:focus {
        outline: none !important;
    }
}

.multiselect__option--highlight {
    background: #d73502 !important;
}

.multiselect__option--highlight:after {
    background: #d73502 !important;
}

.multiselect__tag {
    background: #d73502 !important;
    padding-bottom: 6px!important;

}

.multiselect__tag-icon:after {
    color: white !important;
}

.multiselect--disabled {
	background-color: #F4F4F4!important;
  .multiselect__input, .multiselect__single {
    background-color: #F4F4F4!important;
  }
  .multiselect__tags {
    background-color: #F4F4F4!important;
  }
}
