.tooltip {
	// display: block !important;
	z-index: 10000;
	max-width: 170px;
	text-align: center;
	position: absolute!important;
	font-size: 12px;
	
  
	.tooltip-inner {
	  background: white;
	//   color: white;
	  padding: 10px;
	  border: 1px solid #808080!important;
	border-radius: 4px;
	}
  
	.tooltip-arrow {
	  width: 0;
	  height: 0;
	  position: absolute;
	  margin: 5px;
	  background: white;
	  border-color: #808080;
	  z-index: 1;
	}

	&[x-placement^="top"] {
	  margin-bottom: 5px;

	  .tooltip-arrow {
		border-width: 5px 5px 0 5px;
		border-left-color: transparent !important;
		border-right-color: transparent !important;
		border-bottom-color: transparent !important;
		bottom: -5px;
		left: calc(50% - 5px);
		margin-top: 0;
		margin-bottom: 0;
	  }
	}
  
	&[x-placement^="bottom"] {
	  margin-top: 5px;
  
	  .tooltip-arrow {
		border-width: 0 5px 5px 5px;
		border-left-color: transparent !important;
		border-right-color: transparent !important;
		border-top-color: transparent !important;
		top: -5px;
		left: calc(50% - 5px);
		margin-top: 0;
		margin-bottom: 0;
	  }
	}
  
	&[x-placement^="right"] {
	  margin-left: 5px;
  
	  .tooltip-arrow {
		border-width: 5px 5px 5px 0;
		border-left-color: transparent !important;
		border-top-color: transparent !important;
		border-bottom-color: transparent !important;
		left: -5px;
		top: calc(50% - 5px);
		margin-left: 0;
		margin-right: 0;
	  }
	}
  
	&[x-placement^="left"] {
	  margin-right: 5px;
  
	  .tooltip-arrow {
		border-width: 5px 0 5px 5px;
		border-top-color: transparent !important;
		border-right-color: transparent !important;
		border-bottom-color: transparent !important;
		right: -5px;
		top: calc(50% - 5px);
		margin-left: 0;
		margin-right: 0;
	  }
	}
  
	&.popover {
	  $color: #f9f9f9;
  
	  .popover-inner {
		background: $color;
		color: white;
		padding: 24px;
		border-radius: 5px;
		box-shadow: 0 5px 30px rgba(white, .1);
	  }
  
	  .popover-arrow {
		border-color: $color;
	  }
	}
  
	&[aria-hidden='true'] {
	  visibility: hidden;
	  opacity: 0;
	  transition: all .5s ease-in-out !important;
	}
  
	&[aria-hidden='false'] {
	  visibility: visible;
	  opacity: 1;
	  transition: all .5s ease-in-out !important;
	}
  }

.tooltip-long {
  // display: block !important;
  z-index: 10000;
  max-width: 300px;
  word-break: break-all;
  text-align: center;
  position: absolute!important;
  font-size: 12px;
  
  
  .tooltip-long-inner {
    background: white;
  //   color: white;
    padding: 10px;
    border: 1px solid #808080!important;
  border-radius: 4px;
  }
  
  .tooltip-long-arrow {
    width: 0;
    height: 0;
    position: absolute;
    margin: 5px;
    background: white;
    border-color: #808080;
    z-index: 1;
  }

  &[x-placement^="top"] {
    margin-bottom: 5px;

    .tooltip-long-arrow {
    border-width: 5px 5px 0 5px;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    bottom: -5px;
    left: calc(50% - 5px);
    margin-top: 0;
    margin-bottom: 0;
    }
  }
  
  &[x-placement^="bottom"] {
    margin-top: 5px;
  
    .tooltip-long-arrow {
    border-width: 0 5px 5px 5px;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
    border-top-color: transparent !important;
    top: -5px;
    left: calc(50% - 5px);
    margin-top: 0;
    margin-bottom: 0;
    }
  }
  
  &[x-placement^="right"] {
    margin-left: 5px;
  
    .tooltip-long-arrow {
    border-width: 5px 5px 5px 0;
    border-left-color: transparent !important;
    border-top-color: transparent !important;
    border-bottom-color: transparent !important;
    left: -5px;
    top: calc(50% - 5px);
    margin-left: 0;
    margin-right: 0;
    }
  }
  
  &[x-placement^="left"] {
    margin-right: 5px;
  
    .tooltip-long-arrow {
    border-width: 5px 0 5px 5px;
    border-top-color: transparent !important;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    right: -5px;
    top: calc(50% - 5px);
    margin-left: 0;
    margin-right: 0;
    }
  }
  
  &.popover {
    $color: #f9f9f9;
  
    .popover-inner {
    background: $color;
    color: white;
    padding: 24px;
    border-radius: 5px;
    box-shadow: 0 5px 30px rgba(white, .1);
    }
  
    .popover-arrow {
    border-color: $color;
    }
  }
  
  &[aria-hidden='true'] {
    visibility: hidden;
    opacity: 0;
    transition: all .5s ease-in-out !important;
  }
  
  &[aria-hidden='false'] {
    visibility: visible;
    opacity: 1;
    transition: all .5s ease-in-out !important;
  }
}


//   toltip from scratch
  /**
 * Demo styles
 * Not needed for tooltips to work
 */

/* `border-box`... ALL THE THINGS! */

  /* Base styles for the element that has a tooltip */
  [data-tooltip],
  .tooltip {
	position: relative;
	cursor: pointer;
  }
  
  /* Base styles for the entire tooltip */
  [data-tooltip]:before,
  [data-tooltip]:after,
  .tooltip:before,
  .tooltip:after {
	position: absolute;
	visibility: hidden;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
	opacity: 0;
	-webkit-transition: 
		opacity 0.2s ease-in-out,
		  visibility 0.2s ease-in-out,
		  -webkit-transform 0.2s cubic-bezier(0.71, 1.7, 0.77, 1.24);
	  -moz-transition:    
		  opacity 0.2s ease-in-out,
		  visibility 0.2s ease-in-out,
		  -moz-transform 0.2s cubic-bezier(0.71, 1.7, 0.77, 1.24);
	  transition:         
		  opacity 0.2s ease-in-out,
		  visibility 0.2s ease-in-out,
		  transform 0.2s cubic-bezier(0.71, 1.7, 0.77, 1.24);
	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform:    translate3d(0, 0, 0);
	transform:         translate3d(0, 0, 0);
	pointer-events: none;
  }
  
  /* Show the entire tooltip on hover and focus */
  [data-tooltip]:hover:before,
  [data-tooltip]:hover:after,
  [data-tooltip]:focus:before,
  [data-tooltip]:focus:after,
  .tooltip:hover:before,
  .tooltip:hover:after,
  .tooltip:focus:before,
  .tooltip:focus:after {
	visibility: visible;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
	opacity: 1;
  }
  
  /* Base styles for the tooltip's directional arrow */
  .tooltip:before,
  [data-tooltip]:before {
	z-index: 1001;
	border: 6px solid transparent;
	background: transparent;
	content: "";
  }
  
  /* Base styles for the tooltip's content area */
  .tooltip:after,
  [data-tooltip]:after {
	z-index: 1000;
	padding: 8px;
	width: 160px;
	background-color: white;
	color:#808080;
	content: attr(data-tooltip);
	font-size: 14px;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	border-radius: 4px;
	border: 1px solid #808080;
  }
  
  /* Directions */
  
  /* Top (default) */
  [data-tooltip]:before,
  [data-tooltip]:after,
  .tooltip:before,
  .tooltip:after,
  .tooltip-top:before,
  .tooltip-top:after {
	bottom: 100%;
	left: 50%;
  }
  
  [data-tooltip]:before,
  .tooltip:before,
  .tooltip-top:before {
	margin-left: -6px;
	margin-bottom: -12px;
	border-top-color: #808080;
  }
  
  /* Horizontally align top/bottom tooltips */
  [data-tooltip]:after,
  .tooltip:after,
  .tooltip-top:after {
	margin-left: -80px;
  }
  
  [data-tooltip]:hover:before,
  [data-tooltip]:hover:after,
  [data-tooltip]:focus:before,
  [data-tooltip]:focus:after,
  .tooltip:hover:before,
  .tooltip:hover:after,
  .tooltip:focus:before,
  .tooltip:focus:after,
  .tooltip-top:hover:before,
  .tooltip-top:hover:after,
  .tooltip-top:focus:before,
  .tooltip-top:focus:after {
	-webkit-transform: translateY(-12px);
	-moz-transform:    translateY(-12px);
	transform:         translateY(-12px);
	transition-delay:1s;
  }
  
  /* Left */
  .tooltip-left:before,
  .tooltip-left:after {
	right: 100%;
	bottom: 50%;
	left: auto;
  }
  
  .tooltip-left:before {
	margin-left: 0;
	margin-right: -12px;
	margin-bottom: 0;
	border-top-color: transparent;
	border-left-color: #808080;
  }
  
  .tooltip-left:hover:before,
  .tooltip-left:hover:after,
  .tooltip-left:focus:before,
  .tooltip-left:focus:after {
	-webkit-transform: translateX(-12px);
	-moz-transform:    translateX(-12px);
	transform:         translateX(-12px); 
	transition-delay:1s;
  }
  
  /* Bottom */
  .tooltip-bottom:before,
  .tooltip-bottom:after {
	top: 100%;
	bottom: auto;
	left: 50%;
  }
  
  .tooltip-bottom:before {
	margin-top: -12px;
	margin-bottom: 0;
	border-top-color: transparent;
	border-bottom-color: #808080;
  }
  
  .tooltip-bottom:hover:before,
  .tooltip-bottom:hover:after,
  .tooltip-bottom:focus:before,
  .tooltip-bottom:focus:after {
	-webkit-transform: translateY(12px);
	-moz-transform:    translateY(12px);
	transform:         translateY(12px); 
	transition-delay:1s;
  }
  
  /* Right */
  .tooltip-right:before,
  .tooltip-right:after {
	bottom: 50%;
	left: 100%;
  }
  
  .tooltip-right:before {
	margin-bottom: 0;
	margin-left: -12px;
	border-top-color: transparent;
	border-right-color: #808080;
  }
  
  .tooltip-right:hover:before,
  .tooltip-right:hover:after,
  .tooltip-right:focus:before,
  .tooltip-right:focus:after {
	-webkit-transform: translateX(12px);
	-moz-transform:    translateX(12px);
	transform:         translateX(12px);
	transition-delay:1s;
  }
  
  /* Move directional arrows down a bit for left/right tooltips */
  .tooltip-left:before,
  .tooltip-right:before {
	top: 3px;
  }
  
  /* Vertically center tooltip content for left/right tooltips */
  .tooltip-left:after,
  .tooltip-right:after {
	margin-left: 0;
	margin-bottom: -16px;
  }


// Tooltip
.master-tooltip {
	cursor: pointer; 
	cursor: hand;
}
.tooltip {
	display: none;
	position: absolute;
	border:1px solid#333;
	border-style: solid;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 3px;
	padding-top: 0.8em;
  	padding-bottom: 0.8em;
	padding-left: .8em;
	padding-right: .8em;
	font-size: 13px;
	line-height: 1.2;
	text-align:center;
	color: #333;
}

.tooltip-long {
	display: none;
	position: absolute;
	border:1px solid#333;
	border-style: solid;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 3px;
	padding-top: 0.8em;
  	padding-bottom: 0.8em;
	padding-left: .8em;
	padding-right: .8em;
	font-size: 13px;
	line-height: 1.2;
	text-align:center;
	color: #333;
}