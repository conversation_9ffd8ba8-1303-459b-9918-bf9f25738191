<template>
	<!-- Loader -->
	<loader-circle v-if="isFetchingProject" />
  <div class="p-10">
    <!-- title -->
    <div class="text-2xl font-medium">
      {{ getActiveProject?.name }} | {{ getActiveProject?.booking?.invoiceNumber }}
    </div>
    <!-- studio -->
    <div class="text-lg mt-2">
      {{ getActiveProject?.packages?.studio }}
    </div>
    <!-- date -->
    <div class="text-lg mt-2" v-html="__dateFormatOverview(getActiveProject?.startDate, project?.duration)">
    </div>
    <!-- client info -->
    <div class="font-medium mt-10">{{ $t('Client Information') }}</div>
      <div class="flex flex-col md:flex-row items-left justify-between py-4 max-w-[880px]">
      <!-- User Avatar and Info -->
      <div class="flex items-center mb-4 md:mb-0">
        <!-- Avatar or Initial -->
        <div class="flex-shrink-0">
          <img v-if="getActiveProject?.user && getActiveProject?.user.imgUrl"
            class="rounded-full h-10 w-10 object-cover"
            :src="getActiveProject?.user.imgUrl"
            alt="avatar-image"
            referrerpolicy="no-referrer"
            @error="handleAvatarError(getActiveProject?.user)">
          <div v-else>
            <div
              :style="{ backgroundColor: __getColorByInitial(getActiveProject?.user?.first_name?.[0] || '') }"
              class="text-md rounded-full h-10 w-10 font-medium flex items-center justify-center bg-gray-200 uppercase">
              {{ __generateInitial(getActiveProject?.user?.first_name) }}
            </div>
          </div>
        </div>

        <!-- User Info -->
        <div class="ml-3">
          <div class="text-sm font-medium text-gray-900">
            {{ getActiveProject?.user.first_name }}
          </div>
          <div class="text-sm text-gray-500">
            {{ getActiveProject?.user.email }}
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-3 md:space-x-3 md:mt-0 mt-4">
        <t-button :color="`primary-white`" class="flex items-center text-black px-3 py-1 border border-gray-300 rounded-md">
          <MailIcon class="h-5 w-5 text-gray-400"></MailIcon>
          <div class="font-medium ml-2">{{ $t('Send Email') }}</div>
        </t-button>
        <t-button :color="`primary-white`" class="flex items-center text-black px-3 py-1 border border-gray-300 rounded-md">
          <PhoneIcon class="h-5 w-5 text-gray-400"></PhoneIcon>
          <div class="font-medium ml-2">{{ $t('Call') }}</div>
        </t-button>
      </div>
    </div>

    <!-- collaborators -->
    <div class="max-w-screen-lg mt-10">
      <div class="font-medium mb-4">Collaborators</div>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <div v-for="collaborator in collaborators" :key="collaborator.id" class="flex items-center">
          <!-- Avatar or Initial -->
          <div class="relative flex-shrink-0">
            <img v-if="collaborator && collaborator.imgUrl"
              class="rounded-full h-10 w-10 object-cover"
              :src="collaborator.imgUrl"
              alt="avatar-image"
              referrerpolicy="no-referrer"
              @error="handleAvatarError(collaborator)">
            <div v-else>
              <div
                :style="{ backgroundColor: __getColorByInitial(collaborator?.first_name?.[0] || '') }"
                class="text-md rounded-full h-10 w-10 font-medium flex items-center justify-center bg-gray-200 uppercase">
                {{ __generateInitial(collaborator?.first_name) }}
              </div>
            </div>
            <!-- Close button in the top right corner -->
            <div v-if="(getActiveProject.user.email !== collaborator.email && collaborator?.role?.id !== 1)" @click="deleteCollaborator(collaborator)" 
              class="absolute top-0 pointer right-0 p-1 bg-red-600 rounded-full text-white w-[19px] h-[19px] mt-[-3px] mr-[-2px]">
              <div class="mt-[-8px]">&times;</div>
            </div>
          </div>

          <!-- Collaborator Name -->
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-800">
              {{ collaborator.first_name }}
            </div>
            <div class="text-sm text-gray-400">
              {{ collaborator.email }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- brief -->
    <div class="mt-16 flex items-center">
      <div class="font-medium mr-6">{{ $t('Brief') }}</div>
      <div>
        <t-button @click="editBrief()" :color="`primary-white`" :isDisabled="isSaving" :isLoading="isSaving" class="h-[30px] px-5">
          <div class="font-medium">{{ isEditing ? $t('Save Changes') : $t('Edit') }}</div>
        </t-button>
      </div>
    </div>
    <div>
      <div>
        <!-- Display the brief, clicking it shows the textarea -->
        <div v-if="!isEditing" class="max-w-[880px] mt-4">
          {{ brief || 'Click to provide a brief overview of this project' }}
        </div>

        <!-- Textarea shown when isEditing is true -->
        <div v-show="isEditing" class="max-w-[880px] mt-4" ref="textareaWrapper">
          <t-textarea
            ref="textarea"
            v-model="brief"
            :value="brief"
            class="bg-transparent"
            rows="6"
            placeholder="Provide a brief overview of this project"
            @click.stop
          ></t-textarea>
        </div>
      </div>
    </div>
  </div>

  <ModalGeneral :isShow="isModalDeleteOpen" @update:isShow="isModalDeleteOpen = $event">
    <template #header>
      <p class="text-base font-medium text-gray-800 dark:text-white">
        {{$t('Remove collaborator')}}
      </p>
    </template>
    <template #body>
      <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
      <p class="text-sm text-gray-800 dark:text-neutral-400"> {{ `${$t('Are you sure want to remove')} ${selectedCollaborator.first_name} (${selectedCollaborator.email}) ?`}} </p>
      <!-- </div> -->
    </template>
    <template #footer>
      <t-button :color="`secondary-solid`" @click="isModalDeleteOpen = false;">
        {{ $t('Cancel') }}
      </t-button>
      <t-button :color="`red-solid`" @click="confirmDelete()">
        {{ $t('Delete') }}
      </t-button>
    </template>
  </ModalGeneral>
</template>

<script>
import ModalGeneral from "@/components/modal/ModalGeneral.vue";
import { onClickOutside } from '@vueuse/core';
import TTextarea from '@/components/form/Textarea.vue';
import {
	BriefcaseIcon,
	DownloadIcon,
	UserGroupIcon,
	XIcon,
  PhoneIcon,
  MailIcon,
} from '@heroicons/vue/solid';

import { mapGetters, mapActions } from 'vuex';
import TModal from '@/components/global/Modal.vue';
import projectApi from "@/api/project";

export default {
	components: {
		BriefcaseIcon,
		DownloadIcon,
		UserGroupIcon,
		XIcon,
		TModal,
    PhoneIcon,
    MailIcon,
    TTextarea,
    ModalGeneral
	},
	data() {
		return {
      collaborators: [],
      isSaving: false,
      brief: '',
      isEditing: false,
      projectSlug: null,
      projectId: null,
      isModalDeleteOpen: false,
      selectedCollaborator: null,
		};
	},
	computed: {
		...mapGetters({
      getActiveProject: 'application/getActiveProject',
			user: 'auth/user',
			isAdmin: 'auth/isAdmin',
			isClient: 'auth/isClient',
		}),
    currentId() {
      return this.$route.params.id;
    }
	},
	created() {
    this.setDataUrl()
	},
  watch: {
    currentId() {
      this.setDataUrl()
    },
  },
	mounted() {
    // // We are now targeting the wrapper div around the textarea
    // const textareaWrapperElement = this.$refs.textareaWrapper;

    // // onClickOutside from VueUse to detect outside clicks
    // onClickOutside(textareaWrapperElement, () => {
    //   this.isEditing = false; // Close textarea on outside click
    // });
  },
  beforeUnmount() {
    // Clean up any listeners before unmounting the component
    // document.removeEventListener('click', this.handleClickOutside);
  },
	methods: {
		...mapActions({
			fetchUser: 'auth/fetchUser',
      setDataProject: 'application/setDataProject',
		}),
    setDataUrl() {
      const encryptedId = this.$route.params.id; // Get the encrypted string from route params
      const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data
      
      if (decryptedData) {
        this.projectId = decryptedData.id;   // Access the original project ID
        this.projectSlug = decryptedData.slug; // Access the original project slug
      }
      this.getProject()
    },
    // Toggle the textarea visibility
    toggleTextarea() {
      this.isEditing = true;
      // Attach click event listener for detecting outside clicks
      // document.addEventListener('click', this.handleClickOutside);
    },
    // Function to close the textarea when clicked outside
    handleClickOutside(event) {
      console.log(event);
      const textareaElement = this.$refs.textarea?.$el || this.$refs.textarea;
      console.log(textareaElement);
      if (textareaElement && !textareaElement.contains(event.target)) {
        this.isEditing = false;
        // Remove the click listener after hiding the textarea
        document.removeEventListener('click', this.handleClickOutside);
      }
    },
    closeAction(collab) {

    },
    getProject() {
      const callback = (response) => {
        const data = response.data;
        this.setDataProject(data);
        this.brief = data.brief;
        this.getCollaborators()
      };
      const errCallback = (err) => {
        console.log(err);
      };
      projectApi.get(this.projectId, callback, errCallback);
    },
    getCollaborators() {
      const callback = (response) => {
        const data = response.data;
        this.collaborators = data;
      };
      const errCallback = (err) => {
        console.log(err);
      };
      projectApi.getCollaborators(this.getActiveProject?.id, callback, errCallback);
    },
    editBrief() {
      if (!this.isEditing) {
        this.isEditing = true;
        return
      }
      this.isSaving = true;
      const callback = (response) => {
        const data = response.data;
        const message = response.message;
        this.brief = data.brief;
        this.__showNotif('success', 'Success', message);
        this.isSaving = false;
        this.isEditing = false
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
        this.handleErrors(err.response.data)
        this.isSaving = false;
      }
      const params = {
        brief: this.brief
      }
      if (this.getActiveProject?.id) projectApi.update(this.getActiveProject.id, params, callback, errCallback)
    },
    deleteCollaborator(item) {
      this.isModalDeleteOpen = true;
      this.selectedCollaborator = item
    },
    confirmDelete() {
      if (this.selectedCollaborator) {
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.selectedCollaborator = null;
          this.getProject();
          this.isModalDeleteOpen = false;
          this.__showNotif('success', 'Success', message);
        }
        const errCallback = (err) => {
          console.log(err)
        }
        const id = this.projectId
        const params = {
          userId: this.selectedCollaborator.id
        }
        projectApi.deleteCollab(id, params, callback, errCallback)
      }
    },
	},
};
</script>