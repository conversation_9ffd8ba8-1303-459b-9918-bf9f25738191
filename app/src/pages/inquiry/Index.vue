<template>
  <div>
    <!-- Table Section -->
    <!-- serach -->
    <loader-circle v-if="isFetching" />
    <div class="flex items-center justify-between truncate px-4 pt-2">
      <nav class="flex gap-x-6 ml-6">
        <router-link
          :class="{'border-primary-600 border-b-2': $route.path.includes(`/inquiries/${__encryptProjectData(getActiveProject?.id, getActiveProject?.slug)}`)}"
          class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
          :to="`/inquiries/${__encryptProjectData(getActiveProject?.id, getActiveProject?.slug)}`">
          {{$t('List')}}
        </router-link>
        <router-link
          :class="{'border-primary-600 border-b-2': $route.path.includes(`/e/kanban/${__encryptProjectData(getActiveProject?.id, getActiveProject?.slug)}`)}"
          class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap  hover:text-primary-600"
          :to="`/e/kanban/${__encryptProjectData(getActiveProject?.id, getActiveProject?.slug)}`">
          {{$t('Board')}}
        </router-link>
      </nav>
      <div class="flex items-center gap-x-2">
        <div class="relative">
          <input
            v-model="keyword"
            @keyup.enter="getInquiries"
            type="text"
            class="py-2 px-3 ps-9 block w-full border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400 dark:focus:ring-gray-600"
            placeholder="Search for inquiries">
          <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>
          </div>
        </div>
        <ButtonDropdown
          :items="sortOptions"
          @item-selected="handleSortSelection"
          :selected-item="selectedSortOption"
          class="w-auto"
        />
      </div>
    </div>

    <div v-if="!isFetching"
      class="overflow-x-auto mt-6 [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
      <div class="min-w-full inline-block align-middle">
        <!-- Table -->
        <table class="min-w-full">
          <thead class="border-t border-b border-gray-200">
            <tr class="">
              <th scope="col" class="px-3 py-2.5 text-start">
              </th>

              <th scope="col" class="min-w-80">
                <!-- Sort Dropdown -->
                <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                  <button id="hs-pro-dutnms" type="button"
                    class="px-2 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                    aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    {{ $t('Task Name') }}
                  </button>
                </div>
                <!-- End Sort Dropdown -->
              </th>

              <th scope="col" class="min-w-40">
                <!-- Sort Dropdown -->
                <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                  <button id="hs-pro-dutads" type="button"
                    class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                    aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    {{ $t('Due Date') }}
                  </button>
                </div>
                <!-- End Sort Dropdown -->
              </th>

              <th scope="col" class="min-w-80">
                <!-- Sort Dropdown -->
                <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                  <button id="hs-pro-dutsgs" type="button"
                    class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                    aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    {{ $t('Collaborators') }}
                  </button>
                </div>
                <!-- End Sort Dropdown -->
              </th>
            </tr>
          </thead>

          <tbody class="">
            <tr class="group border-b border-gray-200 hover:bg-slate-100 pointer" v-for="(inquiry, index) in inquiries" :key="inquiry.id" @click="goToDetail(inquiry)">
              <td class="pl-6 whitespace-nowrap py-4 w-[50px]">
                <div class="flex items-center justify-between">
                  <span>{{ (page - 1) * 10 + (index + 1) }}</span>
                </div>
              </td>
              <td class=" whitespace-nowrap px-3 py-1">

                <div class="text-sm text-gray-600 flex items-center flex justify-between">
                <div class="flex items-center gap-x-2">
                  <CheckCircleIcon :class="{'text-primary-600': inquiry.status === 'completed'}" @click.stop="updateComplete(inquiry)" class="mr-2 h-6 w-6 pointer text-gray-400" aria-hidden="true"></CheckCircleIcon>
                  {{inquiry.title || '-'}}
                </div>

                <!-- Actions Dropdown - Only visible on hover -->
                <!-- <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200" @click.stop>
                  <ButtonDropdown :customClassItems="'w-32'">
                    <template #button>
                      <button class="flex items-center justify-center size-[30px] text-sm font-medium rounded-md text-gray-400 hover:text-gray-600 focus:outline-none">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <circle cx="12" cy="12" r="1"/>
                          <circle cx="12" cy="5" r="1"/>
                          <circle cx="12" cy="19" r="1"/>
                        </svg>
                      </button>
                    </template>
                    <template #items>
                      <button @click="goToDetail(inquiry)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                        View Details
                      </button>
                      <button @click="updateComplete(inquiry)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                        {{ inquiry.status === 'completed' ? 'Mark Incomplete' : 'Mark Complete' }}
                      </button>
                    </template>
                  </ButtonDropdown>
                </div> -->
                </div>
              </td>
              <td class=" whitespace-nowrap px-6 py-1">
                <span class="text-sm text-gray-600">
                  {{ inquiry.due_date ? __dateHumanizeText(inquiry.due_date) : '-'}}
                </span>
              </td>
              <td class=" whitespace-nowrap px-4 py-1">
                <!-- <div class="inline-flex items-center px-3 py-1 bg-[#DEDEDE] rounded-lg max-w-xs text-xs">
                  <span class="truncate">
                    {{ inquiry.project.name }}
                  </span>
                </div> -->
              </td>
            </tr>
          </tbody>
        </table>
        <!-- End Table -->
      </div>
    </div>
    <!-- Modern Pagination -->
    <ModernPagination
      v-if="!isFetching && inquiries.length"
      :total="total"
      :current-page="page"
      :last-page="maxPage"
      :per-page="limit"
      :is-fetching="isFetching"
      @page-changed="onPageChanged"
      @per-page-changed="onPerPageChanged"
    />
    <div v-if="!isFetching && !inquiries.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-10">
      {{ $t('No records found') }}
    </div>
    <!-- End Table Section -->
    <ModalContainer />
    <!-- End Table Section -->
  </div>
</template>

<script>
import { delay } from '@/libraries/helper';
import { mapGetters, mapActions } from 'vuex';
import inquiriesApi from "@/api/inquiries";
import projectApi from "@/api/project";
import ModernPagination from '@/components/global/ModernPagination.vue';
import ButtonDropdown from '@/components/global/ButtonDropdown.vue';

import {
  ExternalLinkIcon,
  XIcon,
  LinkIcon,
  DotsHorizontalIcon,
  ThumbUpIcon,
  CheckCircleIcon,
  CalendarIcon
} from '@heroicons/vue/outline';
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
components: {
  CheckCircleIcon,
  ModernPagination,
  ButtonDropdown,
},
props: {
},
data() {
  return {
    inquiries: [],
    project: null,
    isFetching: false,
    keyword: '',
    page: 1,
    maxPage: 1,
    total: 0,
    limit: 10,
    meta: null,
    order_by: 'created_at',
    sort_by: 'desc',
    sortOptions: [
      { id: 'name_asc', label: 'Name (A-Z)', order_by: 'name', sort_by: 'asc' },
      { id: 'name_desc', label: 'Name (Z-A)', order_by: 'name', sort_by: 'desc' },
      { id: 'created_asc', label: 'Oldest First', order_by: 'created_at', sort_by: 'asc' },
      { id: 'created_desc', label: 'Newest First', order_by: 'created_at', sort_by: 'desc' },
    ],
    selectedSortOption: null,
    projectId: null,
    projectSlug: null,
  };
},
computed: {
  ...mapGetters({
    getActiveProject: 'application/getActiveProject',
  }),
  projectId() {
    return this.$route.params.id;
  },
},
watch: {
  '$route.params.slug'() {
    if (this.$route.params?.slug) {
      this.showDetailInquiry(atob(this.$route.params.slug));
    }
  },
  '$route.params.id': {
      immediate: true,
      handler(newId, oldId) {
        if (newId && newId !== oldId) {
          const decryptedData = this.__decryptProjectData(newId);
          if (decryptedData) {
            this.projectId = decryptedData.id;
            this.projectSlug = decryptedData.slug;
            this.getProject();
          }
        }
      }
    },
},
created() {
  this.selectedSortOption = this.sortOptions.find(option => 
    option.order_by === this.order_by && option.sort_by === this.sort_by
  );
  const encryptedId = this.$route.params.id; // Get the encrypted string from route params
  const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data
  if (decryptedData) {
    this.projectId = decryptedData.id;   // Access the original project ID
    this.projectSlug = decryptedData.slug; // Access the original project slug
  }
},
mounted() {
  this.getProject();
  if (this.$route.params?.slug) {
    this.showDetailInquiry(atob(this.$route.params.slug));
  }
},
beforeUnmount() {},
methods: {
  ...mapActions({
    showDetailInquiry: 'application/showDetailInquiry',
    resetStore: 'application/resetStore',
    setDataProject: 'application/setDataProject',
  }),
  getProject() {
    const callback = (response) => {
      const data = response.data;
      // Check if no project
      if (!data) {
        this.__showNotif('error', 'Project not found', "We're unable to locate the project you're looking for. It may have been moved or deleted.");
        this.$router.push({ name: 'Home' });
      }
      this.project = data;
      const roomId = data.slug;
      this.$soketio.emit('join', roomId);
      this.setDataProject(this.project);
      this.getInquiries();
    };
    const errCallback = (err) => {
      console.log(err);
    };
    projectApi.get(this.projectId, callback, errCallback);
    },
  nextPage() {
    this.page = this.page + 1;
    this.getInquiries()
  },
  prevPage() {
    this.page = this.page - 1;
    this.getInquiries()
  },
  handlePageChange(newPage) {
    this.page = newPage;
    this.getInquiries();
  },
  handleSortSelection(option) {
    this.selectedSortOption = option;
    this.order_by = option.order_by;
    this.sort_by = option.sort_by;
    this.page = 1; // Reset to first page when sorting
    this.getInquiries();
  },
  async getInquiries() {
    this.isFetching = true;
    const callback = (response) => {
      const data = response.data;
      const meta = response.meta;
      this.inquiries = data;
      this.meta = meta;
      this.page = meta.current_page;
      this.maxPage = meta.last_page;
      this.total = meta.total;
      this.isFetching = false;
    };
    const errCallback = (err) => {
      console.log(err);
      this.isFetching = false;
    };
    const params = {
      order_by: this.order_by,
      sort_by: this.sort_by,
      page: this.page,
      limit: this.limit,
      project_id: this.projectId,
    }
    if (this.keyword) {
      params.keyword = this.keyword
    } 
    inquiriesApi.getList(params, callback, errCallback);
  },
  updateComplete(inquiry) {
    const callback = (response) => {
      const data = response.data;
      const index = this.inquiries.findIndex(item => item.id === data.id);
      if (index !== -1) {
        // Replace the old item with the updated one
        Object.assign(this.inquiries[index], data);
      } else {
        console.log('Item not found');
      }
    };
    let params = {
      status: !inquiry.status || inquiry.status === 'incomplete' ? 'complete' : 'incomplete'
    }

    // Make the API call to update the inquiry on the server
    inquiriesApi.completeInquiry(inquiry.id, params, callback, (error) => {
      console.error('Error updating inquiry:', error);
    });
  },
  goToDetail(inquiry) {
      this.resetStore()
      const baseURL = import.meta.env.VITE_APP_URL; // Get the base URL from environment variables
      this.$router.push(`/inquiries/${this.$route.params.id}/${btoa(inquiry.slug)}`) // Combine base URL and current route
      setTimeout(() => {
        this.showDetailInquiry(inquiry.slug);
      }, 500);
  },
},
};
</script>
