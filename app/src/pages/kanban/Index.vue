<template>
  <div>
    <loader-circle v-if="isFetching" />
    <main :key="currentId" :class="classMain" class="bg-white dark:bg-kb_black overflow-y-auto overflow-x-auto pl-8">
      <div class="flex justify-between items-center pt-6 pr-6">
        <nav class="-mb-0.5 flex gap-x-6 ml-6" v-if="getActiveProject?.name && $route.name !== 'MyInquiry' && $route.name !== 'InquiryDetailFull' && $route.name !== 'InquiryDetail'">
          <router-link
            :class="{'border-primary-600 border-b-2': $route.path.includes(`/inquiries/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`)}"
            class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
            :to="`/inquiries/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`">
            {{$t('List')}}
          </router-link>
          <router-link
            :class="{'border-primary-600 border-b-2': $route.path.includes(`/e/kanban/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`)}"
            class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap  hover:text-primary-600"
            :to="`/e/kanban/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`">
            {{$t('Board')}}
          </router-link>
        </nav>
      </div>
      <div>
        <ShowMain v-if="!isFetching" @columnChange="columnChange" class="relative" />
      </div>
    </main>
    <ModalContainer />
  </div>
</template>

<script>
import {
    mapGetters,
    mapActions
  } from 'vuex';
import store from "./store";
import inquiriesApi from "@/api/inquiries";
import HeaderBar from "@/components/kanban/Header/HeaderBar.vue";
import ShowMain from "@/components/kanban/Main/ShowMain.vue";
import ModalContainer from "@/components/kanban/ModalContainer.vue";
import projectApi from "@/api/project";
import kanbanApi from "@/api/kanban";
import TButton from '@/components/global/Button.vue';
import { HSStaticMethods } from "preline";
import { delay } from '@/libraries/helper';
import TCheckbox from '@/components/form/Checkbox.vue';

export default {
  components: {
    ShowMain,
    HeaderBar,
    ModalContainer,
    TButton,
    TCheckbox
  },
  data() {
    return {
      rando: Math.random(1,100),
      isDataOk: false,
      project: null,
      inquiries: [],
      keyword: '',
      kanbanColumns: [],
      isFetching: true,
      isLoadingProject: false,
      isLoadingColumns: false,
      isLoadingInquiries: false,
      columnsPromise: null,
      dataKanban: {
        "boards": [{
            "name": "Kanban Desidia",
            "columns": [
              { "name": "Pre Case", "inquiries": [] },
            ]
          }
        ]
      },
      selectedStatuses: {
          isMyInquiry: false,
          isCompleteInquiry: false,
          isIncompleteInquiry: false,
          isDueThisWeek: false,
          isDueNextWeek: false,
        },
      projectSlug: null,
      projectId: null,
    };
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveInquiry: 'application/getActiveInquiry',
    }),
    classHeader() {
      if (this.wWidth < 768) {
        return "left-0 ";
      }
    },
    classAside() {
      if (this.wWidth < 768) {
        return "hidden ";
      }
      if (this.hideAside) {
        return "-translate-x-[100rem]";
      }
    },
    currentId() {
      return this.projectId;
    }
  },
  watch: {
    '$route.params.id': {
      immediate: true,
      handler(newId, oldId) {
        if (newId && newId !== oldId) {
          const decryptedData = this.__decryptProjectData(newId);
          if (decryptedData) {
            this.projectId = decryptedData.id;
            this.projectSlug = decryptedData.slug;
            // Re-fetch project and kanban data
            this.initDataKanban();
            this.getInquiries();
            this.getProject();
          }
        }
      }
    },
    selectedStatuses: {
      async handler(newVal, oldVal) {
        this.resetStore()
        this.dataKanban = {
          boards: [{
            name: "Kanban Desidia",
            columns: []
          }]
        };
        await this.initDataKanban()
        this.getInquiries()
      },
      deep: true, // Deep watcher to monitor all changes within the object
    },
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      setDataProject: 'application/setDataProject',
      changeStatus: 'application/changeStatus',
      updateInquiryInStore: 'application/updateInquiryInStore',
      resetStore: 'application/resetStore',
    }),
    
    debounce(func, wait) {
      let timeout;
      return function(...args) {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
          func.apply(this, args);
          }, wait);
      };
    },
    onInputSearch() {
      delay(() => {
        this.resetStore()
        this.getInquiries()
      }, 1000);
    },
    async getProject() {
      // Avoid duplicate calls if already loading
      if (this.isLoadingProject) return;
      this.isLoadingProject = true;

      this.resetStore();

      const callback = (response) => {
        const data = response.data;
        // Check if no project
        if (!data) {
          this.__showNotif('error', 'Project not found', "We're unable to locate the project you're looking for. It may have been moved or deleted.");
          this.$router.push({ name: 'Home' });
          this.isLoadingProject = false;
          return;
        }
        this.project = data;
        const roomId = data.slug;
        this.$soketio.emit('join', roomId);
        this.setDataProject(this.project);
        this.isLoadingProject = false;
      };
      const errCallback = (err) => {
        console.log(err);
        this.isLoadingProject = false;
      };
      projectApi.get(this.projectId, callback, errCallback);
    },
    getKanbanColumn() {
      return this.loadKanbanColumns();
    },
    populateKanban(dataFromAPI) {
      // Reset the columns array
      this.dataKanban = {
        boards: [{
          name: "Kanban Planlagt",
          columns: []
        }]
      };
      for (let index = 0; index < dataFromAPI.length; index++) {
        const column = dataFromAPI[index];
        column.inquiries = []
        column.name = column.name
        this.dataKanban.boards[0].columns.push(column);
      }
    },
    columnChange(data) {

      // Create a shallow copy of the current columns to avoid mutating the original array
      let currentColumns = [...this.$store.state.application.data[0].columns];

      // Find the column index to update or add
      let columnIndex = currentColumns.findIndex(col => col.id === data.id);
      if (columnIndex !== -1) {
        // If the column exists, update the existing column
        currentColumns[columnIndex].name = data.name;
      } else {
        // If the column doesn't exist, add it as a new column
        data.inquiries = [];
        data.name = data.name;

        // Insert the new column based on direction and current_index
        if (data?.directions === 'left') {
          // Insert the new column before the current_index without removing any item
          currentColumns.splice(data?.current_index, 0, data);
        } else if (data?.directions === 'right') {
          // Insert the new column after the current_index without removing any item
          currentColumns.splice(data?.current_index + 1, 0, data);
        }
      }

      // After updating the columns array, assign the modified array back to the state
      this.$store.state.application.data[0].columns = currentColumns;
      const ids = currentColumns.map(item => item.id);
      this.reorderKanbanColumn(ids)
    },

    columnUpdate(data) {
      let currentColumns = [...this.$store.state.application.data[0].columns];
      let columnIndex = currentColumns.findIndex(col => col.id === data.id);
      if (columnIndex !== -1) {
        currentColumns[columnIndex].name = data.name;
      }
    },

    inquiryUpdate(data) {
      let currentColumns = [...this.$store.state.application.data[0].columns];
      let columnIndex = currentColumns.findIndex(col => col.id === data.id);
      if (columnIndex !== -1) {
        Object.assign(currentColumns[columnIndex], data);
        currentColumns[columnIndex].name = data.name;
      }
    },

    reorderKanbanColumn(ids, additionalInfo = null) {
      // Validate that we have valid IDs
      if (!ids || ids.length === 0) {
        return;
      }

      const callback = (response) => {
        const data = response.data;
      };

      const errCallback = (err) => {
      };

      // Create the reorder payload
      const params = {
        column_ids: ids,
        project_id: this.projectId
      };

      // If we have additional positioning info, include it for backend processing
      if (additionalInfo) {
        params.reorder_info = {
          direction: additionalInfo.direction,
          target_index: additionalInfo.selectedIndexColumn,
          new_column_id: additionalInfo.newColumnId || additionalInfo.id
        };
      }

      kanbanApi.reorder(params, callback, errCallback);
    },


    columnRemove(columnId) {
      let columnIndex = this.$store.state.application.data[0].columns.findIndex(col => col.id === columnId);
      if (columnIndex !== -1) {
        this.$store.state.application.data[0].columns.splice(columnIndex, 1); // Remove the column
      }
    },
    async getInquiries() {
      // Avoid duplicate calls if already loading
      if (this.isLoadingInquiries) return;
      this.isLoadingInquiries = true;

      await this.resetStore()
      this.inquiries = [];
      this.isFetching = true;

      // Ensure columns are loaded before loading inquiries
      if (!this.dataKanban.boards[0].columns.length) {
        await this.loadKanbanColumns();
      }

      const callback = async (response) => {
        const data = response.data;
        this.inquiries = data;
        await this.mapServerDataToKanban(this.inquiries, this.dataKanban.boards[0].columns);
        this.isLoadingInquiries = false;
      };
      const errCallback = (err) => {
        this.isFetching = false;
        this.isLoadingInquiries = false;
      };
      let params = {
        order_by: 'index',
        sort_by: 'asc',
        project_id: this.projectId,
        limit: 99999,
      }
      if (this.keyword) {
        params.keyword = this.keyword
      }
      if (this.selectedStatuses.isMyInquiry) params.isMyInquiry = 1
      if (this.selectedStatuses.isDueThisWeek) params.isDueThisWeek = 1
      if (this.selectedStatuses.isDueNextWeek) params.isDueNextWeek = 1
      if (this.selectedStatuses.isCompleteInquiry) params.status = 'completed'
      if (this.selectedStatuses.isIncompleteInquiry) params.status = 'incompleted'
      if (this.selectedStatuses.isCompleteInquiry && this.selectedStatuses.isIncompleteInquiry) delete params.status

      inquiriesApi.getList(params, callback, errCallback);
    },

    // Make getKanbanColumn async and return a promise
    async loadKanbanColumns() {
      // Avoid duplicate calls if already loading
      if (this.isLoadingColumns) {
        return this.columnsPromise;
      }

      this.isLoadingColumns = true;

      this.columnsPromise = new Promise((resolve, reject) => {
        const callback = (response) => {
          const data = response.data;
          this.kanbanColumns = data;
          this.populateKanban(this.kanbanColumns);
          this.isLoadingColumns = false;
          resolve(data);
        };
        const errCallback = (err) => {
          this.isLoadingColumns = false;
          reject(err);
        };
        const params = {
          project_id: this.projectId,
          limit: 99,
        }
        kanbanApi.getList(params, callback, errCallback);
      });

      return this.columnsPromise;
    },

    async mapServerDataToKanban(serverData, kanbanBoard) {
      // Process each inquiry sequentially
      for await (const inquiry of serverData) {
        const kanbanInquiry = {
          id: inquiry.id,
          title: inquiry.title,
          projectName: inquiry.project.name,
          completed: inquiry.completed,
          assign: inquiry.assign,
          assign_to: inquiry.assign_to,
          startDate: inquiry.startDate,
          due_date: inquiry.due_date,
          parentId: inquiry.parentId,
          updatedAt: inquiry.updatedAt,
          description: inquiry.description,
          createdAt: inquiry.createdAt,
          status: inquiry.status,
          index: inquiry.index,
          type: inquiry.type,
          meta: inquiry.meta,
          slug: inquiry.slug,
          creator: inquiry.creator,
          collaborator: inquiry.collaborator,
          sub_inquiries: inquiry.sub_inquiries ? inquiry.sub_inquiries.map(sub_inquiries => ({
            title: sub_inquiries.title,
            isCompleted: sub_inquiries.status === 'completed'
          })) : [],
          // attributes for playing kanban
          activeIndex: -1, // index of active inquiry
          active: '', // name of the active inquiry (in case of edit)
          columnIndex: -1, // index of column of activeTask
          add: false,
          edit: false,
          delete: false,
          show: undefined, // holds the inquiry object, in inquiryShow
          projectId: inquiry.project?.id,
        };

        // Find the correct column and add the inquiry to it
        const column = kanbanBoard.find(col => col.name === inquiry.type);
        if (column) {
          column.inquiries.push(kanbanInquiry);
        }
      }

      // Set the data and update the state
      this.setData(this.dataKanban);
      this.isFetching = false;
      this.isDataOk = true;

      // Return the updated Kanban board
      return kanbanBoard;
    },

    async initDataKanban() {
      try {
        await this.loadKanbanColumns();
      } catch (error) {
        console.error('Failed to initialize kanban data:', error);
      }
    }
  },
  async mounted() {
    try {
      // Initialize data in the correct order to avoid race conditions
      await this.initDataKanban(); // Load columns first
      await this.getInquiries(); // Then load inquiries
      await this.getProject(); // Load project data

      setTimeout(() => {
        HSStaticMethods.autoInit();
      }, 500);
    } catch (error) {
      console.error('Failed to mount kanban:', error);
      this.isFetching = false;
    }
  },
  beforeUnmount () {
    const roomId = this.project.slug;
    this.$soketio.emit('leave', roomId);
  },
  created() {
    const encryptedId = this.$route.params.id; // Get the encrypted string from route params
    const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data

    if (decryptedData) {
      this.projectId = decryptedData.id;   // Access the original project ID
      this.projectSlug = decryptedData.slug; // Access the original project slug
    }
    this.$soketio.on('project_update', (data) => {
      if (this.project.id === data.id) this.project = data
    });

    let kanbanDataReceived = null;
    let inquiries = []
    // Listen for kanban_update event and store the data
    this.$soketio.on('kanban_update', (kanbanData) => {
      let column = kanbanData
      column.inquiries = inquiries
      this.columnUpdate(column);
      kanbanDataReceived = column; // Store kanbanData for later use
    });

    // Listen for inquiry_update_bulk event (register it once)
    this.$soketio.on('inquiry_update_bulk', (inquiryData) => {
      inquiries = inquiryData?.sort((a, b) => a.index - b.index)
      if (kanbanDataReceived) {
        // If kanban data is available, merge it with inquiry data
        let updatedColumn = { ...kanbanDataReceived }; // Copy the kanban data
        updatedColumn.inquiries = inquiryData; // Add the inquiries to the updated column

        // Call columnChange with the combined data
        this.inquiryUpdate(updatedColumn);
      } else {
        console.warn('No kanban data available when inquiries were updated');
      }
    });
    // need add additionalInfoAddingColumn
    this.$soketio.on('kanban_add', (data) => {
      this.columnChange(data)
    });
    this.$soketio.on('kanban_delete', (data) => {
      this.columnRemove(data.id)
    });
  },
};
</script>

<style scoped>
main {
  @apply absolute left-0 bottom-0 right-0;
}

main {
  @apply top-[0px];
}

._main {
  @apply left-[300px] top-[00px];
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 2.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
