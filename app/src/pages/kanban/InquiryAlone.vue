
<template>
	<ModalContainer />
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
	components: {
	},
	props: {
	},
	data() {
		return {
		};
	},
	computed: {
    ...mapGetters({
			user: 'auth/user',
		}),
  },
	watch: {},
	created() {
    if (this.$route.params.slug) {
      this.showDetailInquiry(atob(this.$route.params.slug));
    }
  },
	mounted() {},
	beforeUnmount() {},
	methods: {
    ...mapActions({
      showDetailInquiry: 'application/showDetailInquiry',
    }),
  },
};
</script>