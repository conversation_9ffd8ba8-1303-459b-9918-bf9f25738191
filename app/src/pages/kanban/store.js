import {
  reactive,
  toRaw
} from "vue"
import {
  useWindowSize
} from "@vueuse/core"
const {
  width,
  height
} = useWindowSize()

const store = reactive({
  version: "1.0.00",
  activeAddInquiryColumnIndex: null,
  wWidth: width,
  wHeight: height,
  data: null,
  hideAside: false,
  lightMode: true,
  isModal: false,
  mutate: false,
  project: null,
  board: {
    active: "", // name of the active board
    activeIndex: 0,
    add: false,
    edit: false,
    delete: false,
  },
  inquiry: {
    id: null,
    title: '',
    projectName: '',
    completed: '',
    assign: '',
    creator: null,
    assign_to: '',
    startDate: '',
    due_date: '',
    parent_id: '',
    updated_at: '',
    description: '',
    created_at: '',
    status: '',
    index: '',
    type: '',
    sub_inquiries: [],
    collaborator: [],
    comments: [],
    active: "", // name of the active inquiry (in case of edit)
    activeIndex: -1, // index of active inquiry
    columnIndex: -1, // index of column of activeInquiry
    status: "", // name of the column (when add / edit inquiry) to which the inquiry belongs
    add: false,
    addDirectly: false,
    edit: false,
    delete: false,
    show: undefined, // holds the inquiry object, in inquiryShow
  },

  setData: function (d) {
    this.data = toRaw(d.boards)
    if (JSON.stringify(this.data) != "{}") {
      this.board.active = this.data[0].title
      console.log('kia', this.board.active);
    }
  },

  setDataProject: function (project) {
    if (project) this.project = project
  },

  getActiveProject : function () {
    return this.project;
  },

  getActiveBoard: function () {
    if (this.data) {
      return this.data.find((el, index) => {
        if (el.name === this.board.active) {
          this.board.activeIndex = index
          return el
        }
      })
    }
  },
  getActiveColumn: function () {
    if (this.data) {
      const activeBoard = this.getActiveBoard();
      if (activeBoard && activeBoard.columns) {
        return activeBoard.columns.find((el) => {
          if (el.name === this.inquiry.type) {
            return el
          }
        })
      }
    }
  },

  getActiveInquiry: function () {
    if (this.data) {
      const activeColumn = this.getActiveColumn();
      if (activeColumn && activeColumn.inquiries) {
        return activeColumn.inquiries.find((el, idx) => {
          if (el.id === this.inquiry.active) {
            this.inquiry.activeIndex = idx
            return el
          }
        })
      }
    }
  },

  getBoardColsLength: function () {
    if (this.data && this.data.length > 0) {
      return this.getActiveBoard().columns.length
    }
    return 0
  },

  changeStatus: function (colIndex) {
    //  splice inquiry from the old status column

    let inquiry = this.data[this.board.activeIndex].columns[
      this.inquiry.columnIndex
    ].inquiries.splice(this.inquiry.activeIndex, 1)

    // push it in the new status column
    this.data[this.board.activeIndex].columns[colIndex].inquiries.push(inquiry[0])
  },

  updateInquiryInStore(inquiry) {
    const column = this.getActiveColumn(inquiry.type);
    if (column) {
        const inquiryIndex = column.inquiries.findIndex(t => t.id === inquiry.id);
        if (inquiryIndex !== -1) {
            column.inquiries.splice(inquiryIndex, 1, inquiry); // Update existing inquiry
        } else {
            column.inquiries.unshift(inquiry); // Add new inquiry at the beginning
        }
    }
  },

  // New method to reset store data
  resetStore: function () {
    this.data = {};
    this.hideAside = false;
    this.lightMode = true;
    this.isModal = false;
    this.mutate = false;
    this.project = null;
    this.board.active = "";
    this.board.activeIndex = 0;
    this.board.add = false;
    this.board.edit = false;
    this.board.delete = false;
    this.inquiry = {
      id: null,
      title: '',
      projectName: '',
      assign: '',
      creator: null,
      assign_to: '',
      startDate: '',
      due_date: '',
      parent_id: '',
      updated_at: '',
      description: '',
      created_at: '',
      status: '',
      index: '',
      type: '',
      sub_inquiries: [],
      collaborator: [],
      comments: [],
      active: "",
      activeIndex: -1,
      columnIndex: -1,
      status: "",
      add: false,
      addDirectly: false,
      edit: false,
      delete: false,
      show: undefined,
    };
  },
})
export default store