
<template>
	<div />
</template>

<script>
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
	components: {
	},
	props: {
	},
	data() {
		return {
		};
	},
	computed: {},
	watch: {},
	created() {
		const data = {
			token: this.$route.query.bearer,
			expires_at: this.$route.query.expires_at,
			// is_set_password: this.$route.query.,
		};
		this.$store.dispatch('auth/setSession', data);
		this.$router.push('/');
	},
	mounted() {},
	beforeUnmount() {},
	methods: {},
};
</script>