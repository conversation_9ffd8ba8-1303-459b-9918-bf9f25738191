<template>
	<div class="min-h-full flex flex-col justify-center py-40 sm:px-6 lg:px-8">
		<!-- <div class="sm:mx-auto sm:w-full sm:max-w-lg">
			<img
				class="mx-auto h-12 mb-5 w-auto"
				src="@/assets/images/svg/bannerbite.svg"
				alt="Bannerbite"
			>
		</div> -->
		<span class="text-center mb-6 px-6"> We will send link to your email to activate your account </span>


		<div class="mt-2 sm:mx-auto sm:w-full lg:max-w-md drop-shadow-xl">
			<div class="bg-white py-12 px-14 shadow sm:rounded-xl lg:px-14 md:px-14">
				<form
					class="space-y-2"
					@submit.prevent="submit"
				>
					<div>
						<div class="mt-1">
							<div class="mb-2">
								Email Address
							</div>
							<t-input
								v-model="email"
								:type="`email`"
								:value="email"
								class="w-full mb-2"
							/>
							<span
								v-if="!isValidEmailAddress && email && email.length !== 0"
								class="text-red-500 text-xs"
							>Invalid Email Address</span>
						</div>
					</div>

					<div class="flex items-center justify-end">
						<div class="text-sm mb-5">
							<router-link
								to="/login"
								class="font-medium text-primary-600"
							>
								Back to login
							</router-link>
						</div>
					</div>

					<div>
						<t-button
							:type="'submit'"
							:color="`primary-solid`"
							class="w-full"
							:isDisabled="!isFormValid || isSendingEmail"
							:isLoading="isSendingEmail"
						>
							Send Email
						</t-button>
					</div>
				</form>
			</div>
		</div>
	</div>
</template>

<script>

import authApi from '@/api/auth';
import TInput from '@/components/form/Input.vue';
import TButton from '@/components/global/Button.vue';
import { isValidEmail } from '@/libraries/helper';

export default {
	
	components: {
		TInput,
		TButton,
	},
	setup() {
		return {
		};
	},
	data() {
		return {
			email: null,
			isSendingEmail: false,
		};
	},
	computed: {
		isValidEmailAddress() {
			return isValidEmail(this.email);
		},
		isFormValid() {
			return (
				this.isValidEmailAddress
			);
		},
	},
	mounted() {
	},
	methods: {
		submit() {
			this.isSendingEmail = true;

			this.isSaving = true;
			const callback = (response) => {
				const message = response.message;
				this.__showNotif('success', 'User', message);
				this.isSendingEmail = false;
				this.email = '';
			};
			const errorCallback = (err) => {
				const message = err?.response?.data?.message;
				this.__showNotif('error', 'Error', message);
				this.isSendingEmail = false;
			};
			authApi.resend(this.email, callback, errorCallback);
		},
	},
};
</script>