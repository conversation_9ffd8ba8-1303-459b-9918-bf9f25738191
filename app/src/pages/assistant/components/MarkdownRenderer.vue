<template>
  <div class="prose prose-gray max-w-none text-gray-800">
    <div class="text-xs lg:text-sm" v-html="processedContent"></div>
  </div>
</template>

<script>
export default {
  name: 'Markdown<PERSON><PERSON><PERSON>',
  props: {
    content: {
      type: [String, Object],
      required: true
    },
    citations: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    processedContent() {
      // Handle enhanced content with citations
      if (typeof this.content === 'object' && this.content.segments) {
        return this.processMarkdownWithCitations(this.content.segments, this.content.citations || []);
      }
      
      // For legacy string content, convert to simple format
      const segments = [{ text: typeof this.content === 'string' ? this.content : '' }];
      return this.processMarkdownWithCitations(segments, this.citations);
    }
  },
  methods: {
    processMarkdownWithCitations(segments, citations) {
      let html = '';
      let citationCounter = 1;
      const citationMap = new Map();
      
      // Create citation mapping
      citations.forEach(citation => {
        if (!citationMap.has(citation.citation_id)) {
          citationMap.set(citation.citation_id, citationCounter++);
        }
      });
      
      segments.forEach((segment, index) => {
        // Process the text for basic markdown
        let processedText = this.processBasicMarkdown(segment.text);
        
        // Add citation button if this segment has a citation
        if (segment.citation_id) {
          const citationNumber = citationMap.get(segment.citation_id);
          const citation = citations.find(c => c.citation_id === segment.citation_id);
          
          if (citation && citationNumber) {
            processedText += this.createCitationButton(citationNumber, citation);
          }
        }
        
        html += processedText;
      });
      
      return html;
    },
    
    processBasicMarkdown(text) {
      if (!text) return '';
      
      // Split into paragraphs
      const paragraphs = text.split('\n\n').filter(p => p.trim());
      
      return paragraphs.map(paragraph => {
        let processed = paragraph.trim();
        
        // Bold text
        processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Italic text
        processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Code blocks
        processed = processed.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
        
        // Inline code
        processed = processed.replace(/`(.*?)`/g, '<code>$1</code>');
        
        // Line breaks
        processed = processed.replace(/\n/g, '<br>');
        
        return `<p class="mb-4 leading-relaxed">${processed}</p>`;
      }).join('');
    },
    
    createCitationButton(number, citation) {
      return `<button
        class="citation-button inline-flex items-center justify-center w-6 h-6 ml-1 text-xs font-medium text-primary-600 bg-primary-100 border border-primary-300 rounded-full hover:bg-primary-200 hover:border-primary-400 transition-all duration-200 cursor-pointer shadow-sm"
        onclick="window.handleCitationClick && window.handleCitationClick(${JSON.stringify(citation).replace(/"/g, '&quot;')})"
        title="Click to view source: ${citation.source_title || 'Source'} (Lines ${citation.chunk_lines_from || 'N/A'}-${citation.chunk_lines_to || 'N/A'})"
        data-citation-id="${citation.citation_id}"
      >${number}</button>`;
    },
    
    handleCitationClick(citation) {
      this.$emit('citation-click', citation);
    }
  },
  
  mounted() {
    // Set up global citation click handler
    window.handleCitationClick = (citation) => {
      this.handleCitationClick(citation);
    };
  },
  
  beforeUnmount() {
    // Clean up global handler
    if (window.handleCitationClick) {
      delete window.handleCitationClick;
    }
  }
};
</script>

<style scoped>
.prose {
  color: #374151;
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.625;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

.prose em {
  font-style: italic;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.prose pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
}

:deep(.citation-button) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  margin-left: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
  background-color: #F24822;
  border: 1px solid #F24822;
  border-radius: 9999px;
  transition: background-color 0.15s ease-in-out;
  cursor: pointer;
}

:deep(.citation-button:hover) {
  background-color: #ff6947;
}
</style>
