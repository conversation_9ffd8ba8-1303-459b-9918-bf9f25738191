<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] flex flex-col">
      <!-- Header -->
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900">Getting Started with AnswerFlow</h2>
          <button 
            @click="$emit('close')"
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Content -->
      <div class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
          <!-- Step 1 -->
          <div class="flex space-x-4">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-primary-600 font-semibold">1</span>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Create a Notebook</h3>
              <p class="text-gray-600 mb-3">
                Start by creating a new notebook for your research project. Each notebook can contain multiple sources and conversations.
              </p>
              <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-sm text-gray-700">
                  <strong>Tip:</strong> Use <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl+N</kbd> to quickly create a new notebook.
                </p>
              </div>
            </div>
          </div>
          
          <!-- Step 2 -->
          <div class="flex space-x-4">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-primary-600 font-semibold">2</span>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">{{$t('Add Sources')}}</h3>
              <p class="text-gray-600 mb-3">
                Upload documents, add websites, or include audio files as sources for your research.
              </p>
              <div class="grid grid-cols-3 gap-3 mb-3">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <svg class="h-6 w-6 text-red-500 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                  <p class="text-xs text-gray-600">PDFs & Docs</p>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <svg class="h-6 w-6 text-primary-500 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                  </svg>
                  <p class="text-xs text-gray-600">Websites</p>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <svg class="h-6 w-6 text-purple-500 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  <p class="text-xs text-gray-600">Audio Files</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Step 3 -->
          <div class="flex space-x-4">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-primary-600 font-semibold">3</span>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Chat with Your Sources</h3>
              <p class="text-gray-600 mb-3">
                Ask questions, request summaries, or generate content based on your uploaded sources.
              </p>
              <div class="bg-primary-50 rounded-lg p-3">
                <p class="text-sm text-primary-700">
                  <strong>Try asking:</strong> "Summarize the main points" or "What are the key findings?"
                </p>
              </div>
            </div>
          </div>
          
          <!-- Step 4 -->
          <div class="flex space-x-4">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-primary-600 font-semibold">4</span>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Take Notes & Organize</h3>
              <p class="text-gray-600 mb-3">
                Use the Studio sidebar to create notes, organize insights, and manage your research workflow.
              </p>
            </div>
          </div>
        </div>
        
        <!-- Keyboard Shortcuts -->
        <div class="mt-8 pt-6 border-t border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Keyboard Shortcuts</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Create notebook</span>
              <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl+N</kbd>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Back to dashboard</span>
              <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Esc</kbd>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Send message</span>
              <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Enter</kbd>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">New line in chat</span>
              <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Shift+Enter</kbd>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer -->
      <div class="p-6 border-t border-gray-200">
        <div class="flex justify-end">
          <t-button :color="'primary-solid'" @click="$emit('close')">
            Got it!
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TButton from '@/components/global/Button.vue';

export default {
  name: 'HelpModal',
  components: {
    TButton,
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped>
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}
</style>
