<template>
  <!-- Main Dialog -->
  <TModal :isShow="open" customClass="max-w-4xl max-h-[90vh] overflow-y-auto">
    <!-- Header -->
    <div class="flex items-center justify-between pb-4">
      <div class="flex items-center space-x-2">
        <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960" width="16px" fill="#FFFFFF">
            <path d="M480-80q-33 0-56.5-23.5T400-160h160q0 33-23.5 56.5T480-80ZM320-200v-80h320v80H320Zm10-120q-69-41-109.5-110T180-580q0-125 87.5-212.5T480-880q125 0 212.5 87.5T780-580q0 81-40.5 150T630-320H330Zm24-80h252q45-32 69.5-79T700-580q0-92-64-156t-156-64q-92 0-156 64t-64 156q0 54 24.5 101t69.5 79Zm126 0Z" />
          </svg>
        </div>
        <h2 class="text-xl font-medium">Desidia</h2>
      </div>
      <button
        @click="$emit('close')"
        class="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-md hover:bg-gray-100"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="space-y-6">
      <div>
        <h2 class="text-xl font-medium mb-2">{{$t('Add Sources')}}</h2>
        <p class="text-gray-600 text-sm mb-1">Sources let Desidia base its responses on the information that matters most to you.</p>
        <p class="text-gray-500 text-xs">
          (Examples: marketing plans, course reading, research notes, meeting transcripts, sales documents, etc.)
        </p>
      </div>

      <!-- File Upload Area -->
      <div 
        :class="[
          'border-2 border-dashed rounded-lg p-12 text-center transition-colors',
          dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
          isProcessingFiles ? 'opacity-50 pointer-events-none' : ''
        ]"
        @dragenter="handleDrag"
        @dragleave="handleDrag"
        @dragover="handleDrag"
        @drop="handleDrop"
      >
        <div class="flex flex-col items-center space-y-4">
          <div class="w-12 h-12 rounded-full flex items-center justify-center bg-slate-100">
            <svg class="h-6 w-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900 mb-2">
              {{ isProcessingFiles ? $t('Processing files...') : $t('Upload sources') }}
            </h3>
            <p class="text-gray-600 text-sm">
              <template v-if="isProcessingFiles">
                Please wait while we process your files
              </template>
              <template v-else>
                Drag & drop or 
                <button 
                  class="text-blue-600 hover:underline" 
                  @click="triggerFileInput"
                  :disabled="isProcessingFiles"
                >
                  choose file
                </button>
                 to upload
              </template>
            </p>
          </div>
          <p class="text-xs text-gray-500">
            Supported file types: PDF, txt, Markdown, Audio (e.g. mp3)
          </p>
          <input
            ref="fileInput"
            type="file"
            multiple
            class="hidden"
            accept=".pdf,.txt,.md,.mp3,.wav,.m4a"
            @change="handleFileSelect"
            :disabled="isProcessingFiles"
          />
        </div>
      </div>

      <!-- Integration Options -->
      <div class="grid grid-cols-2 gap-4">
        <TButton
          color="secondary-outline"
          class="h-auto p-4 flex flex-col items-center space-y-2"
          @click="showMultipleWebsiteDialog = true"
          :disabled="isProcessingFiles"
        >
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
          <span class="font-medium">Link - Website</span>
          <span class="text-sm text-gray-500">Multiple URLs at once</span>
        </TButton>

        <TButton
          color="secondary-outline"
          class="h-auto p-4 flex flex-col items-center space-y-2"
          @click="showCopiedTextDialog = true"
          :disabled="isProcessingFiles"
        >
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <span class="font-medium">Paste Text - Copied Text</span>
          <span class="text-sm text-gray-500">Add copied content</span>
        </TButton>
      </div>
    </div>
  </TModal>

  <!-- Multiple Website URLs Dialog -->
  <TModal :isShow="showMultipleWebsiteDialog" customClass="sm:max-w-lg">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900">Add Website URLs</h3>
      <button
        @click="showMultipleWebsiteDialog = false"
        class="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-md hover:bg-gray-100"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="space-y-4">
      <TInput
        v-model="websiteUrls"
        :value="websiteUrls"
        type="area"
        placeholder="Enter URLs, one per line...&#10;https://example.com&#10;https://another-site.com"
        customStyle="min-height: 120px; resize: vertical;"
      />

      <div class="flex justify-end space-x-3">
        <TButton color="secondary-outline" @click="showMultipleWebsiteDialog = false">
          Cancel
        </TButton>
        <TButton 
          color="primary-solid" 
          @click="handleMultipleWebsiteSubmit" 
          :disabled="!websiteUrls.trim() || isProcessingFiles"
        >
          {{ isProcessingFiles ? 'Processing...' : 'Add URLs' }}
        </TButton>
      </div>
    </div>
  </TModal>

  <!-- Copied Text Dialog -->
  <TModal :isShow="showCopiedTextDialog" customClass="sm:max-w-lg">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900">Add Text Content</h3>
      <button
        @click="showCopiedTextDialog = false"
        class="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-md hover:bg-gray-100"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
        <TInput
          v-model="textTitle"
          :value="textTitle"
          type="text"
          placeholder="Enter a title for this text..."
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
        <TInput
          v-model="textContent"
          :value="textContent"
          type="area"
          placeholder="Paste your text content here..."
          customStyle="min-height: 120px; resize: vertical;"
        />
      </div>

      <div class="flex justify-end space-x-3">
        <TButton color="secondary-outline" @click="showCopiedTextDialog = false">
          Cancel
        </TButton>
        <TButton 
          color="primary-solid" 
          @click="handleTextSubmit" 
          :disabled="!textTitle.trim() || !textContent.trim() || isProcessingFiles"
        >
          {{ isProcessingFiles ? 'Processing...' : 'Add Text' }}
        </TButton>
      </div>
    </div>
  </TModal>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import TModal from '@/components/global/Modal.vue';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import { useSources } from '../hooks/useSources.js';
import { useFileUpload } from '../hooks/useFileUpload.js';
import { useDocumentProcessing } from '../hooks/useDocumentProcessing.js';
import { useNotebookGeneration } from '../hooks/useNotebookGeneration.js';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';

export default {
  name: 'AddSourceDialog',
  components: {
    TModal,
    TButton,
    TInput
  },
  props: {
    open: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: false
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    // Reactive state
    const dragActive = ref(false);
    const showCopiedTextDialog = ref(false);
    const showMultipleWebsiteDialog = ref(false);
    const isLocallyProcessing = ref(false);
    const fileInput = ref(null);
    
    // Form data
    const websiteUrls = ref('');
    const textTitle = ref('');
    const textContent = ref('');

    // Hooks
    const {
      addSourceAsync,
      updateSource,
      isAdding
    } = useSources(computed(() => props.notebookId));

    const {
      uploadFile,
      isUploading
    } = useFileUpload();

    const {
      processDocumentAsync,
      isProcessing
    } = useDocumentProcessing();

    const {
      generateNotebookContentAsync,
      isGenerating
    } = useNotebookGeneration();

    // Computed
    const isProcessingFiles = computed(() => isLocallyProcessing.value);

    // Reset local processing state when dialog opens
    watch(() => props.open, (newVal) => {
      if (newVal) {
        isLocallyProcessing.value = false;
      }
    });

    // Drag and drop handlers
    const handleDrag = (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.type === 'dragenter' || e.type === 'dragover') {
        dragActive.value = true;
      } else if (e.type === 'dragleave') {
        dragActive.value = false;
      }
    };

    const handleDrop = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dragActive.value = false;
      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        const files = Array.from(e.dataTransfer.files);
        handleFileUpload(files);
      }
    };

    const handleFileSelect = (e) => {
      if (e.target.files && e.target.files[0]) {
        const files = Array.from(e.target.files);
        handleFileUpload(files);
      }
    };

    const triggerFileInput = () => {
      if (!isProcessingFiles.value && fileInput.value) {
        fileInput.value.click();
      }
    };

    // File processing function
    const processFileAsync = async (file, sourceId, notebookId) => {
      try {
        console.log('Starting file processing for:', file.name, 'source:', sourceId);
        const fileType = file.type.includes('pdf') ? 'pdf' : file.type.includes('audio') ? 'audio' : 'text';

        // Update status to uploading
        updateSource({
          sourceId,
          updates: {
            processing_status: 'uploading'
          }
        });

        // Upload the file
        const filePath = await uploadFile(file, notebookId, sourceId);
        if (!filePath) {
          throw new Error('File upload failed - no file path returned');
        }
        console.log('File uploaded successfully:', filePath);

        // Update with file path and set to processing
        updateSource({
          sourceId,
          updates: {
            file_path: filePath,
            processing_status: 'processing'
          }
        });

        // Start document processing
        try {
          await processDocumentAsync({
            sourceId,
            filePath,
            sourceType: fileType
          });

          // Generate notebook content
          await generateNotebookContentAsync({
            notebookId,
            filePath,
            sourceType: fileType
          });

          // Mark processing as completed in DB and UI
          updateSource({
            sourceId,
            updates: {
              processing_status: 'completed'
            }
          });

          console.log('Document processing completed for:', sourceId);
        } catch (processingError) {
          console.error('Document processing failed:', processingError);

          // Update to completed with basic info if processing fails
          updateSource({
            sourceId,
            updates: {
              processing_status: 'completed'
            }
          });
        }
      } catch (error) {
        console.error('File processing failed for:', file.name, error);

        // Update status to failed
        updateSource({
          sourceId,
          updates: {
            processing_status: 'failed'
          }
        });
      }
    };

    // File upload handler
    const handleFileUpload = async (files) => {
      if (!props.notebookId) {
        console.error('No notebook selected');
        return;
      }

      console.log('Processing multiple files with delay strategy:', files.length);
      isLocallyProcessing.value = true;

      try {
        // Step 1: Create the first source immediately
        const firstFile = files[0];
        const firstFileType = firstFile.type.includes('pdf') ? 'pdf' : firstFile.type.includes('audio') ? 'audio' : 'text';
        const firstSourceData = {
          notebookId: props.notebookId,
          title: firstFile.name,
          type: firstFileType,
          file_size: firstFile.size,
          processing_status: 'pending',
          metadata: {
            fileName: firstFile.name,
            fileType: firstFile.type
          }
        };
        
        console.log('Creating first source for:', firstFile.name);
        const firstSource = await addSourceAsync(firstSourceData);
        
        let remainingSources = [];
        
        // Step 2: If there are more files, add a delay before creating the rest
        if (files.length > 1) {
          console.log('Adding 150ms delay before creating remaining sources...');
          await new Promise(resolve => setTimeout(resolve, 150));
          
          // Create remaining sources
          remainingSources = await Promise.all(files.slice(1).map(async (file, index) => {
            const fileType = file.type.includes('pdf') ? 'pdf' : file.type.includes('audio') ? 'audio' : 'text';
            const sourceData = {
              notebookId: props.notebookId,
              title: file.name,
              type: fileType,
              file_size: file.size,
              processing_status: 'pending',
              metadata: {
                fileName: file.name,
                fileType: file.type
              }
            };
            console.log('Creating source for:', file.name);
            return await addSourceAsync(sourceData);
          }));
          
          console.log('Remaining sources created:', remainingSources.length);
        }

        // Combine all created sources
        const allCreatedSources = [firstSource, ...remainingSources];

        console.log('All sources created successfully:', allCreatedSources.length);

        // Step 3: Close dialog immediately
        isLocallyProcessing.value = false;
        emit('close');

        // Step 4: Show success toast (you can implement toast later)
        console.log(`${files.length} file${files.length > 1 ? 's' : ''} added and processing started`);

        // Step 5: Process files in parallel (background)
        const processingPromises = files.map((file, index) => processFileAsync(file, allCreatedSources[index].id, props.notebookId));

        // Don't await - let processing happen in background
        Promise.allSettled(processingPromises).then(results => {
          const successful = results.filter(r => r.status === 'fulfilled').length;
          const failed = results.filter(r => r.status === 'rejected').length;

          console.log('File processing completed:', {
            successful,
            failed
          });

          if (failed > 0) {
            console.warn(`${failed} file${failed > 1 ? 's' : ''} had processing issues. Check the sources list for details.`);
          }
        });
      } catch (error) {
        console.error('Error creating sources:', error);
        isLocallyProcessing.value = false;
      }
    };

    // Text submission handler
    const handleTextSubmit = async () => {
      if (!props.notebookId) return;
      isLocallyProcessing.value = true;

      try {
        // Create source record first to get the ID
        const createdSource = await addSourceAsync({
          notebookId: props.notebookId,
          title: textTitle.value,
          type: 'text',
          content: textContent.value,
          processing_status: 'processing',
          metadata: {
            characterCount: textContent.value.length,
            webhookProcessed: true
          }
        });

        // Send to webhook endpoint with source ID
        const { data, error } = await supabase.functions.invoke('process-additional-sources', {
          body: {
            type: 'copied-text',
            notebookId: props.notebookId,
            title: textTitle.value,
            content: textContent.value,
            sourceIds: [createdSource.id],
            timestamp: new Date().toISOString()
          }
        });

        if (error) {
          throw error;
        }

        console.log('Text has been added and sent for processing');
        
        // Reset form
        textTitle.value = '';
        textContent.value = '';
        showCopiedTextDialog.value = false;
      } catch (error) {
        console.error('Error adding text source:', error);
      } finally {
        isLocallyProcessing.value = false;
      }

      emit('close');
    };

    // Multiple website submission handler
    const handleMultipleWebsiteSubmit = async () => {
      if (!props.notebookId) return;
      isLocallyProcessing.value = true;

      try {
        const urls = websiteUrls.value.split('\n').filter(url => url.trim()).map(url => url.trim());
        console.log('Creating sources for multiple websites with delay strategy:', urls.length);
        
        // Create the first source immediately
        const firstSource = await addSourceAsync({
          notebookId: props.notebookId,
          title: `Website 1: ${urls[0]}`,
          type: 'website',
          url: urls[0],
          processing_status: 'processing',
          metadata: {
            originalUrl: urls[0],
            webhookProcessed: true
          }
        });
        
        console.log('First source created:', firstSource.id);
        
        let remainingSources = [];
        
        // If there are more URLs, add a delay before creating the rest
        if (urls.length > 1) {
          console.log('Adding 150ms delay before creating remaining sources...');
          await new Promise(resolve => setTimeout(resolve, 150));
          
          // Create remaining sources
          remainingSources = await Promise.all(urls.slice(1).map(async (url, index) => {
            return await addSourceAsync({
              notebookId: props.notebookId,
              title: `Website ${index + 2}: ${url}`,
              type: 'website',
              url,
              processing_status: 'processing',
              metadata: {
                originalUrl: url,
                webhookProcessed: true
              }
            });
          }));
          
          console.log('Remaining sources created:', remainingSources.length);
        }

        // Combine all created sources
        const allCreatedSources = [firstSource, ...remainingSources];

        // Send to webhook endpoint with all source IDs
        const { data, error } = await supabase.functions.invoke('process-additional-sources', {
          body: {
            type: 'multiple-websites',
            notebookId: props.notebookId,
            urls,
            sourceIds: allCreatedSources.map(source => source.id),
            timestamp: new Date().toISOString()
          }
        });

        if (error) {
          throw error;
        }

        console.log(`${urls.length} website${urls.length > 1 ? 's' : ''} added and sent for processing`);
        
        // Reset form
        websiteUrls.value = '';
        showMultipleWebsiteDialog.value = false;
      } catch (error) {
        console.error('Error adding multiple websites:', error);
      } finally {
        isLocallyProcessing.value = false;
      }

      emit('close');
    };

    return {
      // Reactive state
      dragActive,
      showCopiedTextDialog,
      showMultipleWebsiteDialog,
      isProcessingFiles,
      fileInput,
      
      // Form data
      websiteUrls,
      textTitle,
      textContent,
      
      // Methods
      handleDrag,
      handleDrop,
      handleFileSelect,
      triggerFileInput,
      handleTextSubmit,
      handleMultipleWebsiteSubmit
    };
  }
};
</script>

<style scoped>
/* Custom styles for enhanced visual appeal */
.group:hover .opacity-0 {
  opacity: 1;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition: all 0.2s ease-in-out;
}

/* Enhanced focus states */
.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(59 130 246 / 0.5);
}

/* Custom scrollbar for textareas */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* File drop zone animation */
@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(209 213 219);
  }
  50% {
    border-color: rgb(59 130 246);
  }
}

.border-dashed:hover {
  animation: pulse-border 2s infinite;
}
</style>
