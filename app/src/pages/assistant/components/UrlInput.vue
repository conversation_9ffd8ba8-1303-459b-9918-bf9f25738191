<template>
  <div class="bg-white rounded-lg border border-gray-200 p-4 mb-4">
    <h3 class="text-sm font-medium text-gray-900 mb-3">Add Website</h3>
    <div class="flex space-x-3">
      <div class="flex-1">
        <input
          v-model="url"
          type="url"
          placeholder="Enter website URL..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          @keydown.enter="handleAddUrl"
        />
      </div>
      <t-button 
        :color="'primary-solid'"
        @click="handleAddUrl"
        :disabled="!isValidUrl || isAdding"
      >
        {{ isAdding ? 'Adding...' : 'Add' }}
      </t-button>
    </div>
    <p class="text-xs text-gray-500 mt-2">
      Add web pages, articles, or documentation as sources
    </p>
  </div>
</template>

<script>
import TButton from '@/components/global/Button.vue';

export default {
  name: 'UrlInput',
  components: {
    TButton,
  },
  props: {
    notebookId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      url: '',
      isAdding: false
    };
  },
  computed: {
    isValidUrl() {
      try {
        new URL(this.url);
        return true;
      } catch {
        return false;
      }
    }
  },
  methods: {
    async handleAddUrl() {
      if (!this.isValidUrl || this.isAdding) return;
      
      this.isAdding = true;
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const newSource = {
          id: Date.now().toString(),
          title: this.url,
          type: 'url',
          url: this.url,
          created_at: new Date().toISOString()
        };
        
        this.$emit('source-added', newSource);
        this.url = '';
        
        this.__showNotif('success', 'Success', 'Website added successfully');
      } catch (error) {
        console.error('Failed to add URL:', error);
        this.__showNotif('error', 'Error', 'Failed to add website');
      } finally {
        this.isAdding = false;
      }
    }
  }
};
</script>
