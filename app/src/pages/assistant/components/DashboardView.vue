<template>
  <div class="min-h-screen bg-white">
    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-6">
    
    <loader-circle v-if="isLoading" />
      <!-- Content -->
      <div v-else>
        <!-- Empty State -->
        <EmptyDashboard v-if="!hasNotebooks" @create-notebook="$emit('create-notebook')" :isCreating="isCreating" />
        
        <!-- Notebooks Grid -->
        <NotebookGrid v-else :notebooks="notebooks" @create-notebook="$emit('create-notebook')" @open-notebook="$emit('open-notebook', $event)" :isCreating="isCreating" />
        <!-- Modern Pagination -->
        <ModernPagination
          v-if="!isLoading && hasNotebooks && totalPages > 1"
          :total="totalNotebooks"
          :current-page="currentPage"
          :last-page="totalPages"
          :per-page="perPage"
          :is-fetching="isLoading"
          :is-show-limit="false"
          @page-changed="$emit('set-page', $event)"
          @per-page-changed="$emit('per-page-changed', $event)"
        />
      </div>
    </main>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import EmptyDashboard from './EmptyDashboard.vue';
import NotebookGrid from './NotebookGrid.vue';
import ModernPagination from '@/components/global/ModernPagination.vue';

export default {
  name: 'DashboardView',
  components: {
    EmptyDashboard,
    NotebookGrid,
    ModernPagination,
  },
  props: {
    notebooks: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    isCreating: {
      type: Boolean,
      default: false
    },
    currentPage: {
      type: Number,
      default: 1
    },
    totalPages: {
      type: Number,
      default: 1
    },
    totalNotebooks: {
      type: Number,
      default: 0
    },
    perPage: {
      type: Number,
      default: 12
    }
  },
  computed: {
    ...mapGetters({
      user: 'auth/user'
    }),
    hasNotebooks() {
      return this.notebooks && this.notebooks.length > 0;
    }
  }
};
</script>
