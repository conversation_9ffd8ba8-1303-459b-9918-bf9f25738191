<template>
  <div 
    :class="`rounded-lg border ${borderClass} ${backgroundClass} p-4 hover:shadow-md transition-shadow cursor-pointer relative h-48 flex flex-col`"
    @click="handleClick"
  >
    <!-- Delete Button -->
    <div class="absolute top-3 right-3" @click.stop>
      <div v-if="showDeleteDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="cancelDelete">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4" @click.stop>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete this notebook?</h3>
          <p class="text-gray-600 mb-6">
            You're about to delete this notebook and all of its content. This cannot be undone.
          </p>
          <div class="flex justify-end space-x-3">
            <button 
              @click="cancelDelete"
              class="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button 
              @click="confirmDelete"
              :disabled="isDeleting"
              class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
            >
              {{ isDeleting ? 'Deleting...' : 'Delete' }}
            </button>
          </div>
        </div>
      </div>
      
      <button 
        @click="showDeleteDialog = true"
        class="p-1 hover:bg-red-50 rounded text-gray-400 hover:text-red-500 transition-colors"
        :disabled="isDeleting"
      >
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      </button>
    </div>
    
    <!-- Icon -->
    <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-4">
      <span class="text-3xl">{{ notebook.icon }}</span>
    </div>
    
    <!-- Title -->
    <div class="text-gray-900 mb-2 pr-6 line-clamp-2 text-2xl font-normal flex-grow">
      {{ notebook.title }}
    </div>
    
    <!-- Footer -->
    <div class="flex items-center text-sm text-gray-500 mt-auto">
      <span>{{ notebook.date }}</span> <span class="px-2">-</span>
      <span>{{ notebook.sources }} source{{ notebook.sources !== 1 ? 's' : '' }}</span>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useNotebookDelete } from '../hooks/useNotebookDelete.js';

export default {
  name: 'NotebookCard',
  props: {
    notebook: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const { deleteNotebook, isDeleting } = useNotebookDelete();
    const showDeleteDialog = ref(false);
    const router = useRouter();

    // Color classes (tailwind style)
    const colorName = computed(() => props.notebook.color || 'gray');
    const backgroundClass = computed(() => `bg-${colorName.value}-100`);
    const borderClass = computed(() => `border-${colorName.value}-200`);

    function handleClick() {
      // Route to /assistant/notebook/:notebookid
      router.push(`/assistant/notebook/${props.notebook.id}`);
    }

    function handleDeleteClick(e) {
      e.stopPropagation();
      showDeleteDialog.value = true;
    }

    function cancelDelete(e) {
      if (e) e.stopPropagation();
      showDeleteDialog.value = false;
    }

    function confirmDelete(e) {
      if (e) e.stopPropagation();
      deleteNotebook(props.notebook.id, {
        onSuccess: () => {
          showDeleteDialog.value = false;
          if (typeof window !== 'undefined' && typeof window.__showNotif === 'function') {
            window.__showNotif('success', 'Success', 'Notebook deleted successfully');
          } else if (typeof emit === 'function') {
            emit('__showNotif', 'success', 'Success', 'Notebook deleted successfully');
          }
        },
        onError: (error) => {
          console.error('Failed to delete notebook:', error);
          if (typeof window !== 'undefined' && typeof window.__showNotif === 'function') {
            window.__showNotif('error', 'Error', 'Failed to delete notebook');
          } else if (typeof emit === 'function') {
            emit('__showNotif', 'error', 'Error', 'Failed to delete notebook');
          }
        }
      });
    }

    return {
      showDeleteDialog,
      deleteNotebook,
      isDeleting,
      handleClick,
      handleDeleteClick,
      cancelDelete,
      confirmDelete,
      backgroundClass,
      borderClass,
      notebook: props.notebook,
    };
  }
};
</script>


<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* Standard property for compatibility */
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
