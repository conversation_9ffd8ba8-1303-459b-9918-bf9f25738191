<template>
  <div class="h-full bg-gray-50 border-l border-gray-200 flex flex-col transform transition-transform duration-500 ease-in-out assistant-compact">
    <!-- Header -->
    <div class="p-2 sm:p-3 lg:p-4 border-b border-gray-200">
      <div class="flex items-center justify-between mb-2 sm:mb-3 lg:mb-4">
        <h2 class="text-sm sm:text-base lg:text-lg font-medium text-gray-900">{{$t('Notes')}}</h2>
        <button
          @click="handleCreateNote"
          class="px-3 py-1.5 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors flex items-center"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add note
        </button>
      </div>


    </div>
    
    <!-- Content -->
    <div class="flex-1 overflow-hidden">
      <!-- Notes Tab -->
      <div class="h-full flex flex-col">
        <!-- Notes List -->
        <div class="flex-1 overflow-y-auto p-2 sm:p-3 lg:p-4">
          <!-- Loading State -->
          <div v-if="isLoading" class="text-center py-8">
            <p class="text-sm text-gray-600">Loading notes...</p>
          </div>
          
          <!-- Notes List -->
          <div v-else-if="notes && notes.length > 0" class="space-y-3">
            <div
              v-for="note in notes"
              :key="note.id"
              class="bg-white rounded-lg border border-gray-200 p-3 hover:bg-gray-50 cursor-pointer transition-colors"
              @click="handleEditNote(note)"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-2 mb-1">
                    <svg v-if="note.source_type === 'ai_response'" class="h-3 w-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <svg v-else class="h-3 w-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-xs text-gray-500 uppercase">
                      {{ note.source_type === 'ai_response' ? 'AI Response' : 'Note' }}
                    </span>
                  </div>
                  <h4 class="font-medium text-gray-900 truncate">{{ note.title }}</h4>
                  <p class="text-sm text-gray-600 line-clamp-2 mt-1">
                    {{ getPreviewText(note) }}
                  </p>
                  <p class="text-xs text-gray-500 mt-2">
                    {{ formatDate(note.updated_at) }}
                  </p>
                </div>
                <button v-if="note.source_type === 'user'" class="ml-2 p-1 text-gray-400 hover:text-gray-600">
                  <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Empty State -->
          <div v-else class="text-center py-8">
            <div class="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
              <span class="text-gray-400 text-2xl">📄</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{$t('Saved notes will appear here')}}</h3>
            <p class="text-sm text-gray-600">
              {{$t('Save a chat message to create a new note, or click Add note above.')}}
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Note Editor Modal -->
    <div v-if="isEditingMode" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-xl w-full max-w-md max-h-[85vh] flex flex-col shadow-sm border border-gray-200 relative">
        <!-- Close Button at Top Right -->
        <button
          @click="handleCancel"
          class="absolute top-3 right-3 p-2 text-gray-300 hover:text-gray-500 transition-colors rounded-full focus:outline-none focus:ring-1 focus:ring-blue-100 z-10"
          title="Close"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <!-- Note Fields -->
        <div class="flex-1 flex flex-col gap-3 px-5 py-3 overflow-y-auto">
          <input
            :value="editingNote?.title || ''"
            @input="updateNoteTitle($event.target.value)"
            placeholder="Note title..."
            class="text-base font-normal text-gray-900 border border-gray-200 rounded px-3 py-2 outline-none focus:ring-1 focus:ring-blue-100 transition w-full bg-white"
            maxlength="100"
            autocomplete="off"
          />
          <textarea
            :value="editingNote?.content || ''"
            @input="updateNoteContent($event.target.value)"
            placeholder="Start writing your note..."
            class="w-full min-h-[120px] max-h-48 resize-vertical border border-gray-200 rounded px-3 py-2 outline-none text-gray-900 focus:ring-1 focus:ring-blue-100 transition bg-white"
            maxlength="2000"
          ></textarea>
        </div>
        <!-- Action Bar at Bottom -->
        <div class="flex items-center justify-end gap-2 px-3 pt-2 pb-3 border-t border-gray-100 bg-white">
          <button
            v-if="editingNote && editingNote.source_type === 'user'"
            @click="handleDeleteNote"
            :disabled="isDeleting"
            class="px-3 py-1 text-red-500 border border-gray-200 rounded-md bg-transparent hover:bg-red-50 hover:border-red-200 transition-colors text-sm font-normal disabled:opacity-50"
          >
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
          <button
            @click="saveCurrentNote"
            :disabled="isCreating || isUpdating"
            class="px-4 py-1 border border-blue-200 text-blue-600 bg-transparent rounded-md hover:bg-blue-50 hover:border-blue-300 transition-colors text-sm font-normal disabled:opacity-50"
          >
            {{ isCreating || isUpdating ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useNotes } from '../hooks/useNotes.js';
import { useNotebooks } from '../hooks/useNotebooks.js';
import { useSources } from '../hooks/useSources.js';

export default {
  name: 'StudioSidebar',
  props: {
    notebookId: {
      type: String,
      required: true
    }
  },
  emits: ['citation-click'],
  setup(props, { emit }) {
    // Hooks
    const { notes, isLoading, createNote, updateNote, deleteNote, isCreating, isUpdating, isDeleting } = useNotes(props.notebookId);
    const { notebooks } = useNotebooks();
    const { sources } = useSources(props.notebookId);

    // Reactive state
    const editingNote = ref(null);
    const isCreatingNote = ref(false);

    // Computed
    const notebook = computed(() => notebooks.value?.find(n => n.id === props.notebookId));
    const hasProcessedSource = computed(() => sources.value?.some(source => source.processing_status === 'completed') || false);
    const isEditingMode = computed(() => editingNote.value || isCreatingNote.value);

    // Methods
    const handleCreateNote = () => {
      isCreatingNote.value = true;
      editingNote.value = null;
    };

    const handleEditNote = (note) => {
      console.log('StudioSidebar: Opening note', {
        noteId: note.id,
        sourceType: note.source_type
      });
      editingNote.value = note;
      isCreatingNote.value = false;
    };

    const handleSaveNote = (title, content) => {
      if (editingNote.value && editingNote.value.id) {
        // Only allow updating user notes, not AI responses
        if (editingNote.value.source_type === 'user') {
          updateNote({
            id: editingNote.value.id,
            title,
            content
          });
        }
      } else {
        createNote({
          title,
          content,
          source_type: 'user'
        });
      }
      editingNote.value = null;
      isCreatingNote.value = false;
    };


    const handleDeleteNote = () => {
      if (editingNote.value) {
        deleteNote(editingNote.value.id);
        editingNote.value = null;
      }
    };

    const handleCancel = () => {
      editingNote.value = null;
      isCreatingNote.value = false;
    };

    const getPreviewText = (note) => {
      if (note.source_type === 'ai_response') {
        // Use extracted_text if available, otherwise parse the content
        if (note.extracted_text) {
          return note.extracted_text;
        }
        try {
          const parsed = JSON.parse(note.content);
          if (parsed.segments && parsed.segments[0]) {
            return parsed.segments[0].text;
          }
        } catch (e) {
          // If parsing fails, use content as-is
        }
      }

      // For user notes or fallback, use the content directly
      const contentToUse = note.content;
      return contentToUse.length > 100 ? contentToUse.substring(0, 100) + '...' : contentToUse;
    };

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    };

    const updateNoteTitle = (title) => {
      if (editingNote.value) {
        editingNote.value = { ...editingNote.value, title };
      } else {
        editingNote.value = { title, content: '', source_type: 'user' };
      }
    };

    const updateNoteContent = (content) => {
      if (editingNote.value) {
        editingNote.value = { ...editingNote.value, content };
      } else {
        editingNote.value = { title: '', content, source_type: 'user' };
      }
    };

    const saveCurrentNote = () => {
      if (!editingNote.value) return;
      handleSaveNote(editingNote.value.title || '', editingNote.value.content || '');
    };

    return {
      // Data
      notes,
      isLoading,
      editingNote,
      isCreatingNote,
      notebook,
      hasProcessedSource,
      isEditingMode,
      isCreating,
      isUpdating,
      isDeleting,
      
      // Methods
      handleCreateNote,
      handleEditNote,
      handleSaveNote,
      handleDeleteNote,
      handleCancel,
      getPreviewText,
      formatDate,
      updateNoteTitle,
      updateNoteContent,
      saveCurrentNote
    };
  }
};
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced animations for note items */
.note-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.note-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Smooth tab transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.3s;
}
</style>
