<template>
  <div class="h-full bg-white flex flex-col border border-gray-200 rounded-lg">
    <!-- Header -->
    <div class="flex items-center justify-between px-3 sm:px-4 lg:px-6 py-2 sm:py-1 lg:py-3 border-b border-gray-200 bg-white">
      <div class="text-sm sm:text-base lg:text-lg font-medium sm:font-semibold text-gray-900">Chat</div>
      <div
        v-if="shouldShowRefreshButton"
        @click="clearChat"
        :disabled="isDeletingChatHistory"
        class="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors cursor-pointer"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
        </svg>
        <span class="hidden sm:inline">{{ isDeletingChatHistory ? 'Clearing...' : 'Clear Chat' }}</span>
        <span class="sm:hidden">{{ isDeletingChatHistory ? 'Clearing...' : 'Clear' }}</span>
      </div>
    </div>

    <!-- Chat Messages -->
    <div class="flex-1 overflow-y-auto p-3 sm:p-4 lg:p-6" ref="messagesContainer">
      <!-- Welcome Message -->
      <div v-if="messages.length === 0 && !pendingUserMessage" class="text-center py-8 sm:py-12 lg:py-16">
        <div class="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <svg class="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h3 class="text-sm sm:text-base lg:text-lg font-medium text-gray-900 mb-1 sm:mb-2">
          {{ hasSource ? $t('Ask questions about your sources') : $t('Upload sources to get started') }}
        </h3>
        <p class="text-xs sm:text-sm lg:text-base text-gray-600 max-w-xs sm:max-w-sm lg:max-w-md mx-auto px-2">
          {{ hasSource
            ? $t('I can help you analyze, summarize, and answer questions about your uploaded documents.')
            : $t('Add documents, websites, or audio files to start chatting with your sources.')
          }}
        </p>
      </div>
      
      <!-- Messages -->
      <div v-else class="space-y-3 sm:space-y-4 lg:space-y-6">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'flex',
            isUserMessage(message) ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'max-w-[85%] sm:max-w-[80%] lg:max-w-[75%] rounded-lg px-3 sm:px-4 py-2 sm:py-3',
              isUserMessage(message)
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-900 border border-gray-200'
            ]"
          >
            <!-- User Message -->
            <div v-if="isUserMessage(message)" class="text-xs lg:text-sm">
              {{ message.message.content }}
            </div>
            
            <!-- Assistant Message -->
            <div v-else class="space-y-3">
              <MarkdownRenderer
                :content="message.message.content"
                :citations="message.message.citations || []"
                @citation-click="handleCitationClick"
              />

              <!-- Action Buttons -->
              <div class="mt-3 sm:mt-4 pt-2 sm:pt-3 border-t border-gray-200 space-y-3">
                <div class="flex items-center justify-start gap-2 overflow-x-auto scrollbar-hide py-1">
                  <t-button :color="'secondary-solid-small'">
                    Create Case
                  </t-button>
                  <t-button :color="'secondary-solid-small'">
                    See Documentation
                  </t-button>
                  <t-button :color="'secondary-solid-small'">
                    Contact Board
                  </t-button>
                </div>

                <!-- Feedback Buttons -->
                <div class="flex items-center justify-end space-x-2">
                  <t-button class="p-1 sm:p-1.5 text-gray-400 hover:text-green-600 transition-colors">
                    <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
                    </svg>
                  </t-button>
                  <t-button class="p-1 sm:p-1.5 text-gray-400 hover:text-red-600 transition-colors">
                    <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.20.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"/>
                    </svg>
                  </t-button>
                </div>
              </div>

              <!-- Save to Note Button -->
              <div class="flex justify-start mt-2">
                <!-- <SaveToNoteButton
                  :content="message.content"
                  :notebook-id="notebookId"
                /> -->
              </div>
            </div>
          </div>
        </div>
        
        <!-- Pending User Message -->
        <div v-if="pendingUserMessage" class="flex justify-end">
          <div class="max-w-[85%] sm:max-w-[80%] lg:max-w-[75%] rounded-lg px-3 sm:px-4 py-2 sm:py-3 bg-primary-600 text-white">
            <div class="text-xs lg:text-sm">
              {{ pendingUserMessage }}
            </div>
          </div>
        </div>
        
        <!-- AI Loading Indicator -->
        <div v-if="showAiLoading" class="flex justify-start">
          <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-[80%]">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator (Legacy) -->
        <div v-if="isTyping" class="flex justify-start">
          <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-[80%]">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    



    <!-- Input Area -->
    <div class="border-t border-gray-200">
      

      <!-- Message Input -->
      <div class="p-2">
        <div class="flex space-x-2 sm:space-x-3 my-1">
          <div class="flex-1 items-center">
            <textarea
              v-model="inputMessage"
              @keydown="handleKeyDown"
              :placeholder="!hasSource ? $t('Upload a source to get started...') : $t('Start typing...')"
              :disabled="!hasSource || isSending"
              class="w-full resize-none border border-gray-300 rounded-lg px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm lg:text-base focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
              :rows="2"
              ref="messageInput"
            ></textarea>
          </div>
          <div class="flex items-center">
            <t-button
              @click="handleSendMessage"
              :color="'primary-solid'"
              :disabled="!canSend"
              :class="[
                'p-2 sm:p-2.5 lg:p-3 rounded-lg transition-colors',
                canSend
                  ? 'bg-primary-600 text-white hover:bg-primary-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
            >
              <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </t-button>
          </div>
        </div>
      </div>
    </div>
      <!-- Example Questions -->
    <div v-if="hasSource && messages.length === 0 && exampleQuestions.length > 0" class="mt-2 p-4 bg-primary-50/40 border border-primary-100 rounded-xl shadow-sm animate-fade-in">
      <div class="text-sm font-medium text-primary-700 mb-3">Example questions:</div>
      <div class="flex flex-wrap gap-4">
        <t-button
          v-for="question in exampleQuestions"
          :key="question"
          @click="handleExampleQuestionClick(question)"
          class="px-4 py-2 text-sm bg-white text-primary-700 rounded-lg border border-gray-200 shadow-sm hover:bg-primary-100 hover:text-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-200 transition"
        >
          {{ question }}
        </t-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import TButton from '@/components/global/Button.vue';
import MarkdownRenderer from './MarkdownRenderer.vue';
import SaveToNoteButton from './SaveToNoteButton.vue';
import { useChatMessages } from '../hooks/useChatMessages.js';
import {
		RefreshIcon,
} from '@heroicons/vue/outline';

export default {
  name: 'ChatArea',
  components: {
    TButton,
    MarkdownRenderer,
    SaveToNoteButton,
    RefreshIcon,
  },
  props: {
    hasSource: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    },
    onCitationClick: {
      type: Function,
      default: null
    }
  },
  setup(props) {
    // Store access
    const store = useStore();

    // Computed properties from store
    const isClient = computed(() => store.getters['auth/isClient']);

    // Chat messages hook
    // Defensive: always pass a string notebookId to the hook
    const notebookIdSafe = computed(() => props.notebookId || '');
    const {
      messages,
      sendMessage,
      sendMessageAsync,
      isSending,
      deleteChatHistory,
      isDeletingChatHistory
    } = useChatMessages(notebookIdSafe);

    // Local reactive state
    const showExamplePanel = ref(true);
    const inputMessage = ref('');
    const pendingUserMessage = ref(null);
    const showAiLoading = ref(false);
    const clickedQuestions = ref(new Set());
    const lastMessageCount = ref(0);
    const messagesContainer = ref(null);
    const messageInput = ref(null);
    const isTyping = ref(false);

    // Computed properties
    const canSend = computed(() => {
      return inputMessage.value.trim() && props.hasSource && !isSending.value && !pendingUserMessage.value;
    });

    const shouldShowRefreshButton = computed(() => messages.value.length > 0);
    
    const exampleQuestions = computed(() => {
      return props.notebook?.example_questions?.filter(q => !clickedQuestions.value.has(q)) || [];
    });

    const getPlaceholderText = () => {
      if (!props.hasSource) {
        return t('Upload a source to get started...');
      }
      return t('Start typing...');
    };

    // Helper functions
    const isUserMessage = (msg) => {
      const messageType = msg.message?.type || msg.message?.role;
      return messageType === 'human' || messageType === 'user';
    };

    const isAiMessage = (msg) => {
      const messageType = msg.message?.type || msg.message?.role;
      return messageType === 'ai' || messageType === 'assistant';
    };

    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
        }
      });
    };

    const handleSendMessage = async (messageText) => {
      const textToSend = messageText || inputMessage.value.trim();
      if (textToSend && props.notebookId) {
        try {
          // Store the pending message to display immediately
          pendingUserMessage.value = textToSend;
          
          await sendMessageAsync({
            notebookId: props.notebookId,
            role: 'user',
            content: textToSend
          });
          
          inputMessage.value = '';
          
          // Show AI loading immediately after sending (like React version)
          showAiLoading.value = true;
          scrollToBottom();
        } catch (error) {
          console.error('Failed to send message:', error);
          // Restore message on error
          inputMessage.value = textToSend;
          pendingUserMessage.value = null;
          showAiLoading.value = false;
        }
      }
    };

    const handleRefreshChat = () => {
      if (props.notebookId) {
        console.log('Refresh button clicked for notebook:', props.notebookId);
        deleteChatHistory(props.notebookId);
        // Reset clicked questions when chat is refreshed
        clickedQuestions.value = new Set();
      }
    };

    const handleCitationClick = (citation) => {
      props.onCitationClick?.(citation);
    };

    const handleExampleQuestionClick = (question) => {
      // Add question to clicked set to remove it from display
      clickedQuestions.value = new Set(clickedQuestions.value).add(question);
      inputMessage.value = question;
      handleSendMessage(question);
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (canSend.value) {
          handleSendMessage();
        }
      }
    };

    const clearChat = () => {
      handleRefreshChat();
    };

    // Watch for message updates to clear pending state and scroll
    watch(() => messages.value, (newMessages, oldMessages) => {
      const oldLength = oldMessages?.length || 0;
      const newLength = newMessages.length;
      
      // If new messages arrived
      if (newLength > oldLength) {
        // Check each new message
        for (let i = oldLength; i < newLength; i++) {
          const message = newMessages[i];
          
          // If this is our pending user message, clear pending (but keep AI loading)
          if (pendingUserMessage.value && isUserMessage(message) && 
              message.message?.content === pendingUserMessage.value) {
            console.log('User message appeared, clearing pending message');
            pendingUserMessage.value = null;
            // Keep showAiLoading.value = true (already set in handleSendMessage)
          }
          
          // If this is an AI response and we're showing loading, clear it
          if (showAiLoading.value && isAiMessage(message)) {
            console.log('AI response arrived, hiding loading');
            showAiLoading.value = false;
          }
        }
      }
      
      scrollToBottom();
    }, { deep: true });

    // Watch for pending message and AI loading to scroll
    watch([pendingUserMessage, showAiLoading], () => {
      scrollToBottom();
    });

    // Dismiss (close) an example question without sending
    const handleDismissExampleQuestion = (question) => {
      clickedQuestions.value = new Set(clickedQuestions.value).add(question);
    };
    // Dismiss (close) the entire example questions panel
    const handleDismissExamplePanel = () => {
      showExamplePanel.value = false;
    }; 

    return {
      // Reactive refs
      inputMessage,
      pendingUserMessage,
      showAiLoading,
      clickedQuestions,
      showExamplePanel,
      messagesContainer,
      messageInput,
      isTyping,
      
      // From hook
      messages,
      isSending,
      isDeletingChatHistory,
      
      // Computed
      isClient,
      canSend,
      shouldShowRefreshButton,
      exampleQuestions,
      
      // Methods
      handleSendMessage,
      handleRefreshChat,
      handleCitationClick,
      handleExampleQuestionClick,
      handleDismissExampleQuestion,
      handleDismissExamplePanel,
      handleKeyDown,
      clearChat,
      getPlaceholderText,
      isUserMessage,
      isAiMessage,
      scrollToBottom
    };
  }
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for messages container */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth transitions for all interactive elements */
button {
  transition: all 0.2s ease-in-out;
}

/* Enhanced focus states */
textarea:focus,
button:focus {
  outline: none;
}

/* Message animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-6 > div {
  animation: fadeInUp 0.3s ease-out;
}

/* Context buttons scroll styling */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Smooth scroll behavior */
.overflow-x-auto {
  scroll-behavior: smooth;
}

/* Context button container styling */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f8fafc;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 2px;
}

/* Arrow button disabled state */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Context button hover effects */
.flex-shrink-0:hover:not(:disabled) {
  transform: scale(1.05);
}

/* Scroll indicators */
.scroll-indicator {
  position: relative;
}

.scroll-indicator::before,
.scroll-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 1;
  transition: opacity 0.3s ease;
}

.scroll-indicator::before {
  left: 0;
  background: linear-gradient(to right, rgba(249, 250, 251, 1), rgba(249, 250, 251, 0));
}

.scroll-indicator::after {
  right: 0;
  background: linear-gradient(to left, rgba(249, 250, 251, 1), rgba(249, 250, 251, 0));
}

/* Touch feedback */
.context-button-touch {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* Animation for button clicks */
@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.context-button-touch:active {
  animation: buttonPress 0.1s ease-in-out;
}
</style>
