<template>
  <!-- If we have a selected citation, show the content viewer in full screen -->
  <div v-if="selectedCitation" class="h-full flex flex-col overflow-hidden">
    <!-- Header with back button -->
    <div class="p-4 border-b border-gray-200 flex-shrink-0">
      <div class="flex items-center justify-between">
        <h2 
          class="text-lg font-medium text-gray-900 cursor-pointer hover:text-gray-700" 
          @click="$emit('citation-close')"
        >
          Sources
        </h2>
        <t-button
          color="secondary-outline"
          size="sm"
          @click="$emit('citation-close')"
          class="p-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor" class="h-6 w-6">
            <path d="M440-440v240h-80v-160H200v-80h240Zm160-320v160h160v80H520v-240h80Z" />
          </svg>
        </t-button>
      </div>
    </div>
    
    <!-- Source Content Viewer -->
    <SourceContentViewer 
      :citation="selectedCitation"
      :source-content="selectedSource?.content || ''"
      :source-summary="selectedSource?.summary || ''"
      :source-description="selectedSource?.description || ''"
      :source-url="selectedSource?.url || ''"
      :is-opened-from-source-list="selectedCitation.citation_id === -1"
      class="flex-1 overflow-hidden"
      @citation-close="$emit('citation-close')"
    />
  </div>

  <!-- Default sources list view -->
  <div v-else class="h-full bg-gray-50 border-r border-gray-200 flex flex-col overflow-hidden">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 flex-shrink-0">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900">{{$t('Sources')}}</h2>
      </div>
      
      <!-- Add Source Button -->
      <div class="px-4 pb-4">
        <t-button
          color="primary-outline"
          class="w-full py-3 flex items-center justify-center space-x-2 border-2 border-dashed border-primary-300 text-primary-700 hover:bg-primary-50 hover:border-primary-400 transition-colors"
          @click="showAddSourcesDialog = true"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          <span class="font-medium">{{$t('Add Sources')}}</span>
        </t-button>
      </div>
    </div>

    <!-- Sources List -->
    <div class="flex-1 h-full overflow-hidden">
      <div class="h-full overflow-y-auto p-4">
        <div v-if="isLoadingSources" class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p class="text-sm text-gray-600">Loading sources...</p>
        </div>

        <div v-else-if="sources.length === 0" class="text-center py-6 sm:py-8">
          <div class="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gray-200 rounded-lg mx-auto mb-3 sm:mb-4 flex items-center justify-center">
            <span class="text-gray-400 text-lg sm:text-xl lg:text-2xl">📄</span>
          </div>
          <h3 class="text-sm sm:text-base lg:text-lg font-medium text-gray-900 mb-1 sm:mb-2">{{$t('Saved sources will appear here')}}</h3>
          <p class="text-xs sm:text-sm text-gray-600 mb-3 sm:mb-4 px-2">{{$t('Click Add source above to add PDFs, text, or audio files.')}}</p>
        </div>

        <div v-else class="space-y-1">
          <div
            v-for="source in sources"
            :key="source.id"
            @click="handleSourceClick(source)"
            class="p-2 sm:p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer transition-colors"
          >
            <div class="flex items-start space-x-2 sm:space-x-3">
              <!-- Source Info -->
              <div class="flex-1 min-w-0">
                <h4 class="text-xs sm:text-sm font-medium text-gray-900 truncate">{{ source.title }}</h4>
                <div class="flex items-center mt-1 text-xs">
                  <span class="text-gray-400">{{ formatDate(source.created_at) }}</span>
                  <span class="mx-1 text-gray-300">•</span>
                  <span class="text-gray-500">{{ source.type.toUpperCase() }}</span>
                </div>
              </div>

              <!-- Status + Actions -->
              <div class="flex items-center space-x-2 flex-shrink-0">
                <!-- Status Icon -->
                <div>
                  <!-- Completed -->
                  <svg v-if="source.processing_status === 'completed'" class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414L8.414 15l-4.121-4.121a1 1 0 011.414-1.414L8.414 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  <!-- Failed -->
                  <svg v-else-if="source.processing_status === 'failed'" class="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  <!-- Uploading -->
                  <svg v-else-if="source.processing_status === 'uploading'" class="h-4 w-4 animate-pulse text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M4 12l8-8 8 8M12 4v12" />
                  </svg>
                  <!-- Processing / Pending -->
                  <svg v-else class="h-4 w-4 animate-spin text-gray-500" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                  </svg>
                </div>
                <!-- Delete -->
                <button
                  @click.stop="handleDeleteSource(source)"
                  class="p-1 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-40"
                  :disabled="['uploading','processing','pending'].includes(source.processing_status)"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Sources Dialog -->
    <AddSourceDialog 
      :open="showAddSourcesDialog" 
      :notebook-id="notebookId"
      @close="showAddSourcesDialog = false"
    />

    <!-- Delete Confirmation Dialog -->
    <TModal :isShow="showDeleteDialog" customClass="sm:max-w-md">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete {{ selectedSource?.title }}?</h3>
        <p class="text-sm text-gray-600 mb-6">
          You're about to delete this source. This cannot be undone.
        </p>
        <div class="flex justify-end space-x-3">
          <t-button color="secondary-outline" @click="showDeleteDialog = false">
            Cancel
          </t-button>
          <t-button 
            color="primary-solid" 
            @click="confirmDelete" 
            :disabled="isDeleting"
            class="bg-red-600 hover:bg-red-700"
          >
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </t-button>
        </div>
      </div>
    </TModal>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';
import SourceContentViewer from './SourceContentViewer.vue';
import AddSourceDialog from './AddSourceDialog.vue';
import TModal from '@/components/global/Modal.vue';
import TButton from '@/components/global/Button.vue';
import { useSources } from '../hooks/useSources.js';
import { useSourceDelete } from '../hooks/useSourceDelete.js';

export default {
  name: 'SourcesSidebar',
  components: {
    SourceContentViewer,
    AddSourceDialog,
    TModal,
    TButton
  },
  props: {
    notebookId: {
      type: String,
      required: true
    },
    selectedCitation: {
      type: Object,
      default: null
    }
  },
  emits: ['citation-click', 'citation-close'],
  setup(props, { emit }) {
    // Hooks
    const { sources, isLoading: isLoadingSources } = useSources(props.notebookId);
    const { deleteSource, isDeleting } = useSourceDelete();

    // Reactive state
    const showAddSourcesDialog = ref(false);
    const showDeleteDialog = ref(false);
    const sourceToDelete = ref(null);

    // Methods
    const handleAddSource = () => {
      showAddSourcesDialog.value = true;
    };

    const handleSourceClick = (source) => {
      // Create a mock citation for the selected source without line data (this prevents auto-scroll)
      const mockCitation = {
        citation_id: -1, // Use negative ID to indicate this is a mock citation
        source_id: source.id,
        source_title: source.title,
        source_type: source.type,
        chunk_index: 0,
        excerpt: 'Full document view'
      };

      // Emit the citation click to parent
      emit('citation-click', mockCitation);
    };

    const handleDeleteSource = (source) => {
      sourceToDelete.value = source;
      showDeleteDialog.value = true;
    };

    const confirmDelete = async () => {
      if (!sourceToDelete.value) return;
      
      try {
        await deleteSource(sourceToDelete.value.id);
        showDeleteDialog.value = false;
        sourceToDelete.value = null;
      } catch (error) {
        console.error('Failed to delete source:', error);
      }
    };

    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    };

    // Computed: Find the selected source by citation.source_id
    const selectedSource = computed(() => {
      if (!props.selectedCitation) return null;
      return sources.value.find(s => s.id === props.selectedCitation.source_id);
    });

    return {
      // Data
      sources,
      isLoadingSources,
      showAddSourcesDialog,
      showDeleteDialog,
      sourceToDelete,
      isDeleting,
      selectedSource,
      // Methods
      handleAddSource,
      handleSourceClick,
      handleDeleteSource,
      confirmDelete,
      formatDate
    };
  }
};
</script>