<template>
  <div class="w-full h-full bg-white flex flex-col overflow-hidden">
    <!-- Debug Info -->
    <div v-if="projectId" class="p-4 bg-blue-100 text-blue-800 text-sm">
      <strong>Debug:</strong> ProjectAssistantView loaded with Project ID: {{ projectId }}
    </div>
    <div v-else class="p-4 bg-red-100 text-red-800 text-sm">
      <strong>Error:</strong> No Project ID found in route params
    </div>

    <NotebookView />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import NotebookView from './NotebookView.vue';
import projectApi from '@/api/project.js';

export default {
  name: 'ProjectAssistantView',
  components: {
    NotebookView,
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // Reactive state
    const isLoading = ref(true);
    const error = ref(null);
    const loadingMessage = ref('Loading project...');
    const project = ref(null);
    const notebookId = ref(null);
    const notebook = ref(null);

    // Get project ID from route params
    const projectId = computed(() => {
      const id = route.params.projectid;
      return id ? String(id) : '';
    });

    const loadProject = async () => {
      console.log('loadProject called with projectId:', projectId.value);

      if (!projectId.value) {
        console.error('No project ID provided');
        error.value = 'No project ID provided';
        isLoading.value = false;
        return;
      }

      try {
        isLoading.value = true;
        error.value = null;

        // Log that we're trying to load the project
        console.log('Attempting to load project with ID:', projectId.value);

        // For now, just set a dummy project to see if this function is being called
        project.value = { id: projectId.value, name: 'Test Project' };
        isLoading.value = false;
      } catch (err) {
        console.error('Error loading project:', err);
        error.value = err.message || 'Failed to load project';
        isLoading.value = false;
      }
    }

    const handleBackToDashboard = () => {
      router.push('/');
    };

    // Watch for route changes
    watch(() => route.params.projectid, (newProjectId) => {
      if (newProjectId && newProjectId !== projectId.value) {
        loadProject();
      }
    });

    // Load on mount
    onMounted(() => {
      loadProject();
    });

    return {
      projectId,
      isLoading,
      error,
      loadingMessage,
      project,
      notebookId,
      notebook,
      projectId,
      handleBackToDashboard,
    };
  },
};
</script>
