<template>
  <div class="text-center py-16">
    <div class="mb-12">
      <h2 class="text-3xl font-medium text-gray-900 mb-4">
        Create your first notebook
      </h2>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        <PERSON><PERSON><PERSON> is an AI-powered research and writing assistant that works best with the sources you upload
      </p>
    </div>

    <!-- Feature Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
      <div class="bg-white rounded-lg border border-gray-200 p-6 text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">PDFs</h3>
        <p class="text-gray-600">Upload research papers, reports, and documents</p>
      </div>

      <div class="bg-white rounded-lg border border-gray-200 p-6 text-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Websites</h3>
        <p class="text-gray-600">Add web pages and online articles as sources</p>
      </div>

      <div class="bg-white rounded-lg border border-gray-200 p-6 text-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Audio</h3>
        <p class="text-gray-600">Include multimedia content in your research</p>
      </div>
    </div>

    <!-- Create Button -->
    <button 
      @click="handleCreateNotebook" 
      :disabled="isCreating"
      class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium inline-flex items-center gap-2 disabled:opacity-50"
    >
      <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
      </svg>
      {{ isCreating ? 'Creating...' : 'Create notebook' }}
    </button>
  </div>
</template>

<script>
export default {
  name: 'EmptyDashboard',
  props: {
    isCreating: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleCreateNotebook() {
      this.$emit('create-notebook');
    }
  }
};
</script>
