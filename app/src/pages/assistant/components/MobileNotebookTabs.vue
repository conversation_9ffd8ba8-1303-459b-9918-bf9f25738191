<template>
  <div class="h-full flex flex-col">
    <!-- Tab Navigation -->
    <div class="bg-white border-b border-gray-200">
      <div class="flex">
        <button
          v-for="tab in filteredTabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'flex-1 px-4 py-2 lg:py-3 text-sm font-medium border-b-2 transition-colors',
            activeTab === tab.id
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          ]"
        >
          <div class="flex items-center justify-center space-x-2">
            <component :is="tab.icon" class="h-5 w-5" />
            <span>{{ tab.label }}</span>
          </div>
        </button>
      </div>
    </div>
    
    <!-- Tab Content -->
    <div class="flex-1 overflow-hidden">
      <!-- Sources Tab -->
      <div v-if="activeTab === 'sources'" class="h-full">
        <SourcesSidebar 
          :hasSource="hasSource" 
          :notebookId="notebookId"
          :selectedCitation="selectedCitation"
          @citation-close="$emit('citation-close')"
          @citation-click="$emit('citation-click', $event)"
        />
      </div>
      
      <!-- Chat Tab -->
      <div v-else-if="activeTab === 'chat'" class="h-full">
        <ChatArea 
          :hasSource="hasSource" 
          :notebookId="notebookId"
          :notebook="notebook"
          @citation-click="$emit('citation-click', $event)"
        />
      </div>
      
      <!-- Studio Tab -->
      <div v-else-if="activeTab === 'studio'" class="h-full">
        <StudioSidebar 
          :notebookId="notebookId" 
          @citation-click="$emit('citation-click', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import SourcesSidebar from './SourcesSidebar.vue';
import ChatArea from './ChatArea.vue';
import StudioSidebar from './StudioSidebar.vue';
import { mapGetters } from 'vuex';

// Icon components
const FolderIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
    </svg>
  `
};

const ChatIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
    </svg>
  `
};

const PencilIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
    </svg>
  `
};

export default {
  name: 'MobileNotebookTabs',
  components: {
    SourcesSidebar,
    ChatArea,
    StudioSidebar,
    FolderIcon,
    ChatIcon,
    PencilIcon,
  },
  props: {
    hasSource: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    },
    selectedCitation: {
      type: Object,
      default: null
    }
  },
  computed: {
    ...mapGetters({
      isClient: 'auth/isClient'
    }),
    filteredTabs() {
      if (!this.isClient) {
        return this.tabs;
      }
      return this.tabs.filter(tab => tab.id === 'chat');
    }
  },
  data() {
    return {
      activeTab: 'chat',
      tabs: [
        { id: 'sources', label: 'Sources', icon: 'FolderIcon' },
        { id: 'chat', label: 'Chat', icon: 'ChatIcon' },
        { id: 'studio', label: 'Notes', icon: 'PencilIcon' }
      ]
    };
  },
  watch: {
    hasSource(newValue) {
      // Auto-switch to chat when sources are available
      if (newValue && this.activeTab === 'sources') {
        this.activeTab = 'chat';
      }
    }
  }
};
</script>
