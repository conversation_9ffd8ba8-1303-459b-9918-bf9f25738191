<template>
  <div class="w-full h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <NotebookHeader
      :title="notebook?.title || 'Untitled Notebook'"
      :notebookId="notebookId"
      @back-to-dashboard="$emit('back-to-dashboard')"
      @title-updated="handleTitleUpdate"
      @settings-click="handleSettingsClick"
    />

    <!-- Main Content Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Desktop Layout (3-column) with max-width constraint -->
      <div v-if="isDesktop" class="flex w-full h-full relative mx-auto">
        <!-- Sources Sidebar - Always visible -->
        <div :class="`${sourcesWidth} flex-shrink-0`" v-if="!isClient">
          <SourcesSidebar
            :notebookId="notebookId"
            :selectedCitation="selectedCitation"
            @citation-close="handleCitationClose"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Chat Area -->
        <div :class="`${isClient ? 'lg:w-[1000px] max-w-[1000px] mx-auto' : chatWidth} flex-shrink-0`">
          <ChatArea
            :hasSource="hasSource"
            :notebookId="notebookId"
            :notebook="notebook"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Studio Sidebar - Always visible -->
        <div :class="`${studioWidth} flex-shrink-0`" v-if="!isClient">
          <StudioSidebar
            :notebookId="notebookId"
            @citation-click="handleCitationClick"
          />
        </div>
      </div>

      <!-- Mobile/Tablet Layout (tabs) -->
      <div v-else class="w-full h-full">
        <MobileNotebookTabs
          :hasSource="hasSource"
          :notebookId="notebookId"
          :notebook="notebook"
          :selectedCitation="selectedCitation"
          @citation-close="handleCitationClose"
          @citation-click="handleCitationClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import NotebookHeader from './NotebookHeader.vue';
import SourcesSidebar from './SourcesSidebar.vue';
import ChatArea from './ChatArea.vue';
import StudioSidebar from './StudioSidebar.vue';
import MobileNotebookTabs from './MobileNotebookTabs.vue';
import { useNotebooks } from '../hooks/useNotebooks.js';
import { useSources } from '../hooks/useSources.js';
import { useStore } from 'vuex';

export default {
  name: 'NotebookView',
  components: {
    NotebookHeader,
    SourcesSidebar,
    ChatArea,
    StudioSidebar,
    MobileNotebookTabs,
  },
  emits: ['back-to-dashboard'],
  setup(props, { emit }) {
    const route = useRoute();
    const router = useRouter();

    // Store access
    const store = useStore();
    // Computed properties from store
    const isClient = computed(() => store.getters['auth/isClient']);
    
    // Get notebookId from route params
    const notebookId = computed(() => {
      const id = route.params.notebookid;
      return id ? String(id) : '';
    });

    // Hooks
    const { notebooks } = useNotebooks();
    const { sources } = useSources(notebookId.value);

    // Reactive state
    const selectedCitation = ref(null);
    const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1200);

    // Computed properties
    const notebook = computed(() => {
      return notebooks.value?.find(n => n.id === notebookId.value) || null;
    });

    const hasSource = computed(() => {
      return sources.value && sources.value.length > 0;
    });

    const isDesktop = computed(() => {
      return windowWidth.value >= 1200;
    });

    const isSourceDocumentOpen = computed(() => {
      return !!selectedCitation.value;
    });

    // Dynamic width calculations for desktop - expand studio when editing notes
    const sourcesWidth = computed(() => {
      return isSourceDocumentOpen.value ? 'w-[35%]' : 'w-[25%]';
    });

    const studioWidth = computed(() => {
      return 'w-[30%]'; // Expanded width for note editing
    });

    const chatWidth = computed(() => {
      return isSourceDocumentOpen.value ? 'w-[35%]' : 'w-[45%]';
    });

    // Methods
    const handleCitationClick = (citation) => {
      selectedCitation.value = citation;
    };

    const handleCitationClose = () => {
      console.log("here is the magic begin")
      selectedCitation.value = null;
    };

    const handleTitleUpdate = (newTitle) => {
      // This would typically trigger a notebook update mutation
      // For now, we'll just emit or handle locally
      console.log('Title updated:', newTitle);
    };

    const handleSettingsClick = () => {
      // Handle settings functionality
      console.log('Settings clicked for notebook:', notebookId);
      // You can add settings modal, navigation, or other functionality here
    };

    const handleResize = () => {
      windowWidth.value = window.innerWidth;
    };

    // Lifecycle
    onMounted(() => {
      // Defensive: if notebookId is missing, redirect to /assistant
      if (!notebookId.value && !isClient.value) {
        router.replace('/assistant');
        return;
      }

      // Add resize listener
      window.addEventListener('resize', handleResize);
    });

    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleResize);
    });

    return {
      // Data
      notebookId,
      notebook,
      sources,
      selectedCitation,
      
      // Computed
      hasSource,
      isDesktop,
      isSourceDocumentOpen,
      sourcesWidth,
      studioWidth,
      chatWidth,
      isClient,
      
      // Methods
      handleCitationClick,
      handleCitationClose,
      handleTitleUpdate,
      handleSettingsClick
    };
  },
};
</script>

<style scoped>
/* Prevent layout shift during initialization */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure smooth width transitions */
.flex-1 {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1), flex 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent content jumping during sidebar transitions */
.min-w-0 {
  min-width: 0;
}

/* Smooth button transitions */
button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover {
  transform: scale(1.05);
}

/* Ensure proper layout constraints */
.max-w-\[1920px\] {
  max-width: 1920px;
}

.max-w-\[1200px\] {
  max-width: 1200px;
}

.max-w-\[800px\] {
  max-width: 800px;
}
</style>
