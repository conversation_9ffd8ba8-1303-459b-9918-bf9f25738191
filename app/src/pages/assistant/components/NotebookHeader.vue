<template>
  <header class="bg-white border-b border-gray-200 shadow-sm px-4 sm:px-6 py-2 sm:py-3 sticky top-0 z-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <!-- Back <PERSON><PERSON> with Chevron Left -->
        <button
          @click="handleBackClick"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          title="Back to AnswerFlow"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
          </svg>
        </button>

        <div class="flex items-center lg:space-x-2">
          <!-- Logo/Icon -->
          <div class="hidden lg:flex items-center justify-center w-8 h-8 bg-orange-500 rounded-lg">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>

          <!-- Editable Title -->
          <input
            v-if="isEditing"
            v-model="editedTitle"
            @keydown="handleKeyDown"
            @blur="handleBlur"
            class="text-base sm:text-lg font-semibold text-gray-900 border-none outline-none bg-transparent truncate max-w-full"
            ref="titleInput"
            :disabled="isUpdating"
          />
          <span
            v-else
            @click="handleTitleClick"
            class="text-base max-w-[180px] lg:max-w-[700px]  sm:text-lg font-semibold text-gray-900 cursor-pointer hover:bg-gray-50 rounded sm:px-2 py-0.5 transition-colors truncate max-w-full"
          >
            {{ title }}
          </span>
        </div>
      </div>

      <!-- Right side - Settings Button and Profile -->
      <div class="flex items-center space-x-3">
        <button
          @click="handleSettingsClick"
          class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          Settings
        </button>

        <!-- Profile Avatar Dropdown -->
        <div class="h-[38px]">
          <div
            class="hs-dropdown inline-flex [--strategy:absolute] [--placement:bottom-right] relative text-start"
            ref="userMenu"
          >
            <button
              id="hs-notebook-dropdown"
              type="button"
              class="inline-flex shrink-0 items-center gap-x-3 text-start rounded-full focus:outline-none"
              aria-haspopup="menu"
              aria-expanded="false"
              aria-label="Dropdown"
              @click="toggleUserMenu"
            >
              <img class="shrink-0 size-[38px] rounded-full object-cover" :src="getUserImage(user)" alt="Avatar" />
            </button>

            <!-- Account Dropdown -->
            <div
              v-show="showUserMenu"
              class="hs-dropdown-menu w-60 transition-[opacity,margin] duration bg-white rounded-xl shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)] z-20"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="hs-notebook-dropdown"
            >
              <div class="p-1 border-b border-gray-200">
                <router-link
                  class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
                  to="/settings/profile"
                  @click="showUserMenu = false"
                >
                  <img class="shrink-0 size-8 rounded-full object-cover" :src="getUserImage(user)" alt="Avatar">
                  <div class="grow">
                    <span class="text-sm font-semibold text-gray-800">
                      {{ user?.first_name || '-'}}
                      {{ user?.last_name}}
                    </span>
                    <p class="text-xs text-gray-500">
                      {{ user?.email || '-'}}
                    </p>
                  </div>
                </router-link>
              </div>
              <div class="px-1 my-1">
                <router-link
                  class="flex items-center gap-x-3 py-1 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
                  to="/settings/profile"
                  @click="showUserMenu = false"
                >
                  Profile
                </router-link>
              </div>
              <div class="px-1 my-1" v-if="!isClient">
                <router-link
                  class="flex items-center gap-x-3 py-1 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
                  to="/settings/change-password"
                  @click="showUserMenu = false"
                >
                  Settings
                </router-link>
              </div>
              <div class="p-1 border-t border-gray-200">
                <router-link
                  class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
                  to="/logout"
                  @click="showUserMenu = false"
                >
                  Sign out
                </router-link>
              </div>
            </div>
            <!-- End Account Dropdown -->
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import globalMixin from '@/mixins/global.js';

import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';

import { useStore } from 'vuex';
import { useNotebookUpdate } from '@/pages/assistant/hooks/useNotebookUpdate.js';

export default {

  name: 'NotebookHeader',
  components: {  },
  props: {
    title: {
      type: String,
      required: true
    },
    notebookId: {
      type: String,
      default: null
    }
  },
  setup(props, { emit }) {
    const router = useRouter();
    
    const store = useStore();
    const user = computed(() => store.getters['auth/user']);
    const isClient = computed(() => store.getters['auth/isClient']);
    const showUserMenu = ref(false);
    const isEditing = ref(false);
    const editedTitle = ref(props.title);
    const { updateNotebook, isUpdating } = useNotebookUpdate();

    watch(() => props.title, (newTitle) => {
      editedTitle.value = newTitle;
    });

    const handleTitleClick = () => {
      if (props.notebookId) {
        isEditing.value = true;
        editedTitle.value = props.title;
        setTimeout(() => {
          if (refs.titleInput) refs.titleInput.focus();
        });
      }
    };

    const handleTitleSubmit = async () => {
      if (
        props.notebookId &&
        editedTitle.value.trim() &&
        editedTitle.value !== props.title
      ) {
        try {
          await new Promise((resolve) => setTimeout(resolve, 100)); // brief debounce for UX
          updateNotebook(
            { id: props.notebookId, updates: { title: editedTitle.value.trim() } },
            {
              onSuccess: () => {
                emit('title-updated', editedTitle.value.trim());
                __showNotif('success', 'Success', 'Title updated successfully');
              },
              onError: (error) => {
                console.error('Failed to update title:', error);
                __showNotif('error', 'Error', 'Failed to update title');
                editedTitle.value = props.title;
              }
            }
          );
        } catch (error) {
          console.error('Failed to update title:', error);
          __showNotif('error', 'Error', 'Failed to update title');
          editedTitle.value = props.title;
        }
      }
      isEditing.value = false;
    };

    // --- Additional methods and refs ---
    const titleInput = ref(null);
    const userMenu = ref(null);

    // Keydown handler
    const handleKeyDown = (event) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        handleTitleSubmit();
      } else if (event.key === 'Escape') {
        editedTitle.value = props.title;
        isEditing.value = false;
      }
    };
    // Blur handler
    const handleBlur = () => {
      handleTitleSubmit();
    };
    // Icon click handler
    const handleIconClick = () => {
      emit('back-to-dashboard');
    };
    // Toggle user menu
    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value;
    };
    // Click outside handler
    const handleClickOutside = (event) => {
      if (userMenu.value && !userMenu.value.contains(event.target)) {
        showUserMenu.value = false;
      }
    };
    // Sign out
    const handleSignOut = () => {
      showUserMenu.value = false;
      router.push('/logout');
    };
    // Settings click
    const handleSettingsClick = () => {
      emit('settings-click');
      console.log('Settings clicked for notebook:', props.notebookId);
    };
    // Back click
    const handleBackClick = () => {
      if (isClient.value) {
        router.push('/');
      } else {
        router.push('/assistant');
      }
    };
    // User image helper
    const getUserImage = (userObj) => {
      if (userObj?.imgUrl) {
        return userObj.imgUrl;
      } else {
        const defaultName = userObj && userObj.first_name ? userObj.first_name : "Planlagt";
        // Use static mixin.methods for initial canvas generation
        if (globalMixin.methods && typeof globalMixin.methods.__generateInitialCanvas === 'function') {
          return globalMixin.methods.__generateInitialCanvas.call(globalMixin.methods, defaultName);
        }
        return '';
      }
    };
    // Mount/unmount for click outside
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
    });
    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside);
    });

    return {
      user,
      isClient,
      showUserMenu,
      isEditing,
      editedTitle,
      isUpdating,
      handleTitleClick,
      handleTitleSubmit,
      handleKeyDown,
      handleBlur,
      handleIconClick,
      toggleUserMenu,
      handleClickOutside,
      handleSignOut,
      handleSettingsClick,
      handleBackClick,
      getUserImage,
      titleInput,
      userMenu
    };
  }
};
</script>
