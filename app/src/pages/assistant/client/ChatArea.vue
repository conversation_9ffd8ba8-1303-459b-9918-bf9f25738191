<template>
  <div class="h-full bg-white flex flex-col border border-gray-200 rounded-lg">
    <!-- Header -->
    <div class="flex items-center justify-between px-3 sm:px-4 lg:px-6 py-2 sm:py-1 lg:py-3 border-b border-gray-200 bg-white">
      <div class="text-sm sm:text-base lg:text-lg font-medium sm:font-semibold text-gray-900">Chat</div>
      <div
        v-if="shouldShowRefreshButton"
        @click="clearChat"
        :disabled="isDeletingChatHistory"
        class="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors cursor-pointer"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
        </svg>
        <span class="hidden sm:inline">{{ isDeletingChatHistory ? 'Clearing...' : 'Clear Chat' }}</span>
        <span class="sm:hidden">{{ isDeletingChatHistory ? 'Clearing...' : 'Clear' }}</span>
      </div>
    </div>

    <!-- Chat Messages -->
    <div class="flex-1 overflow-y-auto p-3 sm:p-4 lg:p-6" ref="messagesContainer">
      <!-- Welcome Message -->
      <div v-if="messages.length === 0 && !pendingUserMessage" class="text-center py-8 sm:py-12 lg:py-16">
        <div class="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <svg class="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h3 class="text-sm sm:text-base lg:text-lg font-medium text-gray-900 mb-1 sm:mb-2">
          Start chatting with AI
        </h3>
        <p class="text-xs sm:text-sm lg:text-base text-gray-600 max-w-xs sm:max-w-sm lg:max-w-md mx-auto px-2">
          Ask me anything! I'm here to help you with questions, analysis, and insights.
        </p>
      </div>
      
      <!-- Messages -->
      <div v-else class="space-y-3 sm:space-y-4 lg:space-y-6">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'flex',
            isUserMessage(message) ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'max-w-[85%] sm:max-w-[80%] lg:max-w-[75%] rounded-lg px-3 sm:px-4 py-2 sm:py-3',
              isUserMessage(message)
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-900 border border-gray-200'
            ]"
          >
            <!-- User Message -->
            <div v-if="isUserMessage(message)" class="text-xs lg:text-sm">
              {{ message.message.content }}
            </div>
            
            <!-- Assistant Message -->
            <div v-else class="space-y-3">
              <MarkdownRenderer
                :content="message.message.content"
                :citations="message.message.citations || []"
                @citation-click="handleCitationClick"
              />

              <!-- Action Buttons -->
              <div class="mt-3 sm:mt-4 pt-2 sm:pt-3 border-t border-gray-200 space-y-3">
                <!-- Feedback and Create Complain -->
                <div class="flex items-center space-x-2">
                  <!-- Thumbs Up -->
                  <t-button v-if="!(message.message?.meta?.isOfferingCreateComplain) && !message.message.content.segments[0].text.startsWith('Thanks')" @click="handleThumbsUp(message)" class="p-1 sm:p-1.5 text-gray-400 hover:text-green-600 transition-colors">
                    <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
                    </svg>
                  </t-button>
                  
                  <!-- Thumbs Down -->
                  <t-button v-if="!(message.message?.meta?.isOfferingCreateComplain) && !message.message.content.segments[0].text.startsWith('Thanks')" @click="handleThumbsDown(message)" class="p-1 sm:p-1.5 text-gray-400 hover:text-red-600 transition-colors">
                    <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.20.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"/>
                    </svg>
                  </t-button>
                  <!-- Create Complain Button -->
                  <button v-if="message.message?.meta?.isOfferingCreateComplain" 
                          @click="handleCreateComplain(message)"
                          class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-orange-700 bg-orange-50 border border-orange-200 rounded-md hover:bg-orange-100 transition-colors">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    Create Complain
                  </button>
                </div>
              </div>

              <!-- Save to Note Button -->
              <div class="flex justify-start mt-2">
                <!-- <SaveToNoteButton
                  :content="message.content"
                  :notebook-id="notebookId"
                /> -->
              </div>
            </div>
          </div>
        </div>
        
        <!-- Pending User Message -->
        <div v-if="pendingUserMessage" class="flex justify-end">
          <div class="max-w-[85%] sm:max-w-[80%] lg:max-w-[75%] rounded-lg px-3 sm:px-4 py-2 sm:py-3 bg-primary-600 text-white">
            <div class="text-xs lg:text-sm">
              {{ pendingUserMessage }}
            </div>
          </div>
        </div>
        
        <!-- AI Loading Indicator -->
        <div v-if="showAiLoading" class="flex justify-start">
          <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-[80%]">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator (Legacy) -->
        <div v-if="isTyping" class="flex justify-start">
          <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-[80%]">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    


    <!-- Input Area -->
    <div class="border-t border-gray-200">
      <div class="p-3 sm:p-4">
        <div class="flex items-end space-x-2 sm:space-x-3">
          <textarea
            ref="messageInput"
            v-model="inputMessage"
            @keydown="handleKeyDown"
            @input="adjustTextareaHeight"
            :placeholder="getPlaceholderText()"
            :disabled="isSending"
            class="flex-1 resize-none border border-gray-300 rounded-lg px-3 sm:px-4 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-gray-50 disabled:cursor-not-allowed transition-all duration-150 ease-in-out"
            rows="1"
            style="min-height: 40px; max-height: 120px; overflow-y: hidden;"
          ></textarea>
          <button
            @click="handleSendMessage()"
            :disabled="!canSend"
            class="px-3 sm:px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            style="height: 40px;"
          >
            <svg v-if="!isSending" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
            <svg v-else class="animate-spin h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import TButton from '@/components/global/Button.vue';
import MarkdownRenderer from '../client/MarkdownRenderer.vue';
import SaveToNoteButton from '../components/SaveToNoteButton.vue';
import { useChatMessages } from '../hooks/useChatMessageClient.js';
import {
		RefreshIcon,
} from '@heroicons/vue/outline';

export default {
  name: 'ChatArea',
  components: {
    TButton,
    MarkdownRenderer,
    SaveToNoteButton,
    RefreshIcon,
  },
  props: {
    notebookId: {
      type: String,
      required: true
    },
    project: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    // Store access
    const store = useStore();

    // Computed properties from store
    const isClient = computed(() => store.getters['auth/isClient']);

    // Chat messages hook - use project ID as session ID
    const projectIdSafe = computed(() => props.notebookId || '');
    const {
      messages,
      sendMessageAsync,
      isSending,
      deleteChatHistory,
      isDeletingChatHistory
    } = useChatMessages(projectIdSafe);

    // Local reactive state
    const inputMessage = ref('');
    const pendingUserMessage = ref(null);
    const showAiLoading = ref(false);
    const messagesContainer = ref(null);
    const messageInput = ref(null);
    const isTyping = ref(false);

    // Computed properties
    const canSend = computed(() => {
      return inputMessage.value.trim() && !isSending.value && !pendingUserMessage.value;
    });

    const shouldShowRefreshButton = computed(() => messages.value.length > 0);

    const getPlaceholderText = () => {
      return "Type your message here...";
    };

    // Helper functions
    const isUserMessage = (msg) => {
      const messageType = msg.message?.type || msg.message?.role;
      return messageType === 'human' || messageType === 'user';
    };

    const isAiMessage = (msg) => {
      const messageType = msg.message?.type || msg.message?.role;
      return messageType === 'ai' || messageType === 'assistant';
    };

    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
        }
      });
    };

    const handleSendMessage = async (messageText) => {
      const textToSend = messageText || inputMessage.value.trim();
      if (textToSend && props.notebookId) {
        try {
          // Store the pending message to display immediately
          pendingUserMessage.value = textToSend;
          
          // Clear input immediately
          inputMessage.value = '';
          resetTextareaHeight();
          
          // Show AI loading immediately after sending (like React version)
          showAiLoading.value = true;
          
          await sendMessageAsync({
            notebookId: props.notebookId,
            role: 'user',
            content: textToSend,
            project: props.project
          });
          
          scrollToBottom();
        } catch (error) {
          console.error('Failed to send message:', error);
          // Restore message on error
          inputMessage.value = textToSend;
          pendingUserMessage.value = null;
          showAiLoading.value = false;
          resetTextareaHeight();
        }
      }
    };

    const handleRefreshChat = () => {
      if (props.notebookId) {
        console.log('Refresh button clicked for notebook:', props.notebookId);
        deleteChatHistory(props.notebookId);
      }
    };



    const handleKeyDown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (canSend.value) {
          handleSendMessage();
        }
      }
    };

    const adjustTextareaHeight = () => {
      if (messageInput.value) {
        const textarea = messageInput.value;
        textarea.style.height = 'auto';
        const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120);
        textarea.style.height = newHeight + 'px';
        
        // Show scrollbars only when height exceeds 40px
        textarea.style.overflowY = newHeight > 40 ? 'auto' : 'hidden';
      }
    };

    const resetTextareaHeight = () => {
      if (messageInput.value) {
        messageInput.value.style.height = '40px';
        messageInput.value.style.overflowY = 'hidden';
      }
    };

    const clearChat = () => {
      handleRefreshChat();
    };

    const handleThumbsDown = (message) => {
      const complaintMessage = "This doesn't help";
      handleSendMessage(complaintMessage);
    };
    const handleCreateComplain = (message) => {
      const complaintMessage = "please make a complaint";
      handleSendMessage(complaintMessage);
    };
    const handleThumbsUp = (message) => {
      const complaintMessage = "This helps, I want to give a feedback";
      handleSendMessage(complaintMessage);
    };
    // Watch for message updates to clear pending state and scroll
    watch(() => messages.value, (newMessages, oldMessages) => {
      const oldLength = oldMessages?.length || 0;
      const newLength = newMessages.length;
      
      // If new messages arrived
      if (newLength > oldLength) {
        // Check each new message
        for (let i = oldLength; i < newLength; i++) {
          const message = newMessages[i];
          
          // If this is our pending user message, clear pending (but keep AI loading)
          if (pendingUserMessage.value && isUserMessage(message) && 
              message.message?.content === pendingUserMessage.value) {
            console.log('User message appeared, clearing pending message');
            pendingUserMessage.value = null;
            // Keep showAiLoading.value = true (already set in handleSendMessage)
          }
          
          // If this is an AI response and we're showing loading, clear it
          if (showAiLoading.value && isAiMessage(message)) {
            console.log('AI response arrived, hiding loading');
            showAiLoading.value = false;
          }
        }
      }
      
      scrollToBottom();
    }, { deep: true });

    // Watch for pending message and AI loading to scroll
    watch([pendingUserMessage, showAiLoading], () => {
      scrollToBottom();
    });

    // Watch for input changes to adjust textarea height
    watch(inputMessage, () => {
      nextTick(() => {
        adjustTextareaHeight();
      });
    });

    onMounted(() => {
      // Set initial height
      nextTick(() => {
        adjustTextareaHeight();
      });
    });

    // Dismiss (close) an example question without sending
    const handleDismissExampleQuestion = (question) => {
      clickedQuestions.value = new Set(clickedQuestions.value).add(question);
    };
    // Dismiss (close) the entire example questions panel
    const handleDismissExamplePanel = () => {
      showExamplePanel.value = false;
    }; 

    return {
      // Reactive refs
      inputMessage,
      pendingUserMessage,
      showAiLoading,
      messagesContainer,
      messageInput,
      isTyping,
      
      // From hook
      messages,
      isSending,
      isDeletingChatHistory,
      
      // Computed
      isClient,
      canSend,
      shouldShowRefreshButton,
      
      // Methods
      handleThumbsDown,
      handleThumbsUp,
      handleCreateComplain,
      handleSendMessage,
      handleRefreshChat,
      handleKeyDown,
      clearChat,
      getPlaceholderText,
      isUserMessage,
      isAiMessage,
      scrollToBottom
    };
  }
};
</script>

<style scoped>
/* Smooth textarea transitions */
textarea {
  transition: height 0.15s ease-in-out;
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
  width: 4px;
}

textarea::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Button transitions - only color now since height is fixed */
button {
  transition: background-color 0.2s ease;
}

/* Focus states with theme consistency */
textarea:focus {
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

/* Disabled state styling */
textarea:disabled {
  background-color: #f8fafc;
  color: #64748b;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
</style>
