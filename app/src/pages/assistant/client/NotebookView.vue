<template>
  <div class="w-full h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <NotebookHeader
      :title="project?.name || 'Untitled Project'"
      :project="project"
      :notebookId="notebookId"
      @back-to-dashboard="$emit('back-to-dashboard')"
      @title-updated="handleTitleUpdate"
      @settings-click="handleSettingsClick"
    />

    <!-- Main Content Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Desktop Layout - Client optimized -->
      <div v-if="isDesktop" class="flex w-full h-full relative mx-auto">
        <!-- Chat Area - Full width for client -->
        <div class="lg:w-[1000px] max-w-[1000px] mx-auto flex-shrink-0">
          <ChatArea
            :notebookId="notebookId"
            :project="project"
          />
        </div>
      </div>

      <!-- Mobile/Tablet Layout (tabs) -->
      <div v-else class="w-full h-full">
        <MobileNotebookTabs
          :notebookId="notebookId"
          :project="project"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import NotebookHeader from './NotebookHeader.vue';
import ChatArea from './ChatArea.vue';
import MobileNotebookTabs from './MobileNotebookTabs.vue';
import { useStore } from 'vuex';
import projectApi from '@/api/project.js';

export default {
  name: 'NotebookView',
  components: {
    NotebookHeader,
    ChatArea,
    MobileNotebookTabs,
  },
  emits: ['back-to-dashboard'],
  setup(props, { emit }) {
    const route = useRoute();
    const router = useRouter();

    // Store access
    const store = useStore();
    // Computed properties from store
    const isClient = computed(() => store.getters['auth/isClient']);
    
    // Get projectId from route params
    const projectId = computed(() => {
      const id = route.params.projectid;
      return id ? String(id) : '';
    });

    // Reactive state
    const project = ref(null);
    const loading = ref(true);
    const error = ref(null);
    const selectedCitation = ref(null);
    const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1200);

    // Computed properties
    const isDesktop = computed(() => {
      return windowWidth.value >= 1200;
    });

    // Methods
    const fetchProject = () => {
      if (!projectId.value) return;
      
      loading.value = true;
      error.value = null;
      
      const callback = (response) => {
        const data = response.data;
        project.value = data;
        loading.value = false;
      };
      
      const errCallback = (err) => {
        console.log(err);
        error.value = err.message || 'Project not found';
        // Fallback to mock data for development
        project.value = {
          id: projectId.value,
          name: 'Sample Project',
          description: 'Project description here',
          color: '#82CC13',
          user: {
            first_name: 'Desidia',
            last_name: 'User',
            email: '<EMAIL>'
          }
        };
        loading.value = false;
      };
      
      projectApi.get(projectId.value, callback, errCallback);
    };

    const handleCitationClose = () => {
      selectedCitation.value = null;
    };

    const handleTitleUpdate = (newTitle) => {
      if (project.value) {
        project.value.name = newTitle;
        // Here you would typically call an API to update the project
        console.log('Project title updated:', newTitle);
      }
    };

    const handleSettingsClick = () => {
      console.log('Settings clicked for project:', projectId.value);
    };

    const handleResize = () => {
      windowWidth.value = window.innerWidth;
    };

    // Lifecycle
    onMounted(() => {
      if (typeof window !== 'undefined') {
        window.addEventListener('resize', handleResize);
      }
      
      // Redirect if no projectId
      if (!projectId.value) {
        router.push('/');
      } else {
        fetchProject();
      }
    });

    onBeforeUnmount(() => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    });

    return {
      // Reactive refs
      notebookId: projectId,
      project,
      loading,
      error,
      selectedCitation,
      windowWidth,
      
      // Computed
      isDesktop,
      
      // Methods
      handleCitationClose,
      handleTitleUpdate,
      handleSettingsClick
    };
  },
};
</script>
