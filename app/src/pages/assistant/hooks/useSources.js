import { computed, watchEffect } from 'vue';
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';
import { useNotebookGeneration } from './useNotebookGeneration.js';

export function useSources(notebookId) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { generateNotebookContentAsync } = useNotebookGeneration();

  const sourcesQuery = useQuery({
    queryKey: computed(() => ['sources', notebookId]),
    queryFn: async () => {
      // Always use the primitive value for notebookId (handle ref/computed)
      const notebookIdValue = typeof notebookId === 'object' && 'value' in notebookId ? notebookId.value : notebookId;
      if (!notebookIdValue) return [];
      const { data, error } = await supabase
        .from('sources')
        .select('*')
        .eq('notebook_id', notebookIdValue)
        .order('created_at', { ascending: false });
      if (error) throw error;
      return data;
    },
    enabled: computed(() => !!notebookId),
  });

  watchEffect(onInvalidate => {
    if (!notebookId || !user.value) return;

    // Always use primitive value for notebookId in realtime filter
    const notebookIdValue = typeof notebookId === 'object' && 'value' in notebookId ? notebookId.value : notebookId;

    const channel = supabase
      .channel('sources-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'sources', filter: `notebook_id=eq.${notebookIdValue}` }, payload => {
        queryClient.setQueryData(['sources', notebookId], old => {
          const items = old || [];
          const { eventType, new: newRec, old: oldRec } = payload;
          if (eventType === 'INSERT') {
            // If the record already exists, replace it instead of adding a duplicate
            const existingIdx = items.findIndex(i => i.id === newRec.id);
            if (existingIdx !== -1) {
              items[existingIdx] = newRec;
              return [...items];
            }
            // If there is an optimistic placeholder (temp-*) for this record, replace it
            const placeholderIdx = items.findIndex(i => String(i.id).startsWith('temp-') && i.title === newRec.title);
            if (placeholderIdx !== -1) {
              items[placeholderIdx] = newRec;
              return [...items];
            }
            // Otherwise, prepend the new record
            return [newRec, ...items];
          }
          if (eventType === 'UPDATE') return items.map(i => i.id === newRec.id ? newRec : i);
          if (eventType === 'DELETE') return items.filter(i => i.id !== oldRec.id);
          return items;
        });
      })
      .subscribe();

    onInvalidate(() => supabase.removeChannel(channel));
  });

  const addSource = useMutation({
    mutationFn: async sourceData => {
      if (!user.value) throw new Error('User not authenticated');
      // Always use primitive value for notebook_id
      const notebookIdValue = typeof sourceData.notebookId === 'object' && 'value' in sourceData.notebookId ? sourceData.notebookId.value : sourceData.notebookId;
      const { data, error } = await supabase
        .from('sources')
        .insert({ notebook_id: notebookIdValue, title: sourceData.title, type: sourceData.type, content: sourceData.content, url: sourceData.url, file_path: sourceData.file_path, file_size: sourceData.file_size, processing_status: sourceData.processing_status, metadata: sourceData.metadata })
        .select()
        .single();
      if (error) throw error;
      return data;
    },
    onMutate: async (sourceData) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries(['sources', notebookId]);
      
      // Snapshot the previous value
      const previousSources = queryClient.getQueryData(['sources', notebookId]);
      
      // Optimistically update to the new value
      const optimisticSource = {
        id: `temp-${Date.now()}`, // Temporary ID
        title: sourceData.title,
        type: sourceData.type,
        content: sourceData.content,
        url: sourceData.url,
        file_path: sourceData.file_path,
        file_size: sourceData.file_size,
        processing_status: sourceData.processing_status || 'pending',
        metadata: sourceData.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      queryClient.setQueryData(['sources', notebookId], old => {
        return [...(old || []), optimisticSource];
      });
      
      // Return a context object with the snapshotted value
      return { previousSources, optimisticSource };
    },
    onError: (err, sourceData, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousSources) {
        queryClient.setQueryData(['sources', notebookId], context.previousSources);
      }
    },
    onSuccess: async (newSource, variables, context) => {
      // Replace optimistic source with real source data
      queryClient.setQueryData(['sources', notebookId], old => {
        if (!old) return [newSource];
        // Replace the optimistic source (temp ID) with the real one
        return old.map(source => 
          source.id === context?.optimisticSource?.id ? newSource : source
        );
      });
      
      const current = queryClient.getQueryData(['sources', notebookId]) || [];
      if (current.length === 1 && notebookId) {
        const notebookIdValue = typeof notebookId === 'object' && 'value' in notebookId ? notebookId.value : notebookId;
        const { data: notebook } = await supabase.from('notebooks').select('generation_status').eq('id', notebookIdValue).single();
        if (notebook && notebook.generation_status === 'pending') {
          const canGen = (newSource.type === 'pdf' && newSource.file_path) || (newSource.type === 'text' && newSource.content) || (newSource.type === 'website' && newSource.url) || (newSource.type === 'youtube' && newSource.url) || (newSource.type === 'audio' && newSource.file_path);
          if (canGen) await generateNotebookContentAsync({ notebookId: notebookIdValue, filePath: newSource.file_path || newSource.url, sourceType: newSource.type });
        }
      }
    },
  });

  const updateSource = useMutation({
    mutationFn: async ({ sourceId, updates }) => {
      const { data, error } = await supabase.from('sources').update(updates).eq('id', sourceId).select().single();
      if (error) throw error;
      return data;
    },
    onSuccess: async updatedSource => {
      // Immediately update the cached sources list with the updated source data
      queryClient.setQueryData(['sources', notebookId], old => {
        if (!old) return [updatedSource];
        return old.map(source => source.id === updatedSource.id ? { ...source, ...updatedSource } : source);
      });

      // If this is the only source and the notebook is still pending, trigger generation
      if (updatedSource.file_path && notebookId) {
        const items = queryClient.getQueryData(['sources', notebookId]) || [];
        if (items.length === 1) {
          const notebookIdValue = typeof notebookId === 'object' && 'value' in notebookId ? notebookId.value : notebookId;
          const { data: notebook } = await supabase.from('notebooks').select('generation_status').eq('id', notebookIdValue).single();
          if (notebook && notebook.generation_status === 'pending') {
            await generateNotebookContentAsync({ notebookId: notebookIdValue, filePath: updatedSource.file_path, sourceType: updatedSource.type });
          }
        }
      }
    },
  });

  return {
    sources: computed(() => sourcesQuery.data.value || []),
    isLoading: computed(() => sourcesQuery.isLoading.value),
    addSourceAsync: addSource.mutateAsync,
    isAdding: computed(() => addSource.isLoading.value),
    updateSource: updateSource.mutate,
    isUpdating: computed(() => updateSource.isLoading.value),
  };
}
