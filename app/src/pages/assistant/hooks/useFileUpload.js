import { ref } from 'vue';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';

export function useFileUpload() {
  const isUploading = ref(false);

  async function uploadFile(file, notebookId, sourceId) {
    try {
      isUploading.value = true;
      const fileExtension = file.name.split('.').pop() || 'bin';
      const filePath = `${notebookId}/${sourceId}.${fileExtension}`;
      console.log('Uploading file to:', filePath);
      const { data, error } = await supabase
        .storage
        .from('sources')
        .upload(filePath, file, { cacheControl: '3600', upsert: false });
      if (error) {
        console.error('Upload error:', error);
        throw error;
      }
      console.log('File uploaded successfully:', data);
      return filePath;
    } catch (err) {
      console.error('File upload failed:', err);
      return null;
    } finally {
      isUploading.value = false;
    }
  }

  function getFileUrl(filePath) {
    const { data } = supabase
      .storage
      .from('sources')
      .getPublicUrl(filePath);
    return data.publicUrl;
  }

  return {
    uploadFile,
    getFileUrl,
    isUploading,
  };
}
