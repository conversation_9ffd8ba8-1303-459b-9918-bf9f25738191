import { computed } from 'vue';
import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

export function useSourceDelete() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const mutation = useMutation({
    mutationFn: async (sourceId) => {
      const { data: source, error: fetchError } = await supabase
        .from('sources')
        .select('id, title, file_path')
        .eq('id', sourceId)
        .single();
      if (fetchError) throw new Error('Failed to find source');

      if (source.file_path) {
        await supabase.storage.from('sources').remove([source.file_path]);
      }

      const { error: deleteError } = await supabase
        .from('sources')
        .delete()
        .eq('id', sourceId);
      if (deleteError) throw deleteError;

      return source;
    },
    onSuccess: (deleted) => {
      queryClient.invalidateQueries(['sources']);
    },
    onError: (error) => {
      console.error('Error deleting source:', error);
    },
  });

  return {
    deleteSource: mutation.mutate,
    isDeleting: mutation.isLoading,
  };
}
