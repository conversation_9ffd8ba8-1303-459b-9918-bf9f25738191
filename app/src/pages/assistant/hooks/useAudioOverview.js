import { ref, watchEffect, computed } from 'vue';
import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';

export function useAudioOverview(notebookId) {
  const isGenerating = ref(false);
  const generationStatus = ref(null);
  const isAutoRefreshing = ref(false);
  const queryClient = useQueryClient();

  // Realtime subscription for notebook audio status updates
  watchEffect(onInvalidate => {
    if (!notebookId) return;

    const channel = supabase
      .channel('notebook-audio-updates')
      .on(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'notebooks', filter: `id=eq.${notebookId}` },
        payload => {
          const newData = payload.new;
          if (newData.audio_overview_generation_status) {
            generationStatus.value = newData.audio_overview_generation_status;
            if (newData.audio_overview_generation_status === 'completed' && newData.audio_overview_url) {
              isGenerating.value = false;
              queryClient.invalidateQueries(['notebooks']);
            } else if (newData.audio_overview_generation_status === 'failed') {
              isGenerating.value = false;
            }
          }
        }
      )
      .subscribe();

    onInvalidate(() => supabase.removeChannel(channel));
  });

  const generateMutation = useMutation({
    mutationFn: async nb => {
      isGenerating.value = true;
      generationStatus.value = 'generating';
      const res = await supabase.functions.invoke('generate-audio-overview', { body: { notebookId: nb } });
      if (res.error) throw res.error;
      return res.data;
    },
    onError: error => {
      console.error('Audio generation failed to start:', error);
      isGenerating.value = false;
      generationStatus.value = null;
    },
  });

  const refreshMutation = useMutation({
    mutationFn: async ({ notebookId: nb, silent = false }) => {
      if (!silent) isAutoRefreshing.value = true;
      const res = await supabase.functions.invoke('refresh-audio-url', { body: { notebookId: nb } });
      if (res.error) throw res.error;
      return res.data;
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries(['notebooks']);
      if (!variables.silent) isAutoRefreshing.value = false;
    },
    onError: (error, variables) => {
      console.error('Failed to refresh audio URL:', error);
      if (!variables.silent) {
        isAutoRefreshing.value = false;
      }
    },
  });

  function checkAudioExpiry(expiresAt) {
    if (!expiresAt) return true;
    return new Date(expiresAt) <= new Date();
  }

  async function autoRefreshIfExpired(nb, expiresAt) {
    if (checkAudioExpiry(expiresAt) && !isAutoRefreshing.value && !refreshMutation.isLoading.value) {
      try {
        await refreshMutation.mutateAsync({ notebookId: nb, silent: true });
      } catch (err) {
        console.error('Auto-refresh failed:', err);
      }
    }
  }

  return {
    generateAudioOverview: generateMutation.mutate,
    refreshAudioUrl: nb => refreshMutation.mutate({ notebookId: nb }),
    autoRefreshIfExpired,
    isGenerating: computed(() => isGenerating.value || generateMutation.isLoading.value),
    isAutoRefreshing: computed(() => isAutoRefreshing.value),
    generationStatus: computed(() => generationStatus.value),
    checkAudioExpiry,
  };
}
