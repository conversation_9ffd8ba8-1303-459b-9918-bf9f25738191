import { computed } from 'vue';
import { useMutation } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';

export function useDocumentProcessing() {

  const mutation = useMutation({
    mutationFn: async ({ sourceId, filePath, sourceType }) => {
      console.log('Initiating document processing for:', { sourceId, filePath, sourceType });
      const res = await supabase.functions.invoke('process-document', {
        body: { sourceId, filePath, sourceType }
      });
      if (res.error) {
        console.error('Document processing error:', res.error);
        throw res.error;
      }
      return res.data;
    },
    onSuccess: data => {
      console.log('Document processing initiated successfully:', data);
    },
    onError: error => {
      console.error('Failed to initiate document processing:', error);
    },
  });

  return {
    processDocument: mutation.mutate,
    processDocumentAsync: mutation.mutateAsync,
    isProcessing: computed(() => mutation.isLoading.value),
  };
}
