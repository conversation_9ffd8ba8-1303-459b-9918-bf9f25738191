import { computed } from 'vue';
import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';

export function useNotebookGeneration() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ notebookId, filePath, sourceType }) => {
      const { data, error } = await supabase.functions.invoke('generate-notebook-content', {
        body: { notebookId, filePath, sourceType },
      });
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['notebooks']);
      queryClient.invalidateQueries(['notebook']);
      console.log('Notebook generated successfully');
    },
    onError: (error) => {
      console.error('Error generating notebook:', error);
    },
  });

  return {
    generateNotebookContent: mutation.mutate,
    generateNotebookContentAsync: mutation.mutateAsync,
    isGenerating: computed(() => mutation.isLoading.value),
  };
}
