import { computed, watchEffect } from 'vue';
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

// Transforms raw Supabase chat history item to enhanced format
function transformMessage(item, sourceMap) {
  console.log('Processing item:', item);
  let transformed;
  const msg = item.message;
  if (msg && typeof msg === 'object' && !Array.isArray(msg) && 'type' in msg && 'content' in msg) {
    const o = msg;
    if (o.type === 'ai' && typeof o.content === 'string') {
      try {
        const parsed = JSON.parse(o.content);
        if (parsed.output && Array.isArray(parsed.output)) {
          const segments = [];
          const citations = [];
          let cid = 1;
          parsed.output.forEach(out => {
            segments.push({ text: out.text, citation_id: out.citations?.length ? cid : undefined });
            if (out.citations?.length) {
              out.citations.forEach(c => {
                const info = sourceMap.get(c.chunk_source_id);
                citations.push({
                  citation_id: cid,
                  source_id: c.chunk_source_id,
                  source_title: info?.title || 'Unknown Source',
                  source_type: info?.type || 'pdf',
                  chunk_lines_from: c.chunk_lines_from,
                  chunk_lines_to: c.chunk_lines_to,
                  chunk_index: c.chunk_index,
                  excerpt: `Lines ${c.chunk_lines_from}-${c.chunk_lines_to}`
                });
              });
              cid++;
            }
          });
          transformed = { type: 'ai', content: { segments, citations }, additional_kwargs: o.additional_kwargs, response_metadata: o.response_metadata, tool_calls: o.tool_calls, invalid_tool_calls: o.invalid_tool_calls };
        } else {
          transformed = { type: 'ai', content: o.content, additional_kwargs: o.additional_kwargs, response_metadata: o.response_metadata, tool_calls: o.tool_calls, invalid_tool_calls: o.invalid_tool_calls };
        }
      } catch (e) {
        console.warn('Failed to parse AI content:', e);
        transformed = { type: 'ai', content: o.content, additional_kwargs: o.additional_kwargs, response_metadata: o.response_metadata, tool_calls: o.tool_calls, invalid_tool_calls: o.invalid_tool_calls };
      }
    } else {
      transformed = { type: o.type === 'human' ? 'human' : 'ai', content: o.content || '', additional_kwargs: o.additional_kwargs, response_metadata: o.response_metadata, tool_calls: o.tool_calls, invalid_tool_calls: o.invalid_tool_calls };
    }
  } else if (typeof msg === 'string') {
    transformed = { type: 'human', content: msg };
  } else {
    transformed = { type: 'human', content: 'Unable to parse message' };
  }
  console.log('Transformed message:', transformed);
  return { id: item.id, session_id: item.session_id, message: transformed };
}

export function useChatMessages(notebookId) {
  // Support both ref and string input
  const notebookIdStr = computed(() => {
    if (!notebookId) return '';
    if (typeof notebookId === 'object' && notebookId !== null && 'value' in notebookId) {
      return notebookId.value ? String(notebookId.value) : '';
    }
    return String(notebookId);
  });
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Early return: if notebookIdStr is falsy, return safe empty values
  if (!notebookIdStr.value) {
    return {
      messages: computed(() => []),
      isLoading: computed(() => false),
      error: computed(() => null),
      sendMessage: () => {},
      sendMessageAsync: async () => {},
      isSending: computed(() => false),
      deleteChatHistory: () => {},
      isDeletingChatHistory: computed(() => false),
    };
  }

  const messagesQuery = useQuery({
    queryKey: computed(() => ['chat-messages', notebookIdStr.value]),
    queryFn: async () => {
      if (!notebookIdStr.value) return [];
      const { data, error } = await supabase
        .from('n8n_chat_histories')
        .select('*')
        .eq('session_id', notebookIdStr.value)
        .order('id', { ascending: true });
      if (error) throw error;
      const { data: sourcesData } = await supabase
        .from('sources')
        .select('id, title, type')
        .eq('notebook_id', notebookIdStr.value);
      const sourceMap = new Map((sourcesData || []).map(s => [s.id, s]));
      return data.map(item => transformMessage(item, sourceMap));
    },
    enabled: computed(() => !!notebookIdStr.value && !!user.value),
    refetchOnMount: true,
    refetchOnReconnect: true,
  });

  watchEffect(onInvalidate => {
    if (!notebookIdStr.value || !user.value) return;
    const channel = supabase
      .channel('chat-messages')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'n8n_chat_histories', filter: `session_id=eq.${notebookIdStr.value}` },
        async payload => {
          const { data: sourcesData } = await supabase
            .from('sources')
            .select('id, title, type')
            .eq('notebook_id', notebookIdStr.value);
          const sourceMap = new Map((sourcesData || []).map(s => [s.id, s]));
          const newMsg = transformMessage(payload.new, sourceMap);
          queryClient.setQueryData(['chat-messages', notebookIdStr.value], old => {
            const arr = old || [];
            if (arr.some(m => m.id === newMsg.id)) return arr;
            return [...arr, newMsg];
          });
        }
      )
      .subscribe();
    onInvalidate(() => supabase.removeChannel(channel));
  });

  const sendMessageMutation = useMutation({
    mutationFn: async ({ notebookId: nb, role, content }) => {
      if (!user.value) throw new Error('User not authenticated');
      const res = await supabase.functions.invoke('send-chat-message', {
        body: { 
          session_id: nb, 
          message: content, 
          user_id: user.value.id,
          data: {
            answer_flow_ids: nb
          }
        }
      });
      if (res.error) throw new Error(`Webhook error: ${res.error.message}`);
      return res.data;
    },
    onSuccess: () => console.log('Message sent to webhook successfully'),
  });

  const deleteChatHistoryMutation = useMutation({
    mutationFn: async id => {
      if (!user.value) throw new Error('User not authenticated');
      const { error } = await supabase.from('n8n_chat_histories').delete().eq('session_id', id);
      if (error) throw error;
      return id;
    },
    onSuccess: id => {
      queryClient.setQueryData(['chat-messages', id], []);
      queryClient.invalidateQueries(['chat-messages', id]);
    },
    onError: err => {
    },
  });

  return {
    messages: computed(() => messagesQuery.data.value || []),
    isLoading: computed(() => messagesQuery.isLoading.value),
    error: computed(() => messagesQuery.error.value),
    sendMessage: sendMessageMutation.mutate,
    sendMessageAsync: sendMessageMutation.mutateAsync,
    isSending: computed(() => (typeof sendMessageMutation.isLoading === 'object' && sendMessageMutation.isLoading !== null)
      ? sendMessageMutation.isLoading.value
      : !!sendMessageMutation.isLoading),
    deleteChatHistory: deleteChatHistoryMutation.mutate,
    isDeletingChatHistory: computed(() => (typeof deleteChatHistoryMutation.isLoading === 'object' && deleteChatHistoryMutation.isLoading !== null)
      ? deleteChatHistoryMutation.isLoading.value
      : !!deleteChatHistoryMutation.isLoading),
  };
}
