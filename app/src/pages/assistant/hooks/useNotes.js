import { computed } from 'vue';
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

export function useNotes(notebookId) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const notesQuery = useQuery({
    queryKey: computed(() => ['notes', notebookId]),
    queryFn: async () => {
      if (!notebookId) return [];
      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('notebook_id', notebookId)
        .order('updated_at', { ascending: false });
      if (error) throw error;
      return data;
    },
    enabled: computed(() => !!notebookId && !!user.value),
  });

  const createNoteMutation = useMutation({
    mutationFn: async ({ title, content, source_type = 'user', extracted_text }) => {
      if (!notebookId) throw new Error('Notebook ID is required');
      const { data, error } = await supabase
        .from('notes')
        .insert([{ notebook_id: notebookId, title, content, source_type, extracted_text }])
        .select()
        .single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['notes', notebookId]),
  });

  const updateNoteMutation = useMutation({
    mutationFn: async ({ id, title, content }) => {
      const { data, error } = await supabase
        .from('notes')
        .update({ title, content, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['notes', notebookId]),
  });

  const deleteNoteMutation = useMutation({
    mutationFn: async (id) => {
      const { error } = await supabase.from('notes').delete().eq('id', id);
      if (error) throw error;
    },
    onSuccess: () => queryClient.invalidateQueries(['notes', notebookId]),
  });

  return {
    notes: computed(() => notesQuery.data.value || []),
    isLoading: computed(() => notesQuery.isLoading.value),
    createNote: createNoteMutation.mutate,
    isCreating: computed(() => createNoteMutation.isPending?.value || false),
    updateNote: updateNoteMutation.mutate,
    isUpdating: computed(() => updateNoteMutation.isPending?.value || false),
    deleteNote: deleteNoteMutation.mutate,
    isDeleting: computed(() => deleteNoteMutation.isPending?.value || false),
  };
}
