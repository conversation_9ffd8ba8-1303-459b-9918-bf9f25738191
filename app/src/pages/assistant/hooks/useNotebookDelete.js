import { computed } from 'vue';
import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

export function useNotebookDelete() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const mutation = useMutation({
    mutationFn: async (notebookId) => {
      // Get notebook for title
      const { data: notebook, error: fetchError } = await supabase
        .from('notebooks')
        .select('id, title')
        .eq('id', notebookId)
        .single();
      if (fetchError) {
        throw new Error('Failed to find notebook');
      }

      // Fetch sources file_paths
      const { data: sources, error: sourcesError } = await supabase
        .from('sources')
        .select('file_path')
        .eq('notebook_id', notebookId);
      if (sourcesError) {
        throw new Error('Failed to fetch sources for cleanup');
      }

      // Delete files from storage
      const paths = (sources || [])
        .map(s => s.file_path)
        .filter(Boolean);
      if (paths.length) {
        await supabase.storage.from('sources').remove(paths);
      }

      // Delete notebook record (cascades sources)
      const { error: deleteError } = await supabase
        .from('notebooks')
        .delete()
        .eq('id', notebookId);
      if (deleteError) {
        throw deleteError;
      }

      return notebook;
    },
    onSuccess: (deletedNotebook, notebookId) => {
      queryClient.invalidateQueries(['notebooks', user.value?.id]);
      queryClient.invalidateQueries(['sources', notebookId]);
      queryClient.invalidateQueries(['notebook', notebookId]);
    },
    onError: (error) => {
      console.error('Error deleting notebook:', error);
    },
  });

  return {
    deleteNotebook: mutation.mutate,
    isDeleting: computed(() => (typeof mutation.isLoading === 'object' && mutation.isLoading !== null)
  ? mutation.isLoading.value
  : !!mutation.isLoading),
  };
}
