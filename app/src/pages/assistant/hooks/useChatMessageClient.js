import { computed, watchEffect } from 'vue';
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query';
import { v5 as uuidv5 } from 'uuid';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';
import { useStore } from 'vuex';

// Transforms raw Supabase chat history item to enhanced format
function transformMessage(item, sourceMap) {
  console.log('Processing item:', item);
  let transformed;
  const msg = item.message;
  if (msg && typeof msg === 'object' && !Array.isArray(msg) && 'type' in msg && 'content' in msg) {
    const o = msg;
    if (o.type === 'ai' && typeof o.content === 'string') {
      try {
        const parsed = JSON.parse(o.content);
        console.log('fikuri Processing item:', parsed);
        if (parsed.output && Array.isArray(parsed.output)) {
          const segments = [];
          const citations = [];
          const meta = {};
          let cid = 1;
          parsed.output.forEach(out => {
            meta.isOfferingCreateComplain = out.isOfferingCreateComplain; 
            meta.isGetAllComplainStatus = out.isGetAllComplainStatus;
            segments.push({ text: out.text, 
              citation_id: out.citations?.length ? cid : undefined });
            if (out.citations?.length) {
              out.citations.forEach(c => {
                const info = sourceMap.get(c.chunk_source_id);
                citations.push({
                  citation_id: cid,
                  source_id: c.chunk_source_id,
                  source_title: info?.title || 'Unknown Source',
                  source_type: info?.type || 'pdf',
                  chunk_lines_from: c.chunk_lines_from,
                  chunk_lines_to: c.chunk_lines_to,
                  chunk_index: c.chunk_index,
                  excerpt: `Lines ${c.chunk_lines_from}-${c.chunk_lines_to}`
                });
              });
              cid++;
            }
          });
          transformed = { 
            type: 'ai', 
            content: { segments, citations }, 
            additional_kwargs: o.additional_kwargs, 
            response_metadata: o.response_metadata, 
            tool_calls: o.tool_calls, 
            invalid_tool_calls: o.invalid_tool_calls,
            meta: meta,
          };
        } else {
          transformed = { 
            type: 'ai', 
            content: o.content, 
            additional_kwargs: o.additional_kwargs, 
            response_metadata: o.response_metadata, 
            tool_calls: o.tool_calls, 
            invalid_tool_calls: o.invalid_tool_calls,
          };
        }
      } catch (e) {
        console.warn('Failed to parse AI content:', e);
        transformed = { 
          type: 'ai', 
          content: o.content, 
          additional_kwargs: o.additional_kwargs, 
          response_metadata: o.response_metadata, 
          tool_calls: o.tool_calls, 
          invalid_tool_calls: o.invalid_tool_calls,
        };
      }
    } else {
      transformed = { 
        type: o.type === 'human' ? 'human' : 'ai', 
        content: o.content || '', 
        additional_kwargs: o.additional_kwargs, 
        response_metadata: o.response_metadata, 
        tool_calls: o.tool_calls, 
        invalid_tool_calls: o.invalid_tool_calls,
      };
    }
  } else if (typeof msg === 'string') {
    transformed = { type: 'human', content: msg };
  } else {
    transformed = { type: 'human', content: 'Unable to parse message' };
  }
  console.log('Transformed message:', transformed);
  return { id: item.id, session_id: item.session_id, message: transformed };
}

export function useChatMessages(notebookId) {
  // Support both ref and string input
  const notebookIdStr = computed(() => {
    if (!notebookId) return '';
    if (typeof notebookId === 'object' && notebookId.value !== undefined) {
      return String(notebookId.value);
    }
    return String(notebookId);
  });

  const store = useStore();
  const userId = computed(() => store.state.auth?.user?.id);

  // Generate deterministic UUID for session ID using userID and projectID
  const sessionId = computed(() => {
    if (!notebookIdStr.value || !userId.value) return '';

    // Use UUID v5 (namespace-based) to generate deterministic UUID
    const namespace = '6ba7b810-9dad-11d1-80b4-00c04fd430c8'; // DNS namespace
    const name = `${userId.value}-${notebookIdStr.value}`;
    return uuidv5(name, namespace);
  });

  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Early return: if notebookIdStr is falsy, return safe empty values
  if (!notebookIdStr.value) {
    return {
      messages: computed(() => []),
      isLoading: computed(() => false),
      error: computed(() => null),
      sendMessage: () => {},
      sendMessageAsync: async () => {},
      isSending: computed(() => false),
      deleteChatHistory: () => {},
      isDeletingChatHistory: computed(() => false),
    };
  }

  const messagesQuery = useQuery({
    queryKey: computed(() => ['chat-messages', sessionId.value]),
    queryFn: async () => {
      if (!sessionId.value) return [];
      const { data, error } = await supabase
        .from('n8n_chat_histories')
        .select('*')
        .eq('session_id', sessionId.value)
        .order('id', { ascending: true });
      if (error) throw error;
      const { data: sourcesData } = await supabase
        .from('sources')
        .select('id, title, type')
        .eq('notebook_id', notebookIdStr.value);
      const sourceMap = new Map((sourcesData || []).map(s => [s.id, s]));
      return data.map(item => transformMessage(item, sourceMap));
    },
    enabled: computed(() => !!sessionId.value && !!user.value),
    refetchOnMount: true,
    refetchOnReconnect: true,
  });

  watchEffect(onInvalidate => {
    if (!sessionId.value || !user.value) return;
    const channel = supabase
      .channel('chat-messages')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'n8n_chat_histories', filter: `session_id=eq.${sessionId.value}` },
        async payload => {
          const { data: sourcesData } = await supabase
            .from('sources')
            .select('id, title, type')
            .eq('notebook_id', notebookIdStr.value);
          const sourceMap = new Map((sourcesData || []).map(s => [s.id, s]));
          const newMsg = transformMessage(payload.new, sourceMap);
          queryClient.setQueryData(['chat-messages', sessionId.value], old => {
            const arr = old || [];
            if (arr.some(m => m.id === newMsg.id)) return arr;
            return [...arr, newMsg];
          });
        }
      )
      .subscribe();
    onInvalidate(() => supabase.removeChannel(channel));
  });

  const sendMessageMutation = useMutation({
    mutationFn: async ({ notebookId: nb, role, content, project }) => {
      if (!user.value) throw new Error('User not authenticated');
      if (!userId.value) throw new Error('User ID not available');
      
      // Generate deterministic UUID for session ID
      const namespace = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';
      const name = `${userId.value}-${String(nb)}`;
      const session_id = uuidv5(name, namespace);
      
      // Extract answer_flow_ids from project data
      let answer_flow_ids = '';
      
      if (project?.answer_flow_ids) {
        if (Array.isArray(project.answer_flow_ids)) {
          answer_flow_ids = project.answer_flow_ids.join(',');
        } else if (typeof project.answer_flow_ids === 'string') {
          // Handle JSON array string format like "["uuid1","uuid2"]"
          try {
            const parsed = JSON.parse(project.answer_flow_ids);
            if (Array.isArray(parsed)) {
              answer_flow_ids = parsed.join(',');
            } else {
              answer_flow_ids = project.answer_flow_ids;
            }
            console.log(answer_flow_ids)
          } catch (e) {
            // If it's already a plain comma-separated string, use as-is
            answer_flow_ids = project.answer_flow_ids;
          }
        }
      }
      
      const res = await supabase.functions.invoke('send-chat-message', {
        body: { 
          session_id, 
          message: content, 
          user_id: user.value.id,
          project_id: project.id,
          data: {
            answer_flow_ids: answer_flow_ids,
            sender_id: userId.value
          }
        }
      });
      if (res.error) throw new Error(`Webhook error: ${res.error.message}`);
      return res.data;
    },
    onSuccess: () => console.log('Message sent to webhook successfully'),
  });

  const deleteChatHistoryMutation = useMutation({
    mutationFn: async id => {
      if (!user.value) throw new Error('User not authenticated');
      if (!userId.value) throw new Error('User ID not available');
      
      // Generate deterministic UUID for session ID
      const namespace = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';
      const name = `${userId.value}-${String(id)}`;
      const session_id = uuidv5(name, namespace);
      
      const { error } = await supabase.from('n8n_chat_histories').delete().eq('session_id', session_id);
      if (error) throw error;
      return session_id;
    },
    onSuccess: session_id => {
      queryClient.setQueryData(['chat-messages', session_id], []);
      queryClient.invalidateQueries(['chat-messages', session_id]);
    },
    onError: err => {
    },
  });

  return {
    messages: computed(() => messagesQuery.data.value || []),
    isLoading: computed(() => messagesQuery.isLoading.value),
    error: computed(() => messagesQuery.error.value),
    sendMessage: sendMessageMutation.mutate,
    sendMessageAsync: sendMessageMutation.mutateAsync,
    isSending: computed(() => (typeof sendMessageMutation.isLoading === 'object' && sendMessageMutation.isLoading !== null)
      ? sendMessageMutation.isLoading.value
      : !!sendMessageMutation.isLoading),
    deleteChatHistory: deleteChatHistoryMutation.mutate,
    isDeletingChatHistory: computed(() => (typeof deleteChatHistoryMutation.isLoading === 'object' && deleteChatHistoryMutation.isLoading !== null)
      ? deleteChatHistoryMutation.isLoading.value
      : !!deleteChatHistoryMutation.isLoading),
  };
}
