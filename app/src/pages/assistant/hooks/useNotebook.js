import { computed, watchEffect } from 'vue';
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

export function useNotebooks() {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  const notebooksQuery = useQuery({
    queryKey: computed(() => ['notebooks', user.value?.id]),
    queryFn: async () => {
      if (!user.value) {
        console.log('No user found, returning empty notebooks array');
        return [];
      }

      console.log('Fetching notebooks for user:', user.value.id);

      const { data: notebooksData, error: notebooksError } = await supabase
        .from('notebooks')
        .select('*')
        .eq('user_id', user.value.id)
        .order('updated_at', { ascending: false });

      if (notebooksError) {
        console.error('Error fetching notebooks:', notebooksError);
        throw notebooksError;
      }

      const notebooksWithCounts = await Promise.all(
        (notebooksData || []).map(async (notebook) => {
          const { count, error: countError } = await supabase
            .from('sources')
            .select('*', { count: 'exact', head: true })
            .eq('notebook_id', notebook.id);

          if (countError) {
            console.error('Error fetching source count for notebook:', notebook.id, countError);
            return { ...notebook, sources: [{ count: 0 }] };
          }

          return { ...notebook, sources: [{ count: count || 0 }] };
        })
      );

      console.log('Fetched notebooks:', notebooksWithCounts.length);
      return notebooksWithCounts;
    },
    enabled: computed(() => isAuthenticated.value && !authLoading.value),
    retry: (failureCount, error) => {
      if (error?.message?.includes('JWT') || error?.message?.includes('auth')) {
        return false;
      }
      return failureCount < 3;
    },
  });

  watchEffect((onInvalidate) => {
    if (!user.value?.id || !isAuthenticated.value) return;

    console.log('Setting up real-time subscription for notebooks');

    const channel = supabase
      .channel('notebooks-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notebooks',
          filter: `user_id=eq.${user.value.id}`,
        },
        (payload) => {
          console.log('Real-time notebook update received:', payload);
          queryClient.invalidateQueries(['notebooks', user.value.id]);
        }
      )
      .subscribe();

    onInvalidate(() => {
      console.log('Cleaning up real-time subscription');
      supabase.removeChannel(channel);
    });
  });

  const createNotebookMutation = useMutation({
    mutationFn: async (notebookData) => {
      console.log('Creating notebook with data:', notebookData);
      console.log('Current user:', user.value?.id);

      if (!user.value) {
        console.error('User not authenticated');
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('notebooks')
        .insert({
          title: notebookData.title,
          description: notebookData.description,
          user_id: user.value.id,
          generation_status: 'pending',
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating notebook:', error);
        throw error;
      }

      console.log('Notebook created successfully:', data);
      return data;
    },
    onSuccess: () => {
      console.log('Mutation success, invalidating queries');
      queryClient.invalidateQueries(['notebooks', user.value?.id]);
    },
    onError: (error) => {
      console.error('Mutation error:', error);
    },
  });

  const notebooks = computed(() => notebooksQuery.data.value || []);
  const isLoading = computed(() => authLoading.value || notebooksQuery.isLoading.value);
  const error = computed(() => notebooksQuery.error.value?.message || null);
  const isError = computed(() => notebooksQuery.isError.value);

  return {
    notebooks,
    isLoading,
    error,
    isError,
    createNotebook: createNotebookMutation.mutate,
    isCreating: computed(() => (typeof createNotebookMutation.isLoading === 'object' && createNotebookMutation.isLoading !== null)
  ? createNotebookMutation.isLoading.value
  : !!createNotebookMutation.isLoading),
  };
}
