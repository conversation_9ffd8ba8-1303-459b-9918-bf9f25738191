import { ref, computed, watchEffect } from 'vue';
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

export function useNotebooks() {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  const notebooksQuery = useQuery({
    queryKey: computed(() => ['notebooks', user.value?.id]),
    queryFn: async () => {
      if (!user.value) return [];
      console.log('Fetching notebooks for user:', user.value.id);
      const { data: notebooksData, error: notebooksError } = await supabase
        .from('notebooks')
        .select('*')
        .eq('user_id', user.value.id)
        .order('updated_at', { ascending: false });
      if (notebooksError) throw notebooksError;
      const notebooksWithCounts = await Promise.all(
        (notebooksData || []).map(async notebook => {
          const { count, error: countError } = await supabase
            .from('sources')
            .select('*', { count: 'exact', head: true })
            .eq('notebook_id', notebook.id);
          if (countError) {
            console.error('Error fetching source count for notebook:', notebook.id, countError);
            return { ...notebook, sources: [{ count: 0 }] };
          }
          return { ...notebook, sources: [{ count: count || 0 }] };
        })
      );
      console.log('Fetched notebooks:', notebooksWithCounts.length);
      return notebooksWithCounts;
    },
    enabled: computed(() => isAuthenticated.value && !authLoading.value),
    retry: (failureCount, error) => {
      if (error?.message?.includes('JWT') || error?.message?.includes('auth')) return false;
      return failureCount < 3;
    },
  });

  watchEffect(onInvalidate => {
    if (!user.value?.id || !isAuthenticated.value) return;
    console.log('Setting up real-time subscription for notebooks');
    const channel = supabase
      .channel('notebooks-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'notebooks', filter: `user_id=eq.${user.value.id}` },
        () => queryClient.invalidateQueries(['notebooks', user.value.id])
      )
      .subscribe();
    onInvalidate(() => supabase.removeChannel(channel));
  });

  const createMutation = useMutation({
    mutationFn: async notebookData => {
      if (!user.value) throw new Error('User not authenticated');
      console.log('Creating notebook with data:', notebookData);
      const { data, error } = await supabase
        .from('notebooks')
        .insert({
          title: notebookData.title,
          description: notebookData.description,
          user_id: user.value.id,
          generation_status: 'pending',
        })
        .select()
        .single();
      if (error) throw error;
      console.log('Notebook created successfully:', data);
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['notebooks', user.value?.id]),
    onError: error => console.error('Mutation error:', error),
  });

  return {
    notebooks: computed(() => notebooksQuery.data.value || []),
    isLoading: computed(() => authLoading.value || notebooksQuery.isLoading.value),
    error: computed(() => notebooksQuery.error.value?.message || null),
    isError: computed(() => notebooksQuery.isError.value),
    createNotebook: createMutation.mutate,
    isCreating: computed(() => createMutation.isLoading.value),
  };
}