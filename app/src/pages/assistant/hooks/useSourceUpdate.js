import { computed } from 'vue';
import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';
import { useAuth } from '@/pages/assistant/hooks/useAuth.js';

export function useSourceUpdate() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const mutation = useMutation({
    mutationFn: async ({ sourceId, title }) => {
      console.log('Updating source:', sourceId, 'with title:', title);
      const { data, error } = await supabase
        .from('sources')
        .update({ title })
        .eq('id', sourceId)
        .select()
        .single();
      if (error) {
        console.error('Error updating source:', error);
        throw error;
      }
      console.log('Source updated successfully');
      return data;
    },
    onSuccess: () => {
      console.log('Update mutation success, invalidating queries');
      queryClient.invalidateQueries(['sources']);
    },
    onError: (error) => {
      console.error('Update mutation error:', error);
    },
  });

  return {
    updateSource: mutation.mutate,
    isUpdating: computed(() => mutation.isLoading.value),
  };
}
