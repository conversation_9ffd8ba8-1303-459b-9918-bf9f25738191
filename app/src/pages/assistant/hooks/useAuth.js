import { ref, computed } from 'vue';
import { supabase } from '@/pages/assistant/supabase/supabaseClient';

// Reactive auth state
const user = ref(null);
const session = ref(null);
const loading = ref(true);
const error = ref(null);

const isAuthenticated = computed(() => !!user.value);

let initialized = false;
let subscription = null;

function clearAuthState() {
  user.value = null;
  session.value = null;
  error.value = null;
}

async function initAuth() {
  loading.value = true;

  try {
    // Get current session
    const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.warn('useAuth: session error', sessionError);
    }

    if (!currentSession) {
      // Auto-login as superadmin when no active session
      try {
        const { data, error: loginError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'asdfasdf',
        });
        if (loginError) throw loginError;

        // Cache token explicitly for any background usage
        localStorage.setItem('authToken', data.session?.access_token ?? '');

        session.value = data.session;
        user.value = data.user;
        console.log('useAuth: Logged in as superadmin', data.user?.email);
      } catch (err) {
        console.error('useAuth: auto-login failed', err);
        error.value = err instanceof Error ? err.message : String(err);
      }
    } else {
      session.value = currentSession;
      user.value = currentSession.user;
    }
  } catch (err) {
    console.error('useAuth: initialization error', err);
    error.value = err instanceof Error ? err.message : String(err);
  } finally {
    loading.value = false;
  }

  // Subscribe to auth state changes
  const { data, error: listenerError } = supabase.auth.onAuthStateChange(
    (event, newSession) => {
      console.log('useAuth: Auth state changed', event, newSession?.user?.email || 'No session');

      if (event === 'SIGNED_OUT') {
        clearAuthState();
        loading.value = false;
        return;
      }

      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        session.value = newSession;
        user.value = newSession?.user ?? null;
        error.value = null;
        loading.value = false;
        return;
      }

      // For other events, update state if token changed
      if (session.value?.access_token !== newSession?.access_token) {
        session.value = newSession;
        user.value = newSession?.user ?? null;
        if (loading.value) loading.value = false;
      }
    }
  );

  if (listenerError) {
    console.error('useAuth: subscription error', listenerError);
  } else {
    subscription = data.subscription;
  }
}



/**
 * Sign out the current user and clear local auth state.
 */
export async function signOut() {
  console.log('useAuth: Starting logout process...');
  // Clear local state immediately for responsive UI
  clearAuthState();
  try {
    const { error: signOutError } = await supabase.auth.signOut();
    if (signOutError) {
      console.log('useAuth: logout error', signOutError);

      // If session already invalid on server just return
      if (
        signOutError.message?.includes('session_not_found') ||
        signOutError.message?.includes('Session not found') ||
        signOutError.status === 403
      ) {
        console.log('useAuth: Session already invalid on server');
        return;
      }

      // For other errors, still ensure local session is cleared
      await supabase.auth.signOut({ scope: 'local' });
      return;
    }

    console.log('useAuth: logout successful');
  } catch (err) {
    console.error('useAuth: unexpected logout error', err);

    // Even if there's an error, try to clear local session
    try {
      await supabase.auth.signOut({ scope: 'local' });
    } catch (localError) {
      console.error('useAuth: failed to clear local session', localError);
    }
  }
}

/**
 * Vue composable for authentication state.
 */
export function useAuth() {
  if (!initialized) {
    initAuth();
    initialized = true;
  }
  return {
    user,
    session,
    loading,
    error,
    isAuthenticated,
    signOut,
  };
}
