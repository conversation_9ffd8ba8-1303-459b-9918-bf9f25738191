<template>
  <div class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Dashboard View -->
      <div v-if="currentView === 'dashboard' && !isClient" class="w-full">
        <DashboardView
          :notebooks="paginatedNotebooks"
          :isLoading="isLoading"
          :isCreating="isCreating"
          :currentPage="currentPage"
          :totalPages="totalPages"
          :totalNotebooks="notebooks?.length || 0"
          :perPage="pageSize"
          @set-page="setPage"
          @per-page-changed="setPerPage"
          @create-notebook="handleCreateNotebook"
          @open-notebook="handleOpenNotebook"
        />
      </div>
      <!-- Notebook View -->
      <div v-else-if="currentView === 'notebook' || isClient" class="w-full flex">
        <NotebookView 
          :notebookId="currentNotebookId"
          :notebook="currentNotebook"
          @back-to-dashboard="handleBackToDashboard"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue';
import { mapGetters } from 'vuex';
import DashboardView from './components/DashboardView.vue';
import NotebookView from './components/NotebookView.vue';
import { useNotebooks } from './hooks/useNotebook.js';

export default {
  name: 'AnswerFlow',
  components: {
    DashboardView,
    NotebookView,
  },
  setup() {
    const { notebooks, isLoading, createNotebook, isCreating } = useNotebooks();

    // Pagination state
    const currentPage = ref(1);
    const pageSize = ref(12); // 8 per page (adjust as needed)

    const totalPages = computed(() => {
      return Math.max(1, Math.ceil((notebooks.value?.length || 0) / pageSize.value));
    });

    const paginatedNotebooks = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      return notebooks.value?.slice(start, start + pageSize.value) || [];
    });

    function setPage(page) {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    }

    function setPerPage(newPerPage) {
      pageSize.value = newPerPage;
      currentPage.value = 1; // Reset to first page when changing page size
    }

    return {
      notebooks, // original, for currentNotebook lookup
      paginatedNotebooks,
      isLoading,
      createNotebook,
      isCreating,
      currentPage,
      totalPages,
      pageSize,
      setPage,
      setPerPage,
    };
  },
  data() {
    return {
      currentView: 'dashboard', // 'dashboard' or 'notebook'
      currentNotebookId: null,
      error: null,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/user',
      isClient: 'auth/isClient',
    }),
    currentNotebook() {
      return this.notebooks.find(n => n.id === this.currentNotebookId);
    }
  },
  created() {
    // Check authentication
    if (!this.user) {
      this.$router.push('/login');
      return;
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', this.handleKeyboardShortcuts);
  },

  beforeUnmount() {
    document.removeEventListener('keydown', this.handleKeyboardShortcuts);
  },
  methods: {
    handleCreateNotebook() {
      this.createNotebook({
        title: 'Untitled notebook',
        description: ''
      }, {
        onSuccess: (data) => {
          // After creation, navigate directly to the new notebook route
          this.$router.push(`/assistant/notebook/${data.id}`);
          this.__showNotif('success', 'Success', 'Notebook created successfully');
        },
        onError: (error) => {
          console.error('Failed to create notebook:', error);
          this.__showNotif('error', 'Error', 'Failed to create notebook');
        }
      });
    },
    
    handleOpenNotebook(notebookId) {
      // Navigate to the notebook route so URL reflects the opened notebook
      this.$router.push(`/assistant/notebook/${notebookId}`);
    },
    
    handleBackToDashboard() {
      this.currentView = 'dashboard';
      this.currentNotebookId = null;
      // Notebooks will be automatically updated via the hook's real-time subscription
    },

    handleKeyboardShortcuts(event) {
      // Ctrl/Cmd + N: Create new notebook
      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        if (this.currentView === 'dashboard') {
          this.handleCreateNotebook();
        }
      }

      // Escape: Go back to dashboard
      if (event.key === 'Escape' && this.currentView === 'notebook') {
        this.handleBackToDashboard();
      }
    }
  }
};
</script>
