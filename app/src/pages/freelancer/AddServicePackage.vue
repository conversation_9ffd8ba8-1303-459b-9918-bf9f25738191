<template>
  <loader-circle v-if="isFetching" />
    <div v-if="!isFetching && items.length">
        <div class="flex justify-between m-2">
            <div class="mt-2 ml-4">
                <label for="table-header" class="font-medium text-gray-800">Service Item</label>
            </div>
            <div class="flex">
                <div class="relative mr-2">
                    <input type="text" v-model="keyword" v-value="keyword" @input="onInputSearch"
                        class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                        :placeholder="$t('Search')" />
                    <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
                        <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        <!-- Table Section -->
        <div
            class="m-2 overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
            <div class="min-w-full inline-block align-middle">
                <!-- Table -->
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th scope="col" class="w-[10px]">
                                <div class="text-gray-800"></div>
                            </th>
                            <th scope="col" class="min-w-[150px]">
                                <div
                                    class="pe-4 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            Item Name
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" /></svg>
                                        </button>
                                        
                                        <!-- Dropdown -->
                                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                                            role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                                            <div class="p-1">
                                                <button type="button" @click="sortAsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="m5 12 7-7 7 7" />
                                                        <path d="M12 19V5" /></svg>
                                                    Sort ascending
                                                </button>
                                                <button type="button" @click="sortDsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="M12 5v14" />
                                                        <path d="m19 12-7 7-7-7" /></svg>
                                                    Sort descending
                                                </button>
                                            </div>
                                        </div>
                                        <!-- End Dropdown -->
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col" class="min-w-10">
                                <div
                                    class="text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="px-4 py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            {{isAdmin ? 'Service Fee' : 'Description'}}
                                        </button>
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                </div>
                            </th>
                        </tr>
                    </thead>

                    <tbody class="divide-y divide-gray-200">
                        <tr v-for="(item, index) in items" :key="item.id" @mouseover="onHover(item)"
                            :class="{'bg-slate-100': item.id === curerentActive}">
                            <td class="whitespace-nowrap py-3">
                                <div class=" w-[50px] flex items-center">
                                    <div class="grow">
                                        <span class="text-sm font-medium ml-4 text-gray-800">
                                            {{ index + 1 + (page - 1) * limit }}
                                        </span>
                                    </div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap pe-4 py-3">
                                <div class="w-[350px] flex items-center gap-x-3">
                                    <div class="grow">
                                        <span class="text-sm font-normal text-gray-800">
                                            {{ item.name }}
                                        </span>
                                    </div>
                                    <div :class="{'hidden': item.id !== curerentActive}">
                                        <button @click="addUserService(item)" 
                                            type="button" class="px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                        Select
                                        </button>
                                    </div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3 ">
                                <div class="text-sm text-gray-600 truncate max-w-[800px]">
                                    {{ item.description ?? "-" }}
                                </div>
                                
                            </td>
                            <td class="whitespace-nowrap ">
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- End Table -->
            </div>
        </div>
        <!-- End Table Section -->
        <!-- Footer -->
        <div class="mt-5 flex flex-wrap justify-between items-center gap-2">
            <p class="text-sm ml-4">
                <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
                <span class="font-medium text-stone-800">Results</span>
            </p>
            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" v-if="page > 1" @click="prevPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Previous">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1 mr-2 ml-2">
                    <span
                        class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                        aria-current="page">{{ page }}</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
                </div>
                <button type="button" v-if="page < maxPage" @click="nextPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Next">
                    <span class="sr-only">Next</span>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            </nav>
            <!-- End Pagination -->
        </div>
        <!-- End Footer -->

    </div>
    <div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-10">
    {{ $t('No records found') }}
  </div>
</template>

<script>
    import {
		mapGetters
	} from 'vuex';
    import {
        AdjustmentsIcon,
        UserCircleIcon
    } from "@heroicons/vue/solid";
    import TInput from "@/components/form/Input.vue";

    // import TRadio from '@/components/form/RadioButton.vue'
    import {
        PlusIcon
    } from "@heroicons/vue/solid";
    import expertiseApi from "@/api/expertise";
    import serviceApi from "@/api/service";
    import userApi from "@/api/user";

    import Confirmation from "@/components/modal/Confirmation.vue";
    import {
        HSOverlay,
        HSDropdown,
        HSSelect
    } from "preline/preline";
    import {
        HSStaticMethods
    } from "preline";
    export default {
        name: "addServicePackage",
        watch: {
            isHourly() {
                if (this.isHourly) this.unitPrice = 0
                if (!this.isHourly) this.costPrice = 0
            }
        },
        components: {
            AdjustmentsIcon,
            UserCircleIcon,
            PlusIcon,
            TInput,
            Confirmation,
        },
        computed: {
            ...mapGetters({
                user: 'auth/user',
                isAdmin: 'auth/isAdmin',
            }),
            selectedUserList() {
                return this.selecteduser
            }
        },
        data() {
            return {
                orderBy: "name",
                sortBy: "asc",
                page: 1,
                total: 0,
                maxPage: 1,
                limit: 10,
                name: "",
                taskName: "",
                description: "",
                isHourly: true,
                isUnit: false,
                isAutoAssign: false,
                isAutoDueDate: false,
                isRevision: false,
                costPrice: "",
                unitPrice: "",
                sellingPrice: "",
                selectedExpertiseLevel: null,
                selectedExpertise: "",
                selectedColumn: "",
                curerentActive: -1,
                deliveryTime: 1,
                deliveryType: 'hour',
                expertises: [],
                selectedItem: null,
                itemId: null,
                items: [],
                meta: null,
                isEdit: false,
                keyword: "",
                selectedUser: [],
                userIds: [],
                users: [],
                selectedUserKey: 0,
                isFetching: false,
            };
        },
        created() {
            this.getAll();
            this.getAllExpertise();
            this.getAllUser();
        },
        mounted() {
        },
        methods: {
            addUserService(item) {
                const id = this.$route.params.id
                const callback = (response) => {
                    const data = response.data;
                    this.$router.push('/settings/service')
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }

                const params = {
                    userId: id,
                    serviceItemId: item.id,
                }
                
                userApi.addUserService(params, callback, errCallback)
            },
            sortAsc() {
                this.sortBy = "asc"
                this.getAll()
            },
            sortDsc() {
                this.sortBy = "desc"
                this.getAll()
            },
            onHover(item) {
                this.curerentActive = item.id;
            },
            onHoverOut(item) {
                this.curerentActive = -1;
            },
            hsSelectOption(user) {
                return {
                    'data-hs-select-option': JSON.stringify({
                        description: `${user.first_name}`,
                        icon: `<img class="inline-block rounded-full" src="${this.getUserImage(user)}" />`
                    })
                }
            },
            openDropdown(item) {
                console.log("Asdasdasd")
                const el = HSDropdown.getInstance('.hs-dropdown-example');
                console.log(el)
                this.selectedItem = item
            },
            openDeleteModal(item) {
                HSOverlay.open('#confirm');
                this.selectedItem = item
            },
            onChangeRevision(e) {
                this.isRevision = !this.isRevision;
            },
            onChangeAutoAssign(e) {
                this.isAutoAssign = !this.isAutoAssign
            },
            onChangeAutoDueDate(e) {
                this.isAutoDueDate = !this.isAutoDueDate
            },
            customLabel({
                username
            }) {
                return `${username}`;
            },
            getUserImage(user) {
                if (user.imgUrl) {
                    return user.imgUrl;
                } else {
                    return this.generatePlaceholder(user.username);
                }
            },
            generatePlaceholder(firstName) {
                const init = firstName ? `${firstName.charAt(0)}` : "PA";
                const initials = init;

                // Set canvas size and DPI
                const canvas = document.createElement("canvas");
                const size = 100; // Increased size for better clarity
                const dpi = window.devicePixelRatio || 1; // Use device pixel ratio for sharper images
                canvas.width = size * dpi;
                canvas.height = size * dpi;
                canvas.style.width = `${size}px`;
                canvas.style.height = `${size}px`;

                const ctx = canvas.getContext("2d");

                // Scale the context for higher DPI
                ctx.scale(dpi, dpi);

                // Background
                ctx.fillStyle = "#000000";
                ctx.fillRect(0, 0, size, size);

                // Text
                ctx.fillStyle = "#FFFFFF";
                ctx.font = "bold 50px Arial"; // Increased font size for better visibility
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(initials, size / 2, size / 2);

                return canvas.toDataURL();
            },
            getAllUser() {
                const callback = (response) => {
                    const data = response.data;
                    this.users = data;
                };
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                };

                const params = {
                    orderBy: "first_name",
                    sortBy: "asc",
                    page: 1,
                    limit: 9999,
                };
                userApi.getList(params, callback, errCallback);
            },
            upDeliveryTime(e) {
                this.deliveryTime = this.deliveryTime + 1;
            },
            downDeliveryTime(e) {
                this.deliveryTime = this.deliveryTime - 1;
            },
            getAllExpertise() {
                const callback = (response) => {
                    const data = response.data;
                    this.expertises = data;
                };
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                };

                const params = {
                    orderBy: this.orderBy,
                    sortBy: this.sortBy,
                    page: 1,
                    limit: 999,
                };
                expertiseApi.getList(params, callback, errCallback);
            },
            onHourlyToggle() {
                this.isHourly = true;
            },
            onUnitoggle() {
                this.isHourly = false;
            },
            nextPage() {
                this.page = this.page + 1;
                this.getAll();

            },
            prevPage() {
                this.page = this.page - 1;
                this.getAll();

            },
            debounce(func, wait) {
                let timeout;
                return function (...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(this, args);
                    }, wait);
                };
            },
            onInputSearch() {
                this.debounce(this.getAll(this.keyword), 300); // 300ms debounce
            },
            confirmDelete() {
                if (this.selectedItem) {
                    const callback = (response) => {
                        const data = response.data;
                        const message = response.message;
                        const isDelete = true;
                        this.getAll();
                        this.__showNotif("success", "Success", message);
                    };
                    const errCallback = (err) => {
                        const message = err?.response?.data?.message;
                        this.__showNotif('error', 'Error', message);
                    };
                    const id = this.selectedItem.id;
                    serviceApi.delete(id, callback, errCallback);
                }
            },
            save() {
                if (!this.isEdit) {
                    this.addNew();
                } else {
                    this.updateItem();
                }
            },
            getAll(keyword = null) {
                this.isFetching = true;
                const callback = (response) => {
                    const data = response.data;
                    const meta = response.meta;
                    this.items = data;
                    this.meta = meta;
                    this.page = meta.currentPage;
                    this.maxPage = meta.lastPage;
                    this.total = meta.total;
                    setTimeout(() => {
                        HSStaticMethods.autoInit();
                    }, 500);
                    if (keyword ) {
                        this.page = 1;
                    }
                    this.isFetching = false;
                };
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isFetching = false;
                };

                const params = {
                    orderBy: this.orderBy,
                    sortBy: this.sortBy,
                    page: this.page,
                    limit: this.limit,
                };
                if (keyword) params.keyword = keyword;
                serviceApi.getList(params, callback, errCallback);
            },
        },
    };
</script>