<template>
    <div class="max-w-lg mx-auto p-6 bg-white rounded-lg">
        <!-- Profile Photo and Upload Button -->
        <div class="flex items-center space-x-4 mb-6">
            <img :src="getUserImage(form)" alt="User profile photo" class="w-16 h-16 rounded-full object-cover" />
            <div>
                <input type="file" ref="fileInput" accept="image/*" @change="handlePhotoUpload" class="hidden" />
                <t-button  @click="triggerFileUpload" :isDisabled="isUploadingFile" :color="`primary-white`" class="p-2" :isLoading="isUploadingFile">
                    
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 mr-2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                    </svg>

                    {{ $t('Upload new Photo') }}
                </t-button>
                <p class="text-sm text-gray-500">{{ $t('Photos help your teammates recognize you in Planlagt') }}</p>
            </div>
        </div>

        <!-- Form Fields -->
        <form @submit.prevent="saveChanges">
            <div class="mb-4">
                <label for="full-name" class="block text-sm font-medium text-gray-700 mb-1">Full name</label>
                <input id="full-name" v-model="form.first_name" type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Type in your full name" required />
            </div>

            <div class="mb-4">
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input id="email" v-model="form.email" type="email"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    disabled />
            </div>

            <div class="mb-4">
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone number</label>
                <input id="phone" v-model="form.phone" type="tel"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Your phone number" />
            </div>

            <div class="mb-4">
                <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <input id="location" v-model="form.location" type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Where are you located" />
            </div>

            <div class="mb-4">
                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                <input id="company" v-model="form.company" type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Type in your company name" />
            </div>

            <div class="mb-4" v-if="isClient">
                <label for="job-title" class="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
                <input id="clientJobTitle" v-model="form.clientJobTitle" type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Type in your job title name" />
            </div>

            <div class="mb-4">
                <label for="shortBio" class="block text-sm font-medium text-gray-700 mb-1">Short Bio / Description</label>
                <textarea id="shortBio" v-model="form.shortBio"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Type in short description about you"></textarea>
            </div>

            <div class="flex items-center mb-6" v-if="isClient">
                <input id="available" type="checkbox" v-model="form.isAvailable" :checked="form.isAvailable" 
                    class="mr-2 h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                <label for="available" class="block text-sm font-medium text-gray-700">I am available for assignment</label>
            </div>

            <!-- Form Buttons -->
            <div class="flex justify-between">
                <button type="button" @click="cancelChanges"
                    class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300">
                    Cancel
                </button>
                <button type="submit"
                    class="inline-flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import fileApi from '@/api/files'; // Import your file API module
import userApi from '@/api/user'; // Import your user API module
import jobApi from '@/api/jobs'; // Import your user API module
import logoImage from '@/assets/images/Avatar-User-Default.png';
import { timestamp } from 'velocity-animate';

export default {
    data() {
        return {
            form: {
                first_name: '',
                email: '',
                phone: '',
                location: '',
                company: '',
                jobTitle: '',
                shortBio: '',
                imgUrl: '',
                username: '',
                isAvailable: 0,
            }, 
            isUploadingFile: false,
            jobs: []
        };
    },
    computed: {
        ...mapGetters({
            user: 'auth/user' // Map user getter from Vuex store
        })
    },
    created() {
        this.getAllJobs()
    },
    watch: {
        // Whenever the user object changes, update the form data
        user: {
            immediate: true,
            handler(newUser) {
                if (newUser) {
                    this.form.first_name = newUser.first_name || '';
                    this.form.email = newUser.email || '';
                    this.form.phone = newUser.phone || '';
                    this.form.location = newUser.location || '';
                    this.form.company = newUser.company || '';
                    this.form.jobTitle = newUser.jobTitle || '';
                    this.form.shortBio = newUser.shortBio || '';
                    this.form.isAvailable = !!newUser.isAvailable || 0;
                    this.form.username = newUser.username || this.user.username;
                    this.form.imgUrl = newUser.imgUrl;
                }
            }
        }
    },
    methods: {
        ...mapActions({
            'setUser': 'auth/setUser',
            isClient: 'auth/isClient',
        }),
        getAllJobs() {
            const callback = (response) => {
                const data = response.data;
                this.jobs = data;
            }

            const errCallback = (err) => {
                console.log(err)
            }
            const params = {
                limit: 999,
                orederBy: 'name',
                sortby: 'asc'
            }
            jobApi.getList(params, callback, errCallback)
        },
        triggerFileUpload() {
            this.$refs.fileInput.click();
        },
        handlePhotoUpload(event) {
            const file = event.target.files[0];
            if (file && ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
                this.compressAndUploadImage(file);
            } else {
                this.__showNotif('error', 'Error', 'Unsupported file type. Please upload a JPEG, PNG, or GIF.');
            }
        },
        compressAndUploadImage(file) {
        const reader = new FileReader();

        reader.onload = (e) => {
          const img = new Image();
          img.src = e.target.result;

          img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set desired maximum width and height
            const maxWidth = 100;
            const maxHeight = 100;
            let width = img.width;
            let height = img.height;

            // Calculate aspect ratio
            if (width > height) {
              if (width > maxWidth) {
                height *= (maxWidth / width);
                width = maxWidth;
              }
            } else {
              if (height > maxHeight) {
                width *= (maxHeight / height);
                height = maxHeight;
              }
            }

            // Set canvas size to the new dimensions
            canvas.width = width;
            canvas.height = height;

            // Draw the image onto the canvas with the new size
            ctx.drawImage(img, 0, 0, width, height);

            // Convert the canvas to a Blob or base64 data URL
            canvas.toBlob((blob) => {
              const compressedFile = new File([blob], file.name, { type: file.type });
              this.uploadFile(compressedFile);
            }, file.type, 0.9); // Adjust compression level (0-1)
          };
        };

        reader.readAsDataURL(file); // Read the file as a data URL for the Image object
      },

        uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            this.isUploadingFile = true;
            fileApi.upload(formData, this.onUploadSuccess, this.onUploadError);
        },
        onUploadSuccess(response) {
            this.isUploadingFile = false;
            this.form.imgUrl = response.data; // Update form.imgUrl with the new photo
            this.__showNotif('success', 'Success', 'Photo uploaded successfully!');
        },

        onUploadError() {
            this.isUploadingFile = false;
            this.__showNotif('error', 'Error', 'There was an error uploading the photo.');
        },
        saveChanges() {
            userApi.update(this.user.id, this.form, this.onSaveSuccess, this.onSaveError);
        },
        onSaveSuccess(response) {
            const data = response.data;
            const message = response.message;
            this.setUser(data)
            this.__showNotif('success', 'Success', message);

        },
        onSaveError(error) {
            console.log(error)
            this.__showNotif('error', 'Error', 'There was an error saving your profile.');
        },
        cancelChanges() {
            // Reset the form to the user's initial data
            this.$store.dispatch('auth/fetchUser'); // Assuming fetchUser refetches the user data
        },
        getUserImage(user) {
            if (user?.imgUrl) {
                return user.imgUrl;
            } else {
                const defaultName = user && user.first_name ? user?.first_name : "Planlagt"
                return this.__generateInitialCanvas(defaultName);
            }
        },
    }
};
</script>

<style scoped>
/* Customize styling if necessary */
</style>
