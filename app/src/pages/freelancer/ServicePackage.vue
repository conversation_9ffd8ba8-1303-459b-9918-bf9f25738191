<template>
    <div class="mx-auto p-6 bg-white rounded-lg">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-xl font-semibold text-gray-800">My Service</h2>
                <p class="text-sm text-gray-500">
                    Manage service(s) that you offer, select from the list of services that suit you.
                    Drag to reorder; the top is your most preferred service.
                </p>
            </div>
            <button @click="addService"
                class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Service
            </button>
        </div>

        <!-- Expertise List with Drag and Drop -->
        <draggable v-if="services?.length" v-model="services" @end="updateServiceOrder" class="space-y-4 max-w-4xl">
            <template #item="{ element, index }">
                <div class="flex justify-between items-center bg-gray-100 px-4 py-3 rounded-lg shadow-sm">
                    <!-- Drag Handle -->
                    <div class="flex items-center space-x-2 cursor-move">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 18M6 12L18 12M6 6L18 6" />
                        </svg>
                        
                        <p class="text-gray-800 font-semibold">{{ element.serviceItem.name }} </p>
                    </div>

                    <!-- Delete Button -->
                    <button @click="removeService(index)" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6L18 18" />
                        </svg>
                    </button>
                </div>
            </template>
        </draggable>

        <!-- Empty State Message -->
        <div v-else class="flex items-center text-gray-600">
            <InformationCircleIcon class="w-8 mr-2"/>  <span class="text-gray-400">Start adding your service to receive assignments. </span>
        </div>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import draggable from 'vuedraggable';
import serviceApi from '@/api/service';
import userApi from '@/api/user';
import { InformationCircleIcon } from '@heroicons/vue/outline';

export default {
    components: {
        draggable,
        InformationCircleIcon
    },
    data() {
        return {
            services: [],
            orderBy: 'name',
            sortBy: 'asc',
            page: 1,
            total: 0,
            maxPage: 1,
            limit: 100,
            name: "",
            items: []
        };
    },
    computed: {
        ...mapGetters({
            user: 'auth/user'
        })
    },
    created() {
        this.getAllService();
    },
    methods: {
        ...mapActions({
            fetchUser: 'auth/fetchUser',
            setUser: 'auth/setUser'
        }),

        getAllService() {
            const callback = (response) => {
                const data = response.data;
                this.fetchUser();
                const meta = response.meta;
                this.items = data;
                this.meta = meta;
                this.page = meta.currentPage;
                this.maxPage = meta.lastPage;
                this.total = meta.total;

                setTimeout(() => {
                    this.services = this.user.serviceUsers;
                }, 300);
            };

            const errCallback = (err) => {
                console.log(err);
            };

            const params = {
                orderBy: this.orderBy,
                sortBy: this.sortBy,
                page: this.page,
                limit: this.limit,
            };

            serviceApi.getList(params, callback, errCallback);
        },
        addService() {
            this.$router.push({ name: 'AddServicePackage', params: { id: this.user.id } });
        },
        removeService(index) {
            const callback = () => {
                this.services.splice(index, 1);
            };

            const errCallback = (err) => {
                console.log(err);
            };

            const id = this.services[index].id;
            userApi.removeUserService(id, callback, errCallback);
        },
        updateServiceOrder() {
            // Logic to update the service order after dragging.
            const reorderedServices = this.services.map(service => service.id);
            
            const params = {
                ids: reorderedServices
            }
            const callback = (response) => {
                const data = response.data;
                this.setUser(data);
                this.__showNotif("success", "Success", 'Service order updated successfully.');
            } 
            const errorCallback = (err) => {
                console.log('Error updating service order:', err);
            } 
            
            // Send reorderedServices to API to persist the order in the backend.
            userApi.updateUserServiceOrder(params, callback, errorCallback)
                
        }
    }
};
</script>

<style scoped>
/* Customize styling if necessary */
</style>
