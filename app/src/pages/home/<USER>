<template>
	<!-- Loader -->
	<loader-circle v-if="isFetchingProject" />
	<Home></Home>
</template>

<script>
import {
	BriefcaseIcon,
	DownloadIcon,
	UserGroupIcon,
	XIcon,
} from '@heroicons/vue/solid';

import { mapGetters, mapActions } from 'vuex';
import TModal from '@/components/global/Modal.vue';
import Home from '@/components/home/<USER>';

export default {
	components: {
		BriefcaseIcon,
		DownloadIcon,
		UserGroupIcon,
		XIcon,
		TModal,
		Home,
	},
	data() {
		return {
		};
	},
	computed: {
		...mapGetters({
			getToken: 'auth/getToken',
			user: 'auth/user',
			isFetchingUser: 'auth/isFetchingUser',
			isAdmin: 'auth/isAdmin',
		}),
		isPackageMax() {
			return this.user &&  this.user.userCredit &&  this.user.userCredit.credit ? this.user.userCredit.credit : 'Unlimited';
		}
	},
	created() {
		if (this.getToken && !this.user && !this.isFetchingUser) {
			this.fetchUser();
		}
	},
	mounted() {
	},
	methods: {
		...mapActions({
			fetchUser: 'auth/fetchUser',
		}),
	},
};
</script>