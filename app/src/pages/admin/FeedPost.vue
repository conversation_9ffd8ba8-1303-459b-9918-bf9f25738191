<template>
  <div class="h-full flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between p-4">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">Feed Post Management</h1>
      </div>
    </div>

    <!-- Tab Content -->
    <FeedPostTab ref="feedPostTab" />

    <!-- Modals -->
    <FeedPostModals
      :showFeedPostModal="feedPostModal.show"
      :modalMode="currentModal.mode"
      :currentData="currentModal.data"
      @close-feedpost-modal="closeFeedPostModal"
      @submit-feedpost="submitFeedPost"
    />
  </div>
</template>

<script>
import FeedPostTab from './tabs/FeedPostTab.vue';
import FeedPostModals from '@/components/admin/modals/FeedPostModals.vue';

export default {
  name: 'FeedPostManagement',
  components: {
    FeedPostTab,
    FeedPostModals,
  },
  data() {
    return {
      activeTab: this.$route.query.tab || 'feedposts',
      tabs: [
        { id: 'feedposts', name: 'Feed Posts' }
      ],

      // Modal states
      feedPostModal: { show: false },

      currentModal: {
        mode: 'add', // 'add' or 'edit'
        data: null
      }
    };
  },
  watch: {
    activeTab(newTab) {
      // Update URL query parameter when tab changes
      this.$router.replace({ 
        query: { ...this.$route.query, tab: newTab } 
      });
    }
  },
  methods: {
    setActiveTab(tabId) {
      this.activeTab = tabId;
    },

    // Feed Post modal methods
    showFeedPostModal(mode = 'add', data = null) {
      this.currentModal.mode = mode;
      this.currentModal.data = data;
      this.feedPostModal.show = true;
    },

    closeFeedPostModal() {
      this.feedPostModal.show = false;
      this.currentModal.data = null;
    },

    submitFeedPost(feedPostData) {
      // Forward to FeedPostTab for API handling
      this.$refs.feedPostTab?.submitFeedPost(feedPostData);
      this.closeFeedPostModal();
    }
  },

  // Provide methods to child components
  provide() {
    return {
      showFeedPostModal: this.showFeedPostModal
    };
  }
};
</script>
