<template>
  <div class="h-full flex flex-col">
    <!-- Tab Navigation -->
    <div class="px-6 border-b border-gray-200 dark:border-gray-700">
      <nav class="flex space-x-8" aria-label="Tabs">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="changeTab(tab.id)"
          :class="[
            activeTab === tab.id
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
          ]"
        >
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="flex-1 overflow-hidden">
      <!-- Users Tab -->
      <div v-if="activeTab === 'users'" class="h-full">
        <UserTab ref="userTab" />
      </div>

      <!-- Roles Tab -->
      <div v-if="activeTab === 'roles'" class="h-full">
        <RoleTab ref="roleTab" />
      </div>

      <!-- Groups Tab -->
      <div v-if="activeTab === 'groups'" class="h-full">
        <GroupTab ref="groupTab" />
      </div>

      <!-- Category Tab -->
      <div v-if="activeTab === 'category'" class="h-full">
        <CategoryTab ref="categoryTab" />
      </div>
    </div>

    <!-- Modals -->
    <UserModals
      ref="userModals"
      :showUserModal="userModal.show"
      :showRoleModal="roleModal.show"
      :showGroupModal="groupModal.show"
      :showCategoryModal="categoryModal.show"
      :modalMode="currentModal.mode"
      :currentData="currentModal.data"
      @close-user-modal="closeUserModal"
      @close-role-modal="closeRoleModal"
      @close-group-modal="closeGroupModal"
      @close-category-modal="closeCategoryModal"
      @submit-user="submitUser"
      @submit-role="submitRole"
      @submit-group="submitGroup"
      @submit-category="submitCategory"
    />
  </div>
</template>

<script>
import UserTab from './tabs/UserTab.vue';
import RoleTab from './tabs/RoleTab.vue';
import GroupTab from './tabs/GroupTab.vue';
import CategoryTab from './tabs/CategoryTab.vue';
import UserModals from '@/components/admin/modals/UserModals.vue';

export default {
  name: 'UserManagement',
  components: {
    UserTab,
    RoleTab,
    GroupTab,
    CategoryTab,
    UserModals,
  },
  data() {
    return {
      activeTab: this.$route.query.tab || 'users',
      tabs: [
        { id: 'users', name: this.$t('Users') },
        { id: 'roles', name: this.$t('Roles') },
        { id: 'groups', name: this.$t('Groups') },
        { id: 'category', name: this.$t('Category') }
      ],

      // Modal states
      userModal: { show: false },
      roleModal: { show: false },
      groupModal: { show: false },
      categoryModal: { show: false },

      currentModal: {
        mode: 'add', // 'add' or 'edit'
        data: null
      }
    };
  },
  mounted() {
    // Ensure the tab from query params is valid
    const queryTab = this.$route.query.tab;
    if (queryTab && this.tabs.find(tab => tab.id === queryTab)) {
      this.activeTab = queryTab;
    } else if (!queryTab) {
      // Set default tab in URL if none specified
      this.$router.replace({
        query: { ...this.$route.query, tab: 'users' }
      });
    }
  },
  watch: {
    '$route.query.tab'(newTab) {
      if (newTab && this.tabs.find(tab => tab.id === newTab)) {
        this.activeTab = newTab;
      }
    }
  },
  methods: {
    changeTab(tabId) {
      this.activeTab = tabId;
      // Update route query params to persist tab state
      this.$router.push({
        query: { ...this.$route.query, tab: tabId }
      });
    },

    toggleFilter() {
      // Implement filter functionality
      console.log('Toggle filter');
    },

    // User modal methods
    showUserModal(mode = 'add', data = null) {
      this.currentModal.mode = mode;
      this.currentModal.data = data;
      this.userModal.show = true;
    },

    closeUserModal() {
      this.userModal.show = false;
      this.currentModal.data = null;
    },

    submitUser(userData) {
      // Notify modal that submission started
      this.$refs.userModals?.handleUserSubmitStart();

      // Forward to UserTab for API handling with callbacks
      this.$refs.userTab?.submitUser(userData, {
        onSuccess: () => {
          this.$refs.userModals?.handleUserSubmitSuccess();
          this.closeUserModal();
        },
        onError: (errorMessage) => {
          this.$refs.userModals?.handleUserSubmitError(errorMessage);
          // Don't close modal on error
        }
      });
    },

    // Role modal methods
    showRoleModal(mode = 'add', data = null) {
      this.currentModal.mode = mode;
      this.currentModal.data = data;
      this.roleModal.show = true;
    },

    closeRoleModal() {
      this.roleModal.show = false;
      this.currentModal.data = null;
    },

    submitRole(roleData) {
      // Notify modal that submission started
      this.$refs.userModals?.handleRoleSubmitStart();

      // Forward to RoleTab for API handling with callbacks
      this.$refs.roleTab?.submitRole(roleData, {
        onSuccess: () => {
          this.$refs.userModals?.handleRoleSubmitSuccess();
          this.closeRoleModal();
        },
        onError: (errorMessage) => {
          this.$refs.userModals?.handleRoleSubmitError(errorMessage);
          // Don't close modal on error
        }
      });
    },

    // Group modal methods
    showGroupModal(mode = 'add', data = null) {
      this.currentModal.mode = mode;
      this.currentModal.data = data;
      this.groupModal.show = true;
    },

    closeGroupModal() {
      this.groupModal.show = false;
      this.currentModal.data = null;
    },

    submitGroup(groupData) {
      // Notify modal that submission started
      this.$refs.userModals?.handleGroupSubmitStart();

      // Forward to GroupTab for API handling with callbacks
      this.$refs.groupTab?.submitGroup(groupData, {
        onSuccess: () => {
          this.$refs.userModals?.handleGroupSubmitSuccess();
          this.closeGroupModal();
        },
        onError: (errorMessage) => {
          this.$refs.userModals?.handleGroupSubmitError(errorMessage);
          // Don't close modal on error
        }
      });
    },

    // Category modal methods
    showCategoryModal(mode = 'add', data = null) {
      this.currentModal.mode = mode;
      this.currentModal.data = data;
      this.categoryModal.show = true;
    },

    closeCategoryModal() {
      this.categoryModal.show = false;
      this.currentModal.data = null;
    },

    submitCategory(categoryData) {
      // Notify modal that submission started
      this.$refs.userModals?.handleCategorySubmitStart();

      // Forward to CategoryTab for API handling with callbacks
      this.$refs.categoryTab?.submitCategory(categoryData, {
        onSuccess: () => {
          this.$refs.userModals?.handleCategorySubmitSuccess();
          this.closeCategoryModal();
        },
        onError: (errorMessage) => {
          this.$refs.userModals?.handleCategorySubmitError(errorMessage);
          // Don't close modal on error
        }
      });
    }
  },

  // Provide methods to child components
  provide() {
    return {
      showUserModal: this.showUserModal,
      showRoleModal: this.showRoleModal,
      showGroupModal: this.showGroupModal,
      showCategoryModal: this.showCategoryModal
    };
  }
};
</script>