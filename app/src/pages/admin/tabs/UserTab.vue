<template>
  <!-- Loader -->
  <loader-circle v-if="isFetching" />
  <!-- Users Table Card -->
  <div class="px-2 py-2 space-y-4 flex flex-col rounded-sm h-full">
    <!-- Filter Group -->
    <div class="flex justify-end items-center gap-x-2">
      <!-- button add new -->
      <div>
        <t-button :color="`primary-solid`" @click="addNew()">
          <PlusIcon class="h-5 w-5 text-white" />
          <span class="text-sm text-nowrap text-white">{{$t('Add User')}}</span>
        </t-button>
      </div>

      <div class="relative">
        <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
          <svg class="shrink-0 size-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.3-4.3" /></svg>
        </div>
        <form autocomplete="off">
          <TInput
            v-model="keyword"
            :value="keyword"
            type="text"
            name="search"
            :placeholder="$t('Search')"
            autocomplete="off"
            @input="onInputSearch"
            customStyle="padding-left: 2.5rem; padding-right: 2rem;"
          />
        </form>
      </div>

      <!-- Filter Button -->
      <div class="relative">
        <button
          @click="toggleFilter"
          class="inline-flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          :class="{ 'bg-primary-50 border-primary-300 text-primary-700': showFilter }"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
          </svg>
          Filter
        </button>

        <!-- Sort Filter Component -->
        <SortFilter
          :showFilter="showFilter"
          :orderBy="order_by"
          :sortBy="sort_by"
          :sortOptions="userSortOptions"
          @update:orderBy="order_by = $event"
          @update:sortBy="sort_by = $event"
          @update:showFilter="showFilter = $event"
          @filter-changed="onFilterChanged"
        />
      </div>
    </div>
    <!-- End Filter Group -->

    <div v-if="!isFetching && items.length" class="flex-1 overflow-hidden">
      <!-- Tab Content -->
      <div id="hs-pro-tabs-dut-all" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-all" class="h-full">
        <!-- Table Section -->
        <div class="overflow-auto h-full [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full">
              <thead class="sticky top-0 bg-white">
                <tr class="">
                  <th scope="col" class="w-[50px]">
                    <div class="pe-4 py-3 text-start">
                      <span class="text-sm font-semibold text-gray-800"></span>
                    </div>
                  </th>

                  <th scope="col" class="w-[25%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">User Name</span>
                    </div>
                  </th>

                  <th scope="col" class="w-[25%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">Role</span>
                    </div>
                  </th>

                  <th scope="col" class="w-[25%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">Group</span>
                    </div>
                  </th>

                  <th scope="col" class="w-[25%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">Unit</span>
                    </div>
                  </th>


                </tr>
              </thead>

              <tbody class="divide-y divide-gray-200">
                <tr v-for="(item, index) in items" :key="index" class="group hover:bg-gray-50">
                  <td class="size-px whitespace-nowrap pe-4 py-3">
                    <span class="text-sm text-gray-600">{{ (meta.current_page - 1) * meta.per_page + (index + 1) }}</span>
                  </td>

                  <td class="size-px whitespace-nowrap pe-4 py-3">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center cursor-pointer" data-hs-overlay="#drawer-right-detail" @click="selectedItem=__duplicateVar(item)">
                        <img v-if="item && item.img_url" class="rounded-full size-9 object-cover" :src="item.img_url" alt="avatar-image"
                          referrerpolicy="no-referrer" @error="handleAvatarError(item)">
                        <div v-else>
                          <div
                            class="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs font-medium">
                            {{ __generateInitial(`${item.first_name} ${item.last_name}`) }}
                          </div>
                        </div>
                        <div class="grow">
                          <span class="text-sm font-medium text-gray-800 ml-2">
                            {{ item.first_name }} {{ item.last_name }}
                          </span>
                        </div>
                      </div>

                      <!-- Actions Dropdown - Only visible on hover -->
                      <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <ButtonDropdown :customClassItems="'w-32'">
                          <template #button>
                            <button class="flex items-center justify-center size-[30px] text-sm font-medium rounded-md text-gray-400 hover:text-gray-600 focus:outline-none">
                              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="1"/>
                                <circle cx="12" cy="5" r="1"/>
                                <circle cx="12" cy="19" r="1"/>
                              </svg>
                            </button>
                          </template>
                          <template #items>
                            <button @click="edit(item)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              Edit
                            </button>
                            <button @click="deleteItem(item)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-red-600 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              Delete
                            </button>
                          </template>
                        </ButtonDropdown>
                      </div>
                    </div>
                  </td>

                  <td class="whitespace-nowrap pe-4 py-3">
                    <template v-if="item.roles && item.roles.length > 0">
                      <span
                        v-for="(role, index) in item.roles"
                        :key="role.id"
                        class="text-sm text-gray-600"
                      >
                        {{ role.name }}{{ index < item.roles.length - 1 ? ', ' : '' }}
                      </span>
                    </template>
                    <span v-else class="text-sm text-gray-600">-</span>
                  </td>

                  <td class="whitespace-nowrap pe-4 py-3">
                    <template v-if="item.groups && item.groups.length > 0">
                      <span
                        v-for="(role, index) in item.groups"
                        :key="role.id"
                        class="text-sm text-gray-600"
                      >
                        {{ role.name }}{{ index < item.groups.length - 1 ? ', ' : '' }}
                      </span>
                    </template>
                    <span v-else class="text-sm text-gray-600">-</span>
                  </td>

                  <td class="size-px whitespace-nowrap pe-4 py-3">
                    <span class="text-sm text-gray-600">{{ item.unit || '-' }}</span>
                  </td>


                </tr>
              </tbody>
            </table>
          </div>
          <!-- Footer -->
        <!-- Modern Pagination -->
        <ModernPagination
          :total="meta.total"
          :current-page="meta.current_page"
          :last-page="meta.last_page"
          :per-page="meta.per_page"
          :is-fetching="isFetching"
          @page-changed="onPageChanged"
          @per-page-changed="onPerPageChanged"
        />
        </div>
        <!-- End Table Section -->

        
        <!-- End Footer -->
      </div>
      <!-- End Tab Content -->
    </div>

    <!-- Empty State -->
    <div v-else-if="!isFetching && !items.length" class="flex flex-col items-center justify-center h-64">
      <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-1">No users found</h3>
      <p class="text-gray-500">Get started by adding your first user.</p>
    </div>


  </div>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import { PlusIcon } from '@heroicons/vue/outline';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import ButtonDropdown from '@/components/global/ButtonDropdown.vue';
import ModernPagination from '@/components/global/ModernPagination.vue';
import DataTable from '@/components/global/DataTable.vue';
import SortFilter from '@/components/global/SortFilter.vue';
import userApi from '@/api/user.js';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'UserTab',
  components: {
    PlusIcon,
    TButton,
    TInput,
    ButtonDropdown,
    ModernPagination,
    DataTable,
    SortFilter,
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  inject: ['showUserModal'],
  data() {
    return {
      isFetching: false,
      keyword: '',
      items: [],
      showFilter: false,
      meta: {
        total: 0,
        per_page: 10,
        current_page: 1,
        last_page: 1,
        first_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        previous_page_url: null
      },
      order_by: 'first_name',
      sort_by: 'desc',
      userSortOptions: [
        
        { value: 'first_name', label: 'Name' },
        { value: 'created_at', label: 'Created Date' },
        { value: 'updated_at', label: 'Updated Date' }
      ],
      selectedItem: null,
      tableColumns: [
        { key: 'name', title: 'Full Name' },
        { key: 'email', title: 'Email' },
        { key: 'phoneNumber', title: 'Phone' },
        { key: 'status', title: 'Status' }
      ]
    };
  },
  mounted() {
    this.fetchData();
  },
  computed: {
    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.meta.current_page - 2);
      const end = Math.min(this.meta.last_page, this.meta.current_page + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  methods: {
    addNew() {
      this.showUserModal('add');
    },

    edit(item) {
      this.showUserModal('edit', item);
    },

    deleteItem(item) {
      const first_name = `${item.first_name} ${item.last_name}`.trim();
      this.$confirmDelete({
        itemName: first_name,
        message: this.$t('Confirmation.Are you sure you want to delete this user?'),
        onConfirm: () => {
          this.isFetching = true;
          const callback = (response) => {
            this.fetchData();
            this.isFetching = false;
            const message = response.message || 'User deleted successfully';
            this.__showNotif('success', 'Success', message);
          };
          const errorCallback = (error) => {
            console.error('Delete error:', error);
            this.isFetching = false;
            const message = error?.response?.data?.message || 'Failed to delete user';
            this.__showNotif('error', 'Error', message);
          };
          userApi.delete(item.id, callback, errorCallback);
        }
      });
    },

    onInputSearch() {
      this.debounce(this.fetchData, 300)();
    },

    toggleFilter() {
      this.showFilter = !this.showFilter;
    },

    onFilterChanged(filterData) {
      this.order_by = filterData.order_by;
      this.sort_by = filterData.sort_by;
      this.meta.current_page = 1; // Reset to first page when filtering
      this.fetchData();
    },

    submitUser(userData, callbacks = {}) {
      this.isFetching = true;

      const callback = (response) => {
        this.fetchData(); // Refresh the list
        this.isFetching = false;
        const message = response.message || (userData.id ? 'User updated successfully' : 'User created successfully');

        // Call success callback if provided
        if (callbacks.onSuccess) {
          callbacks.onSuccess(message);
        } else {
          // Fallback to showing notification if no callback
          this.__showNotif('success', 'Success', message);
        }
      };

      const errorCallback = (error) => {
        console.error('Save user error:', error);
        this.isFetching = false;
        const message = error?.response?.data?.message || (userData.id ? 'Failed to update user' : 'Failed to create user');

        // Call error callback if provided
        if (callbacks.onError) {
          callbacks.onError(message);
        } else {
          // Fallback to showing notification if no callback
          this.__showNotif('error', 'Error', message);
        }
      };

      // Check if we're editing (userData should have an id for updates)
      if (userData.id) {
        // Update existing user
        userApi.update(userData.id, userData, callback, errorCallback);
      } else {
        // Create new user
        userApi.create(userData, callback, errorCallback);
      }
    },

    nextPage() {
      this.meta.current_page = this.meta.current_page + 1;
      this.fetchData();
    },

    prevPage() {
      if (this.meta.current_page > 1) {
        this.meta.current_page = this.meta.current_page - 1;
        this.fetchData();
      }
    },

    goToPage(page) {
      this.meta.current_page = page;
      this.fetchData();
    },

    changePerPage() {
      this.meta.current_page = 1;
      this.fetchData();
    },

    onPageChanged(page) {
      this.meta.current_page = page;
      this.fetchData();
    },

    onPerPageChanged(per_page) {
      this.meta.per_page = per_page;
      this.meta.current_page = 1;
      this.fetchData();
    },

    onSearch(searchTerm) {
      this.keyword = searchTerm;
      this.onInputSearch();
    },

    debounce(func, wait) {
      let timeout;
      return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },

    __duplicateVar(item) {
      return JSON.parse(JSON.stringify(item));
    },

    __generateInitial(first_name) {
      if (!first_name) return 'DS';
      return first_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    },

    handleAvatarError(item) {
      // Handle avatar loading error
      const first_name = `${item.first_name} ${item.last_name}`.trim();
      console.log('Avatar error for:', first_name);
    },

    fetchData() {
      this.isFetching = true;

      const callback = (response) => {
        this.items = response.data || [];
        this.meta = response.meta || this.meta;
        this.isFetching = false;
      };

      const errorCallback = (error) => {
        console.error('Fetch error:', error);
        this.isFetching = false;
      };

      const params = {
        order_by: this.order_by,
        sort_by: this.sort_by,
        page: this.meta.current_page,
        limit: this.meta.per_page,
      };

      if (this.keyword) {
        params.keyword = this.keyword;
      }

      userApi.getList(params, callback, errorCallback);
    }
  }
};
</script>
