<template>
  <loader-circle v-if="isFetching" />
  
  <div class="h-full flex flex-col">
    <div class="flex justify-end items-center gap-x-2 lg:m-2">
      <!-- button add new -->
      <div>
        <t-button :color="`primary-solid`" @click="add()">
          <PlusIcon class="h-5 w-5 text-white" />
          <span class="text-sm text-white text-nowrap">Add Group</span>
        </t-button>
      </div>

      <div class="relative">
        <TInput
          v-model="keyword"
          :value="keyword"
          type="text"
          :placeholder="$t('Search')"
          @input="onInputSearch"
          customStyle="padding-left: 2.5rem; padding-right: 2rem;"
        />
        <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
          <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </div>
      </div>

      <!-- Filter Button -->
      <div class="relative">
        <button
          @click="toggleFilter"
          class="inline-flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          :class="{ 'bg-primary-50 border-primary-300 text-primary-700': showFilter }"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
          </svg>
          Filter
        </button>

        <!-- Sort Filter Component -->
        <SortFilter
          :showFilter="showFilter"
          :orderBy="order_by"
          :sortBy="sort_by"
          :sortOptions="groupSortOptions"
          @update:orderBy="order_by = $event"
          @update:sortBy="sort_by = $event"
          @update:showFilter="showFilter = $event"
          @filter-changed="onFilterChanged"
        />
      </div>
    </div>
    
    <!-- Table Section -->
    <div v-if="!isFetching && items.length" class="flex-1 overflow-hidden m-2">
      <div class="overflow-auto h-full [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
        <div class="min-w-full inline-block align-middle">
          <!-- Table -->
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="sticky top-0 bg-white">
              <tr>
                <th scope="col" class="max-w-[50px]">
                  <div class="pe-4 py-3 text-start">
                    <span class="text-sm font-semibold text-gray-800"></span>
                  </div>
                </th>
                <th scope="col" class="w-[50%]">
                  <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutnms" type="button"
                        class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap text-black-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Group Name
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" />
                        </svg>
                      </button>
                    </div>
                    <!-- End Sort Dropdown -->
                  </div>
                </th>
                <th scope="col" class="w-[50%]">
                  <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutads" type="button"
                        class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap text-black-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Description
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </th>

              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr v-for="(item, index) in items" :key="index" class="group hover:bg-gray-50">
                <td class="size-px whitespace-nowrap pe-4 py-3">
                  <span class="text-sm text-gray-600">{{ (meta.current_page - 1) * meta.per_page + (index + 1) }}</span>
                </td>
                <td class="size-px whitespace-nowrap pe-4 py-3">
                  <div class="w-full flex items-center justify-between gap-x-3">
                    <div class="grow">
                      <span class="text-sm font-medium text-gray-800">{{ item.name }}</span>
                    </div>

                    <!-- Actions Dropdown - Only visible on hover -->
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <ButtonDropdown :customClassItems="'w-32'">
                        <template #button>
                          <button class="flex items-center justify-center size-[30px] text-sm font-medium rounded-md text-gray-400 hover:text-gray-600 focus:outline-none">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <circle cx="12" cy="12" r="1"/>
                              <circle cx="12" cy="5" r="1"/>
                              <circle cx="12" cy="19" r="1"/>
                            </svg>
                          </button>
                        </template>
                        <template #items>
                          <button @click="edit(item)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            Edit
                          </button>
                          <button @click="deleteItem(item)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-red-600 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            Delete
                          </button>
                        </template>
                      </ButtonDropdown>
                    </div>
                  </div>
                </td>
                <td class="size-px whitespace-nowrap pe-4 py-3">
                  <span class="text-sm text-gray-600">{{ item.description }}</span>
                </td>

              </tr>
            </tbody>
          </table>
        <!-- Modern Pagination -->
        <ModernPagination
          v-if="!isFetching && items.length"
          :total="meta.total"
          :current-page="meta.current_page"
          :last-page="meta.last_page"
          :per-page="meta.per_page"
          :is-fetching="isFetching"
          @page-changed="onPageChanged"
          @per-page-changed="onPerPageChanged"
        />
        <!-- End Footer -->
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!isFetching && !items.length" class="flex flex-col items-center justify-center h-64 m-2">
      <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-1">No groups found</h3>
      <p class="text-gray-500">Get started by adding your first group.</p>
    </div>

    <!-- Loader -->
    <loader-circle v-if="isFetching" />
  </div>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import { PlusIcon } from '@heroicons/vue/outline';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import ButtonDropdown from '@/components/global/ButtonDropdown.vue';
import ModernPagination from '@/components/global/ModernPagination.vue';
import SortFilter from '@/components/global/SortFilter.vue';
import groupApi from '@/api/group.js';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'GroupTab',
  components: {
    PlusIcon,
    TButton,
    TInput,
    ButtonDropdown,
    ModernPagination,
    SortFilter,
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  inject: ['showGroupModal'],
  data() {
    return {
      isFetching: false,
      keyword: '',
      items: [],
      showFilter: false,
      meta: {
        total: 0,
        per_page: 10,
        current_page: 1,
        last_page: 1,
        first_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        previous_page_url: null
      },
      order_by: 'name',
      sort_by: 'desc',
      groupSortOptions: [
        
        { value: 'name', label: 'Group Name' },
        { value: 'description', label: 'Description' },
        { value: 'created_at', label: 'Created Date' },
        { value: 'updated_at', label: 'Updated Date' }
      ]
    };
  },
  mounted() {
    this.fetchData();
  },
  computed: {
    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.meta.current_page - 2);
      const end = Math.min(this.meta.last_page, this.meta.current_page + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  methods: {
    add() {
      this.showGroupModal('add');
    },

    edit(item) {
      this.showGroupModal('edit', item);
    },

    deleteItem(item) {
      this.$confirmDelete({
        itemName: item.name,
        message: this.$t('Confirmation.Are you sure you want to delete this group?'),
        onConfirm: () => {
          this.isFetching = true;
          const callback = (response) => {
            this.fetchData();
            this.isFetching = false;
            const message = response.message || 'Group deleted successfully';
            this.__showNotif('success', 'Success', message);
          };
          const errorCallback = (error) => {
            console.error('Delete error:', error);
            this.isFetching = false;
            const message = error?.response?.data?.message || 'Failed to delete group';
            this.__showNotif('error', 'Error', message);
          };
          groupApi.delete(item.id, callback, errorCallback);
        }
      });
    },

    onInputSearch() {
      this.debounce(this.fetchData, 300)();
    },

    toggleFilter() {
      this.showFilter = !this.showFilter;
    },

    onFilterChanged(filterData) {
      this.order_by = filterData.order_by;
      this.sort_by = filterData.sort_by;
      this.meta.current_page = 1; // Reset to first page when filtering
      this.fetchData();
    },

    submitGroup(groupData, callbacks = {}) {
      this.isFetching = true;

      const callback = (response) => {
        this.fetchData(); // Refresh the list
        this.isFetching = false;
        const message = response.message || (groupData.id ? 'Group updated successfully' : 'Group created successfully');

        // Call success callback if provided
        if (callbacks.onSuccess) {
          callbacks.onSuccess(message);
        } else {
          // Fallback to showing notification if no callback
          this.__showNotif('success', 'Success', message);
        }
      };

      const errorCallback = (error) => {
        console.error('Save group error:', error);
        this.isFetching = false;
        const message = error?.response?.data?.message || (groupData.id ? 'Failed to update group' : 'Failed to create group');

        // Call error callback if provided
        if (callbacks.onError) {
          callbacks.onError(message);
        } else {
          // Fallback to showing notification if no callback
          this.__showNotif('error', 'Error', message);
        }
      };

      // Check if we're editing (groupData should have an id for updates)
      if (groupData.id) {
        // Update existing group
        groupApi.update(groupData.id, groupData, callback, errorCallback);
      } else {
        // Create new group
        groupApi.create(groupData, callback, errorCallback);
      }
    },

    nextPage() {
      this.meta.current_page = this.meta.current_page + 1;
      this.fetchData();
    },

    prevPage() {
      if (this.meta.current_page > 1) {
        this.meta.current_page = this.meta.current_page - 1;
        this.fetchData();
      }
    },

    goToPage(page) {
      this.meta.current_page = page;
      this.fetchData();
    },

    changePerPage() {
      this.meta.current_page = 1;
      this.fetchData();
    },

    onPageChanged(page) {
      this.meta.current_page = page;
      this.fetchData();
    },

    onPerPageChanged(per_page) {
      this.meta.per_page = per_page;
      this.meta.current_page = 1;
      this.fetchData();
    },

    debounce(func, wait) {
      let timeout;
      return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },

    fetchData() {
      this.isFetching = true;

      const callback = (response) => {
        this.items = response.data || [];
        this.meta = response.meta || this.meta;
        this.isFetching = false;
      };

      const errorCallback = (error) => {
        console.error('Fetch error:', error);
        this.isFetching = false;
      };

      const params = {
        order_by: this.order_by,
        sort_by: this.sort_by,
        page: this.meta.current_page,
        limit: this.meta.per_page,
      };

      if (this.keyword) {
        params.keyword = this.keyword;
      }

      groupApi.getList(params, callback, errorCallback);
    }
  }
};
</script>
