<template>
  <div class="h-full flex flex-col">
    <div class="flex justify-end items-center gap-x-2 lg:m-2">
      <!-- button add new -->
      <div>
        <t-button :color="`primary-solid`" @click="add()">
          <PlusIcon class="h-5 w-5 text-white" />
          <span class="text-sm text-white text-nowrap">Add Role</span>
        </t-button>
      </div>

      <div class="relative">
        <TInput
          v-model="keyword"
          :value="keyword"
          type="text"
          :placeholder="$t('Search')"
          @input="onInputSearch"
          customStyle="padding-left: 2.5rem; padding-right: 2rem;"
        />
        <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
          <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </div>
      </div>

      <!-- Filter Button -->
      <div class="relative">
        <button
          @click="toggleFilter"
          class="inline-flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          :class="{ 'bg-primary-50 border-primary-300 text-primary-700': showFilter }"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
          </svg>
          Filter
        </button>

        <!-- Sort Filter Component -->
        <SortFilter
          :showFilter="showFilter"
          :orderBy="order_by"
          :sortBy="sort_by"
          :sortOptions="roleSortOptions"
          @update:orderBy="order_by = $event"
          @update:sortBy="sort_by = $event"
          @update:showFilter="showFilter = $event"
          @filter-changed="onFilterChanged"
        />
      </div>
    </div>
    
    <div v-if="!isFetching && items.length" >
      <!-- Table Section -->
      <div class="flex-1 overflow-hidden m-2">
        <div class="overflow-auto h-full [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="sticky top-0 bg-white">
                <tr>
                  <th scope="col" class="w-[50px]">
                    <div class="pe-4 py-3 text-start">
                      <span class="text-sm font-semibold text-gray-800"></span>
                    </div>
                  </th>
                  <th scope="col" class="w-[50%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">Role Name</span>
                    </div>
                  </th>
                  <th scope="col" class="w-[50%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">Access</span>
                    </div>
                  </th>
                  <!-- <th scope="col" class="w-[33%]">
                    <div class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-normal text-gray-800">
                      <span class="text-sm font-semibold text-gray-800">Permissions</span>
                    </div>
                  </th> -->

                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr v-for="(item, index) in items" :key="index" class="group hover:bg-gray-50">
                  <td class="size-px whitespace-nowrap pe-4 py-3">
                    <span class="text-sm text-gray-600">{{ (meta.current_page - 1) * meta.per_page + (index + 1) }}</span>
                  </td>
                  <td class="size-px whitespace-nowrap pe-4 py-3">
                    <div class="w-full flex items-center justify-between gap-x-3">
                      <div class="grow">
                        <span class="text-sm font-medium text-gray-800">{{ item.name }}</span>
                      </div>

                      <!-- Actions Dropdown - Only visible on hover -->
                      <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <ButtonDropdown :customClassItems="'w-32'">
                          <template #button>
                            <button class="flex items-center justify-center size-[30px] text-sm font-medium rounded-md text-gray-400 hover:text-gray-600 focus:outline-none">
                              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="1"/>
                                <circle cx="12" cy="5" r="1"/>
                                <circle cx="12" cy="19" r="1"/>
                              </svg>
                            </button>
                          </template>
                          <template #items>
                            <button @click="edit(item)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              Edit
                            </button>
                            <button @click="deleteItem(item)" type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-md text-[13px] font-normal text-red-600 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              Delete
                            </button>
                          </template>
                        </ButtonDropdown>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap pe-4 py-3">
                    <span class="text-sm text-gray-600">
                      {{ getEnabledAccessRights(item.access_rights) }}
                    </span>
                  </td>
                  <!-- <td class="size-px whitespace-nowrap pe-4 py-3">
                    <span class="text-sm text-gray-600"></span>
                  </td> -->

                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Modern Pagination -->
      <ModernPagination
        class="px-2"
        v-if="!isFetching && items.length"
        :total="meta.total"
        :current-page="meta.current_page"
        :last-page="meta.last_page"
        :per-page="meta.per_page"
        :is-fetching="isFetching"
        @page-changed="onPageChanged"
        @per-page-changed="onPerPageChanged"
      />
      <!-- End Footer -->
    </div>

    <!-- Empty State -->
    <div v-else-if="!isFetching && !items.length" class="flex flex-col items-center justify-center h-64 m-2">
      <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-1">No roles found</h3>
      <p class="text-gray-500">Get started by adding your first role.</p>
    </div>

    <!-- Loader -->
    <loader-circle v-if="isFetching" />
  </div>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import { PlusIcon } from '@heroicons/vue/outline';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import ButtonDropdown from '@/components/global/ButtonDropdown.vue';
import ModernPagination from '@/components/global/ModernPagination.vue';
import DataTable from '@/components/global/DataTable.vue';
import SortFilter from '@/components/global/SortFilter.vue';
import rolesApi from '@/api/roles.js';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'RoleTab',
  components: {
    PlusIcon,
    TButton,
    TInput,
    ButtonDropdown,
    ModernPagination,
    DataTable,
    SortFilter,
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  inject: ['showRoleModal'],
  data() {
    return {
      isFetching: false,
      keyword: '',
      items: [],
      showFilter: false,
      meta: {
        total: 0,
        per_page: 10,
        current_page: 1,
        last_page: 1,
        first_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        previous_page_url: null
      },
      order_by: 'name',
      sort_by: 'desc',
      roleSortOptions: [
        
        { value: 'name', label: 'Role Name' },
        { value: 'description', label: 'Description' },
        { value: 'created_at', label: 'Created Date' },
        { value: 'updated_at', label: 'Updated Date' }
      ],
      tableColumns: [
        { key: 'name', title: 'Role Name' },
        { key: 'description', title: 'Description' },
        { key: 'permissions', title: 'Permissions' }
      ]
    };
  },
  mounted() {
    this.fetchData();
  },
  computed: {
    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.meta.current_page - 2);
      const end = Math.min(this.meta.last_page, this.meta.current_page + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  methods: {
    getEnabledAccessRights(jsonString) {
      let accessObj;
      try {
        if (typeof jsonString === 'string') {
          accessObj = JSON.parse(jsonString);
        } else if (typeof jsonString === 'object' && jsonString !== null) {
          accessObj = jsonString; // Already an object
        } else {
          return 'N/A'; // Or an empty string if you prefer
        }
      } catch (e) {
        console.error("Error parsing access_rights JSON:", e);
        return 'Invalid Data'; // Or an empty string
      }

      const enabledRights = [];
      for (const key in accessObj) {
        // Ensure the property belongs to the object itself, not its prototype chain
        if (Object.prototype.hasOwnProperty.call(accessObj, key) && accessObj[key] === true) {
          enabledRights.push(this.formatAccessKey(key));
        }
      }

      if (enabledRights.length === 0) {
        return 'No specific rights'; // Or "N/A", or ""
      }

      return enabledRights.join(', ');
    },

    // Re-use the existing formatAccessKey method
    formatAccessKey(key) {
      if (!key) return '';
      return key
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    },
    add() {
      this.showRoleModal('add');
    },

    edit(item) {
      this.showRoleModal('edit', item);
    },

    deleteItem(item) {
      this.$confirmDelete({
        itemName: item.name,
        message: this.$t('Confirmation.Are you sure you want to delete this role?'),
        onConfirm: () => {
          this.isFetching = true;
          const callback = (response) => {
            this.fetchData();
            this.isFetching = false;
            const message = response.message || 'Role deleted successfully';
            this.__showNotif('success', 'Success', message);
          };
          const errorCallback = (error) => {
            console.error('Delete error:', error);
            this.isFetching = false;
            const message = error?.response?.data?.message || 'Failed to delete role';
            this.__showNotif('error', 'Error', message);
          };
          rolesApi.delete(item.id, callback, errorCallback);
        }
      });
    },

    onInputSearch() {
      this.debounce(this.fetchData, 300)();
    },

    toggleFilter() {
      this.showFilter = !this.showFilter;
    },

    onFilterChanged(filterData) {
      this.order_by = filterData.order_by;
      this.sort_by = filterData.sort_by;
      this.meta.current_page = 1; // Reset to first page when filtering
      this.fetchData();
    },

    submitRole(roleData, callbacks = {}) {
      this.isFetching = true;

      const callback = (response) => {
        this.fetchData(); // Refresh the list
        this.isFetching = false;
        const message = response.message || (roleData.id ? 'Role updated successfully' : 'Role created successfully');

        // Call success callback if provided
        if (callbacks.onSuccess) {
          callbacks.onSuccess(message);
        } else {
          // Fallback to showing notification if no callback
          this.__showNotif('success', 'Success', message);
        }
      };

      const errorCallback = (error) => {
        console.error('Save role error:', error);
        this.isFetching = false;
        const message = error?.response?.data?.message || (roleData.id ? 'Failed to update role' : 'Failed to create role');

        // Call error callback if provided
        if (callbacks.onError) {
          callbacks.onError(message);
        } else {
          // Fallback to showing notification if no callback
          this.__showNotif('error', 'Error', message);
        }
      };

      // Check if we're editing (roleData should have an id for updates)
      if (roleData.id) {
        // Update existing role
        rolesApi.update(roleData.id, roleData, callback, errorCallback);
      } else {
        // Create new role
        rolesApi.create(roleData, callback, errorCallback);
      }
    },

    nextPage() {
      this.meta.current_page = this.meta.current_page + 1;
      this.fetchData();
    },

    prevPage() {
      if (this.meta.current_page > 1) {
        this.meta.current_page = this.meta.current_page - 1;
        this.fetchData();
      }
    },

    goToPage(page) {
      this.meta.current_page = page;
      this.fetchData();
    },

    changePerPage() {
      this.meta.current_page = 1;
      this.fetchData();
    },

    onPageChanged(page) {
      this.meta.current_page = page;
      this.fetchData();
    },

    onPerPageChanged(per_page) {
      this.meta.per_page = per_page;
      this.meta.current_page = 1;
      this.fetchData();
    },

    debounce(func, wait) {
      let timeout;
      return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },

    getPermissionsList(item) {
      // Convert permissions object to array for display
      if (!item.permissions) return [];
      if (Array.isArray(item.permissions)) return item.permissions;

      const permissions = [];
      if (item.permissions.crud) permissions.push('CRUD');
      if (item.permissions.view) permissions.push('View');
      if (item.permissions.comment) permissions.push('Comment');
      if (item.permissions.assign) permissions.push('Assign');
      if (item.permissions.answerFlow) permissions.push('Answer Flow');

      return permissions;
    },

    fetchData() {
      this.isFetching = true;

      const callback = (response) => {
        this.items = response.data || [];
        this.meta = response.meta || this.meta;
        this.isFetching = false;
      };

      const errorCallback = (error) => {
        console.error('Fetch error:', error);
        this.isFetching = false;
      };

      const params = {
        order_by: this.order_by,
        sort_by: this.sort_by,
        page: this.meta.current_page,
        limit: this.meta.per_page,
      };

      if (this.keyword) {
        params.keyword = this.keyword;
      }

      rolesApi.getList(params, callback, errorCallback);
    }
  }
};
</script>
