<template>
    <div class="w-full max-w-2xl mx-auto p-4 sm:p-6 bg-white rounded-lg">
        <!-- Profile Photo and Upload Button -->
        <div class="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6 mb-8">
            <img :src="getUserImage(form)" alt="User profile photo" class="w-20 h-20 sm:w-24 sm:h-24 rounded-full object-cover flex-shrink-0" />
            <div class="text-center sm:text-left flex-1">
                <input type="file" ref="fileInput" accept="image/*" @change="handlePhotoUpload" class="hidden" />
                <t-button @click="triggerFileUpload" :isDisabled="isUploadingFile" :color="`primary-white`" class="mb-3 px-4 py-2" :isLoading="isUploadingFile">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4 sm:h-5 sm:w-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                    </svg>
                    <span class="text-xs sm:text-sm">{{ $t('Upload new Photo') }}</span>
                </t-button>
                <p class="text-xs sm:text-sm text-gray-500 leading-relaxed">{{ $t('Photos help your teammates recognize you in desidia') }}</p>
            </div>
        </div>

        <!-- Form Fields -->
        <form @submit.prevent="saveChanges" class="space-y-6">
            <!-- First Name -->
            <div>
                <label for="first-name" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('First Name') }}</label>
                <Input
                    v-model="form.first_name"
                    :value="form.first_name"
                    type="text-input"
                    :placeholder="$t('Enter your first name')"
                    data-test="first-name-input"
                />
            </div>

            <!-- Last Name -->
            <div>
                <label for="last-name" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('Last Name') }}</label>
                <Input
                    v-model="form.last_name"
                    :value="form.last_name"
                    type="text-input"
                    :placeholder="$t('Enter your last name')"
                    data-test="last-name-input"
                />
            </div>

            <!-- Phone -->
            <div>
                <label for="phone" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('Phone') }}</label>
                <Input
                    v-model="form.phone"
                    :value="form.phone"
                    type="tel"
                    :placeholder="$t('Enter your phone number')"
                    data-test="phone-input"
                />
            </div>

            <!-- Email -->
            <div>
                <label for="email" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('Email') }}</label>
                <Input
                    v-model="form.email"
                    :value="form.email"
                    type="email"
                    :placeholder="$t('Enter your email address')"
                    :isDisabled="true"
                    data-test="email-input"
                />
            </div>

            <!-- Address -->
            <div>
                <label for="address" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('Address') }}</label>
                <Input
                    v-model="form.address"
                    :value="form.address"
                    type="text-input"
                    :placeholder="$t('Enter your address')"
                    data-test="address-input"
                />
            </div>

            <!-- Unit -->
            <div>
                <label for="unit" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('Unit') }}</label>
                <Input
                    v-model="form.unit"
                    :value="form.unit"
                    type="text-input"
                    :placeholder="$t('Enter your unit/apartment number')"
                    data-test="unit-input"
                />
            </div>

            <!-- Display Contact -->
            <div>
                <label for="display-contact" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">{{ $t('Display Contact') }}</label>
                <div class="flex items-center space-x-3">
                    <input
                        id="display-contact"
                        :value="form.is_display_contact"
                        v-model="form.is_display_contact"
                        type="checkbox"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <span class="text-sm sm:text-base text-gray-600">{{ $t('Allow others to see my contact information') }}</span>
                </div>
            </div>

            <!-- Image URL (hidden field for API) -->
            <input type="hidden" v-model="form.img_url" />

            <!-- Form Buttons -->
            <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
                <Button
                    type="button"
                    @click="cancelChanges"
                    color="secondary-solid"
                    class="w-full sm:w-auto order-2 sm:order-1"
                >
                    {{ $t('Cancel') }}
                </Button>
                <Button
                    type="submit"
                    color="primary-solid"
                    :isLoading="isSaving"
                    class="w-full sm:w-auto order-1 sm:order-2"
                >
                    {{ $t('Save Changes') }}
                </Button>
            </div>
        </form>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import fileApi from '@/api/files';
import authApi from '@/api/auth';
import Input from '@/components/form/Input.vue';
import Button from '@/components/global/Button.vue';

export default {
    components: {
        Input,
        Button
    },
    data() {
        return {
            form: {
                first_name: '',
                last_name: '',
                phone: '',
                email: '',
                address: '',
                unit: '',
                is_display_contact: false,
                img_url: ''
            },
            isUploadingFile: false,
            isSaving: false
        };
    },
    computed: {
        ...mapGetters({
            user: 'auth/user', // Map user getter from Vuex store
            isClient: 'auth/isClient',
        })
    },
    watch: {
        // Whenever the user object changes, update the form data
        user: {
            immediate: true,
            handler(newUser) {
                if (newUser) {
                    this.form.first_name = newUser.first_name  || '';
                    this.form.last_name = newUser.last_name || '';
                    this.form.phone = newUser.phone || '';
                    this.form.email = newUser.email || '';
                    this.form.address = newUser.address || '';
                    this.form.unit = newUser.unit || '';
                    this.form.is_display_contact = !!newUser.is_display_contact || false;
                    this.form.img_url = newUser.img_url || '';
                }
            }
        }
    },
    methods: {
        ...mapActions({
            'setUser': 'auth/setUser'
        }),
        triggerFileUpload() {
            this.$refs.fileInput.click();
        },
        handlePhotoUpload(event) {
            const file = event.target.files[0];
            if (file && ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
                this.compressAndUploadImage(file);
            } else {
                this.__showNotif('error', 'Error', 'Unsupported file type. Please upload a JPEG, PNG, or GIF.');
            }
        },
        compressAndUploadImage(file) {
            const reader = new FileReader();

            reader.onload = (e) => {
                const img = new Image();
                img.src = e.target.result;

                img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Set desired maximum width and height
                const maxWidth = 100;
                const maxHeight = 100;
                let width = img.width;
                let height = img.height;

                // Calculate aspect ratio
                if (width > height) {
                    if (width > maxWidth) {
                    height *= (maxWidth / width);
                    width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                    width *= (maxHeight / height);
                    height = maxHeight;
                    }
                }

                // Set canvas size to the new dimensions
                canvas.width = width;
                canvas.height = height;

                // Draw the image onto the canvas with the new size
                ctx.drawImage(img, 0, 0, width, height);

                // Convert the canvas to a Blob or base64 data URL
                canvas.toBlob((blob) => {
                    const compressedFile = new File([blob], file.name, { type: file.type });
                    this.uploadFile(compressedFile);
                }, file.type, 0.9); // Adjust compression level (0-1)
                };
            };

          reader.readAsDataURL(file); // Read the file as a data URL for the Image object
        },

        uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            this.isUploadingFile = true;
            fileApi.upload(formData, this.onUploadSuccess, this.onUploadError);
        },
        onUploadSuccess(response) {
            this.isUploadingFile = false;
            this.form.img_url = response.data; // Update form.img_url with the new photo
            this.__showNotif('success', 'Success', 'Photo uploaded successfully!');
        },

        onUploadError() {
            this.isUploadingFile = false;
            this.__showNotif('error', 'Error', 'There was an error uploading the photo.');
        },
        saveChanges() {
            this.isSaving = true;
            authApi.update(this.form, this.onSaveSuccess, this.onSaveError);
        },
        onSaveSuccess(response) {
            this.isSaving = false;
            const data = response.data;
            const message = response.message;
            this.setUser(data);
            this.__showNotif('success', 'Success', message);
        },
        onSaveError(error) {
            this.isSaving = false;
            console.log(error);
            this.__showNotif('error', 'Error', 'There was an error saving your profile.');
        },
        cancelChanges() {
            // Reset the form to the user's initial data
            this.$store.dispatch('auth/fetchUser'); // Assuming fetchUser refetches the user data
        },
        getUserImage(user) {
            if (user?.img_url) {
                return user.img_url;
            } else {
                const defaultName = user && (user.first_name || user.last_name)
                    ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                    : "desidia";
                return this.__generateInitialCanvas(defaultName);
            }
        },
    }
};
</script>

<style scoped>
/* Customize styling if necessary */
</style>
