<template>
    <div class="max-w-lg mx-auto  p-6 bg-white rounded-lg ">
        <h2 class="text-xl font-semibold mb-4">Password</h2>

        <!-- Current Password -->
        <div class="mb-4">
            <label for="current-password" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('Form Labels.Current password') }} <span class="text-red-500">*</span>
            </label>
            <TInput
                v-model="form.currentPassword"
                :value="form.currentPassword"
                type="password"
                :placeholder="$t('Form Placeholders.Enter current password')"
                :required="true"
                :showValidation="showValidation"
                :preventSpaces="true"
            />
        </div>

        <!-- New Password -->
        <div class="mb-4">
            <label for="new-password" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('Form Labels.New password') }} <span class="text-red-500">*</span>
            </label>
            <TInput
                v-model="form.newPassword"
                :value="form.newPassword"
                type="password"
                :placeholder="$t('Form Placeholders.Enter new password')"
                :required="true"
                :showValidation="showValidation"
                :preventSpaces="true"
                :minLength="6"
            />
            <!-- Helper message when user starts typing -->
            <p v-if="form.newPassword && form.newPassword.length > 0 && form.newPassword.length < 6"
               class="text-blue-600 text-xs mt-1">
                Password must be at least 6 characters ({{ form.newPassword.length }}/6)
            </p>
            <!-- Success message when password meets requirements -->
            <p v-if="form.newPassword && form.newPassword.length >= 6"
               class="text-green-600 text-xs mt-1">
                ✓ Password meets minimum length requirement
            </p>
            <!-- Error message only shown during validation -->
            <p v-if="showValidation && form.newPassword && form.newPassword.length < 6"
               class="text-red-500 text-xs mt-1">
                Password must be at least 6 characters long
            </p>
        </div>

        <!-- Confirm New Password -->
        <div class="mb-6">
            <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('Form Labels.Confirm new password') }} <span class="text-red-500">*</span>
            </label>
            <TInput
                v-model="form.confirmPassword"
                :value="form.confirmPassword"
                type="password"
                :placeholder="$t('Form Placeholders.Confirm new password')"
                :required="true"
                :showValidation="showValidation"
                :preventSpaces="true"
                :confirmPassword="form.newPassword"
                :minLength="6"
            />
            <!-- Helper message when user starts typing -->
            <p v-if="form.confirmPassword && form.confirmPassword.length > 0 && form.confirmPassword.length < 6"
               class="text-blue-600 text-xs mt-1">
                Password confirmation must be at least 6 characters ({{ form.confirmPassword.length }}/6)
            </p>
            <!-- Password mismatch message (when both passwords have minimum length) -->
            <p v-if="form.newPassword && form.confirmPassword &&
                     form.newPassword.length >= 6 && form.confirmPassword.length >= 6 &&
                     form.newPassword !== form.confirmPassword"
               class="text-orange-600 text-xs mt-1">
                ⚠ Passwords do not match
            </p>
            <!-- Success message when passwords match -->
            <p v-if="form.newPassword && form.confirmPassword &&
                     form.newPassword.length >= 6 && form.confirmPassword.length >= 6 &&
                     form.newPassword === form.confirmPassword"
               class="text-green-600 text-xs mt-1">
                ✓ Passwords match
            </p>
            <!-- Error messages only shown during validation -->
            <p v-if="showValidation && form.confirmPassword && form.confirmPassword.length < 6"
               class="text-red-500 text-xs mt-1">
                Password confirmation must be at least 6 characters long
            </p>
            <p v-if="showValidation && form.newPassword && form.confirmPassword &&
                     form.newPassword.length >= 6 && form.confirmPassword.length >= 6 &&
                     form.newPassword !== form.confirmPassword"
               class="text-red-500 text-xs mt-1">
                Passwords must match
            </p>
        </div>

        <!-- Buttons -->
        <div class="flex justify-between">
            <button type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300"
                @click="onCancel">
                Cancel
            </button>
            <button type="button"
                :disabled="!isFormValid"
                :class="[
                    'inline-flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
                    isFormValid
                        ? 'text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500'
                        : 'text-gray-400 bg-gray-300 cursor-not-allowed'
                ]"
                @click="onSaveChanges">
                Save Changes
            </button>
        </div>
    </div>
</template>

<script>
    import authApi from '@/api/auth'
    import TInput from '@/components/form/Input.vue'

    export default {
        components: {
            TInput
        },
        data() {
            return {
                form: {
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                },
                showValidation: false // Control when validation should be shown
            };
        },
        computed: {
            isFormValid() {
                return this.form.currentPassword &&
                       this.form.currentPassword.trim() !== '' &&
                       this.form.newPassword &&
                       this.form.newPassword.length >= 6 &&
                       this.form.confirmPassword &&
                       this.form.confirmPassword.length >= 6 &&
                       this.form.newPassword === this.form.confirmPassword;
            }
        },
        methods: {
            onCancel() {
                // Clear form or navigate away, depending on your logic
                this.form.currentPassword = '';
                this.form.newPassword = '';
                this.form.confirmPassword = '';
                this.showValidation = false; // Reset validation flag
            },
            onSaveChanges() {
                // Enable validation when user tries to submit
                this.showValidation = true;

                // Check if form is valid
                if (!this.isFormValid) {
                    if (!this.form.currentPassword || !this.form.newPassword || !this.form.confirmPassword) {
                        this.__showNotif('error', 'Error', 'Please fill in all required fields.');
                        return;
                    }
                    if (this.form.newPassword.length < 6) {
                        this.__showNotif('error', 'Error', 'New password must be at least 6 characters long.');
                        return;
                    }
                    if (this.form.confirmPassword.length < 6) {
                        this.__showNotif('error', 'Error', 'Password confirmation must be at least 6 characters long.');
                        return;
                    }
                    if (this.form.newPassword !== this.form.confirmPassword) {
                        this.__showNotif('error', 'Error', 'New passwords do not match.');
                        return;
                    }
                    return;
                }
                // Further logic like sending the data to an API...
                const callback = (response) => {
                    console.log(response.data)
                    this.onCancel() // This will reset form and validation flag
                    const message = response.message;
                    this.__showNotif('success', 'Success', message);
                }
                const errCallback = (err) => {
                    const message = err.response.data.message;
                    this.__showNotif('error', 'error', message);
                }

                const params = {
                    oldPassword: this.form.currentPassword,
                    newPassword: this.form.newPassword,
                    confirmPassword: this.form.confirmPassword
                }
                authApi.changePassword(params, callback, errCallback)
            }
        }
    };
</script>

<style scoped>
    /* Customize styling if necessary */
</style>