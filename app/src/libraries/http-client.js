// http-client.js
import axios from 'axios';

const url = import.meta.env.VITE_API_URL;
let token = localStorage.getItem('access_token');
let locale = localStorage.getItem('locale');

const globalResponseHandler = response => response;

const globalErrorHandler = (error) => {
	const { status } = error.response;
	const isTokenExpired = status === 401;
	const isUnauthorized = status === 403;
	if (isTokenExpired) {
		localStorage.removeItem('access_token');
		localStorage.removeItem('id_token');
		localStorage.removeItem('expires_at');

		const originalRequest = error.config;
		delete originalRequest.headers.Authorization;
		window.location.href = '/login';
	} else if (isUnauthorized) {
		// localStorage.removeItem('access_token');
		window.location.href = '/';
	}
	return Promise.reject(error);
};

const client = axios.create({
	baseURL: url,
	headers: {
		Authorization: `Bearer ${token}`,
		'Accept-Language': locale ?? 'en',
	},
});

// Add a response interceptor
client.interceptors.response.use(globalResponseHandler, globalErrorHandler);

// Function to update Accept-Language header
const updateLocale = (newLocale) => {
	locale = newLocale;
	client.defaults.headers['Accept-Language'] = newLocale;
};

export { client, updateLocale };
