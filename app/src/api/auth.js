import BaseApi from './BaseApi';

class AuthApi extends BaseApi {
	constructor() {
		super('/auth');
	}

	// Login
	login(creds, cb, errorCb) {
		this.customCall('post', 'login', creds, cb, errorCb);
	}

	// Forgot Password
	forgot(emailUser, cb, errorCb) {
		const params = { email: emailUser };
		this.customCall('post', 'forgotPassword', params, cb, errorCb);
	}

	// Reset Password
	reset(params, cb, errorCb) {
		this.customCall('post', 'resetPassword', params, cb, errorCb);
	}

	// Register
	register(params, cb, errorCb) {
		this.customCall('post', 'register', params, cb, errorCb);
	}

	// Verify
	verify(tokenUser, cb, errorCb) {
		const params = { token: tokenUser };
		this.customCall('post', 'verification', params, cb, errorCb);
	}
	// Resend verification email
	resend(emailUser, cb, errorCb) {
		const params = { email: emailUser };
		this.customCall('post', 'resendToken', params, cb, errorCb);
	}

	// Login with social media
	loginSocialMedia(params, cb, errorCb) {
		this.customCall('post', 'loginSocialMedia', params, cb, errorCb);
	}

	// Get profile
	getProfile(cb, errorCb) {
		this.customCall('get', 'me', {}, cb, errorCb);
	}

	// Update profile
	update(params, cb, errorCb) {
		this.customCall('put', 'update', params, cb, errorCb);
	}

	// Change password
	changePassword(params, cb, errorCb) {
		this.customCall('put', 'changePassword', params, cb, errorCb);
	}
}

export default new AuthApi();
