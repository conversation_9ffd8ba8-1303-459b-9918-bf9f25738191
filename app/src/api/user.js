import BaseApi from './BaseApi';

class UserApi extends BaseApi {
	constructor() {
		super('/users');
	}
	// Custom methods using BaseApi's customCall
	addUserExpertise(params, cb, errorCb) {
		this.customCall('post', 'expertise', params, cb, errorCb);
	}

	addUserService(params, cb, errorCb) {
		this.customCall('post', 'service', params, cb, errorCb);
	}

	removeUserExpertise(id, cb, errorCb) {
		this.customCall('delete', `expertise/${id}`, {}, cb, errorCb);
	}

	removeUserService(id, cb, errorCb) {
		this.customCall('delete', `service/${id}`, {}, cb, errorCb);
	}
	updateUserServiceOrder(params, cb, errorCb) {
		this.customCall('put', 'service/order', params, cb, errorCb);
	}

	updateExpertiseOrder(params, cb, errorCb) {
		this.customCall('put', 'expertise/order', params, cb, errorCb);
	}

	// Note: update, get, delete methods are inherited from BaseApi

	// Permanent delete
	deletePermanent(id, cb, errorCb) {
		this.customCall('delete', `permanent/${id}`, {}, cb, errorCb);
	}

	// Delete forever (duplicate of deletePermanent - should be removed)
	deleteForever(id, cb, errorCb) {
		this.customCall('delete', `permanent/${id}`, {}, cb, errorCb);
	}

	// Bulk Delete - custom implementation needed
	bulkDelete(ids, cb, errorCb) {
		const params = {
			user_ids: JSON.stringify(ids),
			is_deleted: 1,
		};
		const { client } = require('@/libraries/http-client');
		client.delete(this.endpoint, { data: params })
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	// Get User Profile
	getProfile(cb, errorCb) {
		this.customCall('get', 'me', {}, cb, errorCb);
	}

	// Update User Profile
	updateProfile(user, cb, errorCb) {
		const { client } = require('@/libraries/http-client');
		client.put(this.endpoint, user)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	// Generate API token
	generate(cb, errorCb) {
		this.customCall('post', 'apiToken/generate', {}, cb, errorCb);
	}

	// Revoke API token
	revoke(cb, errorCb) {
		this.customCall('post', 'apiToken/revoke', {}, cb, errorCb);
	}

	// Media social disconnect
	mediaSocial(params, cb, errorCb) {
		this.customCall('post', 'mediaSocial/disconnect', params, cb, errorCb);
	}

	// Test connection
	testConnection(params, cb, errorCb) {
		this.customCall('post', 'mediaSocial/post', params, cb, errorCb);
	}
}

export default new UserApi();
