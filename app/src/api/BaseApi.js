import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';

/**
 * Base API class that provides common CRUD operations
 * This eliminates code duplication across all API files
 */
export default class BaseApi {
	constructor(endpoint) {
		this.endpoint = endpoint;
	}

	/**
	 * Generic response handler
	 * @param {Function} cb - Success callback
	 * @returns {Function} Response handler function
	 */
	createResponseHandler(cb) {
		return (response) => {
			if (cb) cb(response.data);
		};
	}

	/**
	 * Generic error handler
	 * @param {Function} errorCb - Error callback
	 * @returns {Function} Error handler function
	 */
	createErrorHandler(errorCb) {
		return (error) => {
			if (errorCb) errorCb(error);
		};
	}

	/**
	 * Get list of items with optional query parameters
	 * @param {Object} params - Query parameters
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	getList(params = {}, cb, errorCb) {
		const query = buildQuery(params);
		const url = `${this.endpoint}?${query}`;
		
		client.get(url)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	/**
	 * Get single item by ID
	 * @param {string|number} id - Item ID
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	get(id, cb, errorCb) {
		const url = `${this.endpoint}/${id}`;
		
		client.get(url)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	/**
	 * Create new item
	 * @param {Object} params - Item data
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	create(params, cb, errorCb) {
		client.post(this.endpoint, params)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	/**
	 * Update existing item
	 * @param {string|number} id - Item ID
	 * @param {Object} params - Updated data
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	update(id, params, cb, errorCb) {
		const url = `${this.endpoint}/${id}`;
		
		client.put(url, params)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	/**
	 * Delete item
	 * @param {string|number} id - Item ID
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	delete(id, cb, errorCb) {
		const url = `${this.endpoint}/${id}`;
		
		client.delete(url)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	/**
	 * Get item by slug
	 * @param {string} slug - Item slug
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	getBySlug(slug, cb, errorCb) {
		const url = `${this.endpoint}/slug/${slug}`;
		
		client.get(url)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	/**
	 * Custom endpoint call
	 * @param {string} method - HTTP method (get, post, put, delete)
	 * @param {string} path - Additional path to append to endpoint
	 * @param {Object} params - Request parameters/data
	 * @param {Function} cb - Success callback
	 * @param {Function} errorCb - Error callback
	 */
	customCall(method, path = '', params = {}, cb, errorCb) {
		const url = path ? `${this.endpoint}/${path}` : this.endpoint;
		
		let request;
		switch (method.toLowerCase()) {
			case 'get':
				const query = buildQuery(params);
				request = client.get(`${url}?${query}`);
				break;
			case 'post':
				request = client.post(url, params);
				break;
			case 'put':
				request = client.put(url, params);
				break;
			case 'delete':
				request = client.delete(url, { data: params });
				break;
			default:
				throw new Error(`Unsupported HTTP method: ${method}`);
		}

		request
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}
}
