import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';

const endpoint = '/projects';
const getProjectEndpoint = '/projects';
export default {
	// Get List
	getList(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const query = buildQuery(params);
		const url = `${endpoint}?${query}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	getListStarred(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const query = buildQuery(params);
		const url = `${endpoint}/starred?${query}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	getSchedule(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const query = buildQuery(params);
		const url = `/studio/schedules?${query}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// Create
	create(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// Note: User access is now handled through the main project CRUD operations
	// using the user_access_ids field in create/update requests

	// Get project users
	getProjectUsers(projectId, params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const query = buildQuery(params);
		const url = `${endpoint}/${projectId}/users?${query}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},
	sendQuote(id, status, params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/quotation/${status}/${id}`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},
	changeEditableQuotation(id, cb, errorCb) {
		const url = `${endpoint}/${id}`;
		client.put(url, params)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// Update
	update(id, params, cb, errorCb) {
		const url = `${endpoint}/${id}`;
		client.put(url, params)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

  // Update
	starred(id, cb, errorCb) {
		const url = `${endpoint}/starred/${id}`;
		client.post(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

	removeStarred(id, cb, errorCb) {
		const url = `${endpoint}/starred/${id}`;
		client.delete(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

	// Get project by id
	get(id, cb, errorCb) {
		const url = `${endpoint}/${id}`;
		client.get(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},
	// Get details
	getDetailProject(id, cb, errorCb) {
		const url = `${getProjectEndpoint}/${id}`;
		client.get(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},
	// Get details by slug
	getBySlug(slug, cb, errorCb) {
		const url = `${endpoint}/slug/${slug}`;
		client.get(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},

	// Delete
	delete(id, cb, errorCb) {
		const url = `${endpoint}/${id}`;
		client.delete(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},

  deleteCollab(id, params, cb, errorCb) {
    const url = `${endpoint}/collaborators/${id}`;
    client.delete(url, { data: params }) // Fix: pass params inside { data: ... }
        .then((response) => {
            cb(response.data); // Properly handle the response
        })
        .catch((e) => {
            if (errorCb) errorCb(e); // Error callback if there's an error
        });
  },

	// reorder
	reorder(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/reorder`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},
	// reorder
	duplicate(id, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/duplicate/${id}`;
		client.post(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// Export
	export(id, cb, errorCb) {
		const url = `${endpoint}/export/${id}`;
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},
	// Import
	import(params, cb, errorCb) {
		const url = `${endpoint}/import`;
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},

  // collaborators
  getCollaborators(id, cb, errorCb) {
		const url = `${endpoint}/collaborators/${id}`;
		client.get(url)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},
};
