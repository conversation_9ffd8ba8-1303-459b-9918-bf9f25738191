import BaseApi from './BaseApi';
import axios from 'axios';
import { client } from '@/libraries/http-client';

const CancelToken = axios.CancelToken;

class FilesApi extends BaseApi {
	constructor() {
		super('/uploads');
	}

	/**
	 * File Upload with progress and cancel support
	 * @param {Object} params Params
	 * @param {File} params.fileUpload File to be uploaded
	 * @param {Function} cb Success callback
	 * @param {Function} errorCb Error callback
	 * @param {Function} progressCallback Progress callback
	 * @param {Function} cancelCallback Cancel callback
	 */
	upload(params, cb, errorCb, progressCallback, cancelCallback) {
		const config = {
			onUploadProgress: (progressEvent) => {
				const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
				if (progressCallback) progressCallback(percentCompleted);
			},
			cancelToken: new CancelToken((c) => {
				if (cancelCallback) cancelCallback(c);
			}),
		};

		client.post(this.endpoint, params, config)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}
	uploadVideo(params, cb, errorCb, progressCallback, cancelCallback) {
		const config = {
			onUploadProgress: (progressEvent) => {
				const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
				if (progressCallback) progressCallback(percentCompleted);
			},
			cancelToken: new CancelToken((c) => {
				if (cancelCallback) cancelCallback(c);
			}),
		};

		client.post('/uploadVideo', params, config)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	// Override delete method for custom endpoint
	delete(params, cb, errorCb) {
		const url = `/v1/file/remove`;
		client.delete(url, { data: params })
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}

	// Upload with background removal
	uploadRemoveBG(params, cb, errorCb, progressCallback, cancelCallback) {
		const config = {
			responseType: 'blob',
			onUploadProgress: (progressEvent) => {
				const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
				if (progressCallback) progressCallback(percentCompleted);
			},
			cancelToken: new CancelToken((c) => {
				if (cancelCallback) cancelCallback(c);
			}),
		};

		// External service URL
		const url = 'https://rembg.bannerbite.com/';
		client.post(url, params, config)
			.then(this.createResponseHandler(cb))
			.catch(this.createErrorHandler(errorCb));
	}
}

export default new FilesApi();