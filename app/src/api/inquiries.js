import BaseApi from './BaseApi';

class InquiriesApi extends BaseApi {
	constructor() {
		super('/inquiries');
	}

	// Custom method for updating inquiry assignment
	updateInquiry(id, params, cb, errorCb) {
		this.customCall('put', `${id}/assign`, params, cb, errorCb);
	}

	// Custom method for completing inquiry
	completeInquiry(id, params, cb, errorCb) {
		this.customCall('put', `${id}/completed`, params, cb, errorCb);
	}

	// Custom method for reordering inquiries
	reorder(params, cb, errorCb) {
		this.customCall('put', 'reorder', params, cb, errorCb);
	}

	// Custom method for adding collaborator
	addCollaborator(id, params, cb, errorCb) {
		this.customCall('post', `${id}/collaborator`, params, cb, errorCb);
	}

	// Custom method for removing collaborator
	removeCollaborator(id, params, cb, errorCb) {
		this.customCall('delete', `${id}/collaborator`, params, cb, errorCb);
	}
}

export default new InquiriesApi();
