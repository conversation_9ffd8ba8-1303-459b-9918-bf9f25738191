// import createPersistedState from "vuex-persistedstate";
import {
	useWindowSize
} from "@vueuse/core";

const {
	width,
	height
} = useWindowSize();
import inquiriesApi from "@/api/inquiries";

export default {
	namespaced: true,
	// plugins: [createPersistedState()]
	state: () => ({
		projectSelected: null,
		onlineUsers: {},
		version: "1.0.00",
		activeAddInquiryColumnIndex: null,
		wWidth: width,
		wHeight: height,
		data: null,
		hideAside: false,
		lightMode: true,
		isModal: false,
		mutate: false,
		project: null,
		starredProject: null,
		board: {
			active: "", // name of the active board
			activeIndex: 0,
			add: false,
			edit: false,
			delete: false,
		},
		inquiry: {
			id: null,
			title: '',
			projectName: '',
			assign: '',
			creator: null,
			assign_to: '',
			startDate: '',
			due_date: '',
			parent_id: '',
			updated_at: '',
			description: '',
			created_at: '',
			status: '',
			index: '',
			type: '',
      meta: null,
			sub_inquiries: [],
			collaborator: [],
			comments: [],
			active: "", // name of the active inquiry (in case of edit)
			activeIndex: -1, // index of active inquiry
			columnIndex: -1, // index of column of activeInquiry
			status: "", // name of the column (when add / edit inquiry) to which the inquiry belongs
			add: false,
			addDirectly: false,
			edit: false,
			delete: false,
			show: undefined, // holds the inquiry object, in inquiryShow
      slug: null,
		}
	}),
	mutations: {
		setOnlineUsers(state, items) {
			state.onlineUsers = items;
		},
		setData(state, d) {
			state.data = d.boards;
			if (JSON.stringify(state.data) != "{}") {
				state.board.active = state.data[0].name;
			}
		},
		setDataProject(state, project) {
			if (project) state.project = project;
		},
		setDatastarredProject(state, projects) {
			if (projects) state.starredProject = projects;
		},
		resetDataProject(state) {
			state.project = null;
		},
		showDetailInquiry(state, slug) {
			if (!slug) return;
			const callback = (response) => {
				const data = response.data;
				state.inquiry = data
				state.inquiry.active = data.id;
				state.inquiry.status = data.status;
				state.inquiry.projectName = data.project.name,
				state.inquiry.type = data.type;
				state.inquiry.addDirectly = false;
				state.inquiry.edit = true;
				state.inquiry.add = false;
				state.mutate = true;
			};
			const errCallback = (err) => {
				console.error(err);
			};
			inquiriesApi.get(slug, callback, errCallback);
		},
		changeStatus(state, colIndex) {
			let inquiry = state.data[state.board.activeIndex].columns[
				state.inquiry.columnIndex
			].inquiries.splice(state.inquiry.activeIndex, 1);

			// push it in the new status column
			state.data[state.board.activeIndex].columns[colIndex].inquiries.push(inquiry[0]);
		},
		updateInquiryInStore(state, inquiry) {
      console.log('🔄 Store: Updating inquiry in store:', inquiry);

      // Safety check for state.data and board
      if (!state.data || !state.data[state.board.activeIndex] || !state.data[state.board.activeIndex].columns) {
        console.error('Invalid state data structure:', state.data);
        return;
      }

      // IMPORTANT: First, remove the inquiry from ALL columns to prevent duplication
      state.data[state.board.activeIndex].columns.forEach(col => {
        if (!col || !col.inquiries) return; // Safety check

        const existingIndex = col.inquiries.findIndex(t => t && t.id === inquiry.id);
        if (existingIndex !== -1) {
          col.inquiries.splice(existingIndex, 1);
          console.log(`✅ Removed inquiry from column: ${col.name || col.title}`);
        }
      });

      // Find the target column (check both name and title)
      const targetColumn = state.data[state.board.activeIndex].columns.find(col =>
        (col.name === inquiry.type || col.title === inquiry.type) && col.inquiries
      );

      if (!targetColumn) {
        console.warn('Target column not found for inquiry type:', inquiry.type);
        return;
      }

      // Add to target column (we already removed it from all columns)
      if (inquiry.position === 'top') {
        targetColumn.inquiries.unshift(inquiry); // Add new inquiry at the beginning
      } else {
        targetColumn.inquiries.push(inquiry); // Add new inquiry at the end
      }

      console.log(`✅ Added inquiry to target column: ${targetColumn.name || targetColumn.title}`);
    },
  
		updateInquiryInStore2(state, inquiry) {
			console.log('🔄 Store2: Updating inquiry in place:', inquiry);

			// Safety check for state.data and board
			if (!state.data || !state.data[state.board.activeIndex] || !state.data[state.board.activeIndex].columns) {
				console.error('Invalid state data structure:', state.data);
				return;
			}

			// Find the current column where the inquiry exists
			let currentColumn = null;
			let currentIndex = -1;

			for (const col of state.data[state.board.activeIndex].columns) {
				if (!col || !col.inquiries) continue;
				const index = col.inquiries.findIndex(t => t && t.id === inquiry.id);
				if (index !== -1) {
					currentColumn = col;
					currentIndex = index;
					break;
				}
			}

			if (!currentColumn || currentIndex === -1) {
				console.warn('Inquiry not found in any column:', inquiry.id);
				return;
			}

			// Check if the inquiry type has changed (needs to move to different column)
			const targetColumn = state.data[state.board.activeIndex].columns.find(col =>
				col.name === inquiry.type || col.title === inquiry.type
			);

			if (!targetColumn) {
				console.warn('Target column not found for inquiry type:', inquiry.type);
				return;
			}

			// If the inquiry is staying in the same column, just update in place
			if (currentColumn === targetColumn) {
				console.log('✅ Updating inquiry in same column, preserving position');
				Object.assign(currentColumn.inquiries[currentIndex], inquiry);
			} else {
				// If moving to different column, remove from current and add to target
				console.log('🔄 Moving inquiry to different column');
				const inquiryToMove = currentColumn.inquiries.splice(currentIndex, 1)[0];
				const updatedInquiry = { ...inquiryToMove, ...inquiry };

				// Add to target column (preserve order by adding at end)
				targetColumn.inquiries.push(updatedInquiry);
			}

			console.log('✅ Inquiry updated successfully');
		},

    deleteInquiryInStore(state, inquiry) {
      const column = state.data[state.board.activeIndex].columns.find(col => col.name === inquiry.type);
      const inquiryIndex = column.inquiries.findIndex(t => t.id === inquiry.id);
      if (inquiryIndex !== -1) {
          column.inquiries.splice(inquiryIndex, 1); // Remove inquiry if found
      }
    },

    updateOneInquiryInStoreSocket(state, inquiry) {
      console.log('🔄 Socket: Updating single inquiry in store:', inquiry);

      // Safety check for state.data and board
      if (!state.data || !state.data[state.board.activeIndex] || !state.data[state.board.activeIndex].columns) {
        console.error('Invalid state data structure for socket update:', state.data);
        return;
      }

      // IMPORTANT: First, remove the inquiry from ALL columns to prevent duplication
      state.data[state.board.activeIndex].columns.forEach(col => {
        if (!col || !col.inquiries) return; // Safety check

        const existingIndex = col.inquiries.findIndex(t => t && t.id === inquiry.id);
        if (existingIndex !== -1) {
          col.inquiries.splice(existingIndex, 1);
          console.log(`✅ Removed inquiry from column via socket: ${col.name || col.title}`);
        }
      });

      // Find target column by name or title
      const targetColumn = state.data[state.board.activeIndex].columns.find(col =>
        (col.name === inquiry.type || col.title === inquiry.type) && col.inquiries
      );

      if (!targetColumn) {
        console.warn('Target column not found for inquiry type:', inquiry.type);
        return;
      }

      // Add inquiry to target column
      targetColumn.inquiries.push(inquiry);
      console.log('✅ Inquiry added to target column via socket:', targetColumn.name || targetColumn.title);
    },

    updateInquiryInStoreSocket(state, inquiry) {
      console.log('🔄 Socket: Updating inquiry in store with reorder:', inquiry);

      // Find the new column where the inquiry is supposed to go (check both name and title)
      const newColumn = state.data[state.board.activeIndex].columns.find(col =>
        col.name === inquiry?.inquiry?.type || col.title === inquiry?.inquiry?.type
      );

      console.log('Target column for inquiry:', newColumn);

      // If newColumn exists, update or add the inquiry to the new column
      if (newColumn) {
        const newInquiryIndex = newColumn.inquiries.findIndex(t => t.id === inquiry?.inquiry?.id);

        // If the inquiry already exists in the new column, update it and adjust its position
        if (newInquiryIndex !== -1) {
          // Update the inquiry details
          Object.assign(newColumn.inquiries[newInquiryIndex], inquiry?.inquiry);
          console.log('✅ Updated existing inquiry in new column');

          // If the inquiry is reordered within the same column, update its position
          newColumn.inquiries.splice(newInquiryIndex, 1); // Remove inquiry from current position
          if (inquiry?.inquiry.position === 'top') {
            newColumn.inquiries.unshift(inquiry?.inquiry); // Add to the top
          } else {
            newColumn.inquiries.push(inquiry?.inquiry); // Add to the bottom
          }
        } else {
          // If inquiry does not exist in the column, add it to the appropriate position
          if (inquiry?.inquiry.position === 'top') {
            newColumn.inquiries.unshift(inquiry?.inquiry); // Add to the top
          } else {
            newColumn.inquiries.push(inquiry?.inquiry); // Add to the bottom
          }
          console.log('✅ Added new inquiry to column');
        }
      } else {
        console.warn('New column not found for inquiry type:', inquiry?.inquiry?.type);
      }

      // Find the old column where the inquiry was before it was moved (only if it exists)
      if (inquiry?.oldInquiry?.type !== inquiry?.inquiry?.type) { // Check if inquiry moved to a different column
        const oldColumn = state.data[state.board.activeIndex].columns.find(col =>
          col.name === inquiry?.oldInquiry?.type || col.title === inquiry?.oldInquiry?.type
        );

        // If oldColumn exists, find and remove the inquiry from the old column
        if (oldColumn) {
          const oldInquiryIndex = oldColumn.inquiries.findIndex(t => t.id === inquiry?.oldInquiry?.id);
          if (oldInquiryIndex !== -1) {
            oldColumn.inquiries.splice(oldInquiryIndex, 1); // Remove the old inquiry
            console.log('✅ Removed inquiry from old column');
          }
        } else {
          console.warn('Old column not found for inquiry type:', inquiry?.oldInquiry?.type);
        }
      }
    },
    

		resetStore(state) {
			state.data = null;
			state.hideAside = false;
			state.lightMode = true;
			state.isModal = false;
			state.mutate = false;
			state.board.active = "";
			state.board.activeIndex = 0;
			state.board.add = false;
			state.board.edit = false;
			state.board.delete = false;
			state.inquiry = {
				id: null,
				title: '',
				assign: '',
				creator: null,
				assign_to: '',
				startDate: '',
				due_date: '',
				parent_id: '',
				updated_at: '',
				description: '',
				created_at: '',
				status: '',
				index: '',
				type: '',
				sub_inquiries: [],
				collaborator: [],
				comments: [],
				active: "",
				activeIndex: -1,
				columnIndex: -1,
				status: "",
				add: false,
				addDirectly: false,
				edit: false,
				delete: false,
				show: undefined,
			};
		},
		resetStoreInquiry(state) {
			state.inquiry = {
				id: null,
				title: '',
				assign: '',
				creator: null,
				assign_to: '',
				startDate: '',
				due_date: '',
				parent_id: '',
				updated_at: '',
				description: '',
				created_at: '',
				status: '',
				index: '',
				type: '',
				sub_inquiries: [],
				collaborator: [],
				comments: [],
				active: "",
				activeIndex: -1,
				columnIndex: -1,
				status: "",
				add: false,
				addDirectly: false,
				edit: false,
				delete: false,
				show: undefined,
			};
		}
	},
	actions: {
		clearOnlineUsers({ commit }) {
			commit("setOnlineListManually", {});
		},
		setData({ commit }, d) {
			commit('setData', d);
		},
		setDataProject({ commit }, project) {
			commit('setDataProject', project);
		},
		setDatastarredProject({ commit }, projects) {
			commit('setDatastarredProject', projects);
		},
		changeStatus({ commit }, colIndex) {
			commit('changeStatus', colIndex);
		},
		updateInquiryInStore({ commit }, inquiry) {
			commit('updateInquiryInStore', inquiry);
		},
		updateInquiryInStore2({ commit }, inquiry) {
			commit('updateInquiryInStore2', inquiry);
		},
    updateInquiryInStoreSocket({ commit }, inquiry) {
			commit('updateInquiryInStoreSocket', inquiry);
		},
    updateOneInquiryInStoreSocket({ commit }, inquiry) {
			commit('updateOneInquiryInStoreSocket', inquiry);
		},
    deleteInquiryInStore({ commit }, inquiry) {
			commit('deleteInquiryInStore', inquiry);
		},
		resetStore({ commit }) {
			commit('resetStore');
		},
		resetStoreInquiry({ commit }) {
			commit('resetStoreInquiry');
		},
		resetDataProject({ commit }) {
			commit('resetDataProject');
		},
    showDetailInquiry({ commit }, id) {
			commit('showDetailInquiry', id);
		},
	},
	getters: {
		getOnlineUsers(state) {
			return state.onlineUsers;
		},
		getActiveProject: (state) => {
			return state.project;
		},
		getStarredProject: (state) => {
			return state.starredProject;
		},
		getActiveBoard: (state) => {
			if (state.data) {
				return state.data.find((el, index) => {
					if (el.name === state.board.active) {
						state.board.activeIndex = index;
						return el;
					}
				});
			}
		},
		getActiveColumn: (state, getters) => {
			if (state.data) {
				return getters.getActiveBoard.columns.find((el) => {
					if (el.name === state.inquiry.type) {
						return el;
					}
				});
			}
		},
		getActiveInquiry: (state, getters) => {
      if (state.data) {
        if (getters.getActiveBoard.columns) {
          // Loop through all the columns in the active board
          for (const column of getters.getActiveBoard.columns) {
            // Search for the active inquiry within the inquiries array of each column
            const inquiry = column.inquiries.find((el, idx) => {
              if (el.id === state.inquiry.active) {
                state.inquiry.activeIndex = idx; // Store the index of the active inquiry
                state.inquiry.columnIndex = getters.getActiveBoard.columns.indexOf(column); // Store the index of the column
                return el;
              }
            });

            // If the inquiry is found, return it
            if (inquiry) {
              return inquiry;
            }
          }
        }
      } else {
        // my inquiry
        return state.inquiry
      }

      // Return null if no inquiry is found
      return null;
    },
		getBoardColsLength: (state, getters) => {
			if (state.data && state.data.length > 0) {
				return getters.getActiveBoard.columns.length;
			}
			return 0;
		}

	},
};