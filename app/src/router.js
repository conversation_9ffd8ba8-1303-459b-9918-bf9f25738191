import { createRouter, createWebHistory } from 'vue-router';

// Routes
const routes = [
	{ name: 'Login', path: '/login', component: () => import('@/pages/auth/Login.vue'), meta: { title: 'Login', layout: 'auth' }, },

	{ name: 'Logout', path: '/logout', component: () => import('@/pages/auth/Logout.vue'), meta: { title: 'Logout', layout: 'auth' }, },
	{ name: 'ForgotPassword', path: '/forgot-password', component: () => import('@/pages/auth/ForgotPassword.vue'), meta: { title: 'Forgot Password', layout: 'auth' }, },
	{ name: 'ResetPassword', path: '/user/resetPassword', component: () => import('@/pages/auth/ResetPassword.vue'), meta: { title: 'Reset Password', layout: 'auth' }, },
	{ name: 'Register', path: '/register', component: () => import('@/pages/auth/Register.vue'), meta: { title: 'Register', layout: 'auth' }, },
	{ name: 'Resend Email', path: '/resend-email', component: () => import('@/pages/auth/ResendEmail.vue'), meta: { title: 'Resend Email', layout: 'auth' }, },
	{ name: 'Verify', path: '/user/verify', component: () => import('@/pages/auth/Verify.vue'), meta: { title: 'Verify', layout: 'auth' }, },
	{ name: 'MagicLink', path: '/magic-link', component: () => import('@/pages/auth/MagicLink.vue'), meta: { title: 'Magic Link', layout: 'auth' }, },
	{ name: 'RequestInvite', path: '/request-invite', component: () => import('@/pages/auth/RequestInvite.vue'), meta: { title: 'Request Invite', layout: 'auth' }, },
	{ name: 'Home', path: '/home', component: () => import('@/pages/home/<USER>'), meta: { title: 'Home', layout: 'main' }, },
	{ name: 'Users', path: '/admin/users', component: () => import('@/pages/admin/User.vue'), meta: { title: 'Users', layout: 'main' }, },
	{ name: 'FeedPosts', path: '/admin/feed-posts', component: () => import('@/pages/admin/FeedPost.vue'), meta: { title: 'Feed Posts', layout: 'main' }, },
	{ name: 'MyInquiry', path: '/inquiries/:id', component: () => import('@/pages/inquiry/Index.vue'), meta: { title: 'Inquiry', layout: 'main' }, },
	{ name: 'InquiryDetail', path: '/inquiries/:id/:slug', component: () => import('@/pages/inquiry/Index.vue'), meta: { title: 'Inquiry', layout: 'main' }, },
	{ name: 'InquiryDetailFull', path: '/inquiries/:id/:slug/f', component: () => import('@/pages/inquiry/Index.vue'), meta: { title: 'Inquiry', layout: 'main' }, },
	{ name: 'Inbox', path: '/inbox', component: () => import('@/pages/inbox/Index.vue'), meta: { title: 'Inbox', layout: 'main' }, },
	{ name: 'UserRole', path: '/admin/role', component: () => import('@/pages/admin/UserRole.vue'), meta: { title: 'Manage Role', layout: 'main' }, },
	{ name: 'Category', path: '/admin/category', component: () => import('@/pages/admin/Category.vue'), meta: { title: 'Manage Category', layout: 'main' }, },

	{ name: 'Dashboard', path: '/', component: () => import('@/pages/home/<USER>'), meta: { title: 'Dashboard', layout: 'main' }, },


	{ name: 'ChangePassword', path: '/settings/change-password', component: () => import('@/pages/profile/ChangePassword.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'AccountProfile', path: '/settings/profile', component: () => import('@/pages/profile/AccountProfile.vue'), meta: { title: 'Settings ', layout: 'main' }, },

	{ name: 'InquiryAlone', path: '/e/:id/:slug', component: () => import('@/pages/kanban/InquiryAlone.vue'), meta: { title: 'Inquiry Detail', layout: 'main' }, },
	{ name: 'InquiryAloneFull', path: '/e/:id/:slug/f', component: () => import('@/pages/kanban/InquiryAlone.vue'), meta: { title: 'Inquiry Detail', layout: 'main' }, },
	{ name: 'EventKanban', path: '/e/kanban/:id', component: () => import('@/pages/kanban/Index.vue'), meta: { title: 'Event Kanban', layout: 'main' }, },
	{ name: 'InquiryDetailKanban', path: '/e/kanban/:id/:slug', component: () => import('@/pages/kanban/Index.vue'), meta: { title: 'Event Kanban', layout: 'main' }, },
	{ name: 'InquiryDetailKanbanFull', path: '/e/kanban/:id/:slug/f', component: () => import('@/pages/kanban/Index.vue'), meta: { title: 'Event Kanban', layout: 'main' }, },
	{ name: 'EventOverview', path: '/e/overview/:id', component: () => import('@/pages/overview/Index.vue'), meta: { title: 'Event Overview', layout: 'main' }, },
	{ name: 'EventReport', path: '/e/report/:id', component: () => import('@/pages/report/Index.vue'), meta: { title: 'Event Report', layout: 'main' }, },
	{ name: 'EventFiles', path: '/e/files/:id', component: () => import('@/pages/files/Index.vue'), meta: { title: 'Event Files', layout: 'main' }, },
	{ name: 'EventProjects', path: '/projects', component: () => import('@/pages/projects/Index.vue'), meta: { title: 'Manage Projects', layout: 'main' }, },

	// Assistant routes - more specific routes MUST come before general ones
	{ name: 'AssistantProject', path: '/project/assistant/:projectid', component: () => import('@/pages/assistant/client/NotebookView.vue'), meta: { title: 'Project Assistant', layout: 'main' }, props: true },
	{ name: 'AssistantNotebook', path: '/assistant/notebook/:notebookid', component: () => import('@/pages/assistant/components/NotebookView.vue'), meta: { title: 'Notebook', layout: 'main' }, props: true },
	{ name: 'AnswerFlow', path: '/assistant', component: () => import('@/pages/assistant/Index.vue'), meta: { title: 'AnswerFlow', layout: 'main' }, },

	{ name: 'NotFound', path: '/:path(.*)', component: () => import('@/pages/errors/NotFound.vue'), meta: { title: 'Not Found', layout: 'auth' }, },

];

// Middleware here
import vueMiddleware from "@grafikri/vue-middleware";
import authMiddleware from '@/middleware/auth.js';
// import { HSStaticMethods } from 'preline';
import { nextTick } from 'vue';
const middleware = [authMiddleware];

// Apply middleware.
for (var r in routes) {
	routes[r].meta.middleware = middleware;
}

const history = createWebHistory();

export default {
	createRouter(_store) {
		let router = createRouter({
			history: history,
			scrollBehavior() {
				return { x: 0, y: 0 };
			},
			routes
		});
		router.afterEach(() => {
			// Router navigation completed successfully
			// HSStaticMethods.autoInit() can be called here if needed
		});
		router.beforeEach(vueMiddleware({ store: _store }));
		return router;
	}
};