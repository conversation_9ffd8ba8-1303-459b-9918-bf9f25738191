export const DUMMY_USERS = [
	{
		name: '<PERSON>',
		email: 'emily<PERSON><PERSON>@example.com',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: '<PERSON>',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: '<PERSON>',
		email: 'emily<PERSON><PERSON>@example.com',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: 'Emily Selman',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: 'Emily Selman',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: 'Emily Selman',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: 'Emily Selman',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: 'Emily Selman',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
	{
		name: 'Emily Selman',
		email: '<EMAIL>',
		imageUrl:
		'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
		applied: 'January 7, 2020',
		appliedDatetime: '2020-07-01T15:34:56',
		status: 'Completed phone screening',
	},
];

export const FILTER_TABS = [
	{ id: 'all', name: 'All', href: '#', current: false },
	{ id: 'client', name: 'Client', href: '#', current: false },
	{ id: 'admin', name: 'Admin', href: '#', current: false },
];

export const USER_DEFAULT = {
	id: null,
	email: null,
	username: null,
	password: null,
	name: null,
	mobile_phone: null,
	roles: null,
	language: 'en',
	is_verified: true,
	is_spectator: false,
	is_allow_notify: false,
	settings: {},
	picture: null,
	gender: null,
	job_title: null,
	address: null,
	city: null,
	state: null,
	country: null,
	postal_code: null,
	company: null,
	birth_date: null,
	biography: null,
	package_id: null,
};

export const USER_ROLES = [
	{ id: 'client', name: 'Client' },
	{ id: 'super_admin', name: 'Admin' },
];

export const USER_LANGUAGES = [
	{ id: 'en', name: 'English' },
	{ id: 'id', name: 'Indonesia' },
	{ id: 'no', name: 'Norsk' },
];

export const USER_MENUS = [
	{ name: 'My Account', href: '/settings/profile' },
	{ name: 'Language', href: '', child: [
		{ name: 'English', key: 'en' },
		{ name: 'Bokmål', key: 'no' },
		{ name: 'Bahasa', key: 'id' },
	]},
	{ name: 'Subscription', href: '/subscriptions' },
	// { name: 'Billing', href: '/billings' },
	// { name: 'Settings', href: '/profile' },
	{ name: 'Onboarding', href: '' },
	{ name: 'Customer Support', href: 'https://support.bannerbite.com/' },
	{ name: 'Sign out', href: '/logout' },
];