<template>
  <div class="assignee-selector ml-5" >
    <multiselect 
      v-model="selectedUser" 
      :options="users" 
      :searchable="true" 
      :close-on-select="true"
      :custom-label="customLabel"
      no-result="No matches found"
      placeholder="Search for user"
      :show-labels="false"
      @close="closeDropdown"
      @select="select"
      @remove="removeAssign"
      @open="openDropdown"
      track-by="id"
      value="id"
      class="custom-multiselect-edit"
      label="first_name">
      <template #placeholder>
        <div class="pointer w-[24px] h-[24px] flex items-center border border-dashed border-gray-400 rounded-full pl-[6px]">
          <UserAddIcon class="h-3 w-3 text-gray-500" />
        </div>
      </template>
      <template #singleLabel="{ option }">
        <div class="flex items-center ">
          <img
            v-if="option.imgUrl" 
            class="rounded-full w-[22px] h-[22px] object-cover border-2"
            alt="avatar-image"
            :src="option.imgUrl"
            referrerpolicy="no-referrer"
          >
          <div :style="{ backgroundColor: __getColorByInitial(option.first_name[0]) }" v-else class="text-[8px] mr-2 rounded-full w-[22px] h-[22px] font-medium text-center bg-white uppercase border-[1px]">
            {{ __generateInitial(option.first_name) }} 
          </div>
          <div class="text-xs text-gray-700">{{option.first_name}}</div>
        </div>
      </template>
      <template #option="{ option }">
        <div class="flex items-center">
            <img
              v-if="option.imgUrl" 
              class="rounded-full size-7 object-cover border-2"
              :src="option.imgUrl"
              alt="avatar-image"
              referrerpolicy="no-referrer"
            >
          <div class="flex items-center" v-else>
            <div :style="{ backgroundColor: __getColorByInitial(option.first_name[0]) }" class="text-[10px] text-black mr-2 rounded-full w-[26px] h-[26px] pt-[4px] font-medium text-center bg-white uppercase border-[1px]">
              {{ __generateInitial(option.first_name) }} 
            </div>
          </div>
          <span class="">{{option.first_name}}</span>
        </div>
      </template>
      <template #noResult>
        <span>No matches found</span>
      </template>
    </multiselect>
  </div>
</template>

<script>
import {
  UserAddIcon,
} from '@heroicons/vue/outline';
import Multiselect from 'vue-multiselect';
import "vue-multiselect/dist/vue-multiselect.css";
export default {
  components: { Multiselect, UserAddIcon },
  data() {
    return {
      keyword: '',
      selectedUser: null,
      showDropdown: false,
      defaultAvatar: 'path-to-default-avatar'
    };
  },
  props: {
    users: {
    },
    currentAssignee: {
      type: Object
    }
  },
  mounted() {
    if (this.currentAssignee) {
      this.selectedUser = this.currentAssignee
    }
  },
  methods: {
    removeAssign() {
      this.$emit('remove')
    },
    select(event) {
      this.$emit('select', event)
    },
    closeDropdown() {
      this.showDropdown = false;
    },
    openDropdown() {
      this.showDropdown = true;
    },
    customLabel({ first_name, email }) {
      return `${first_name}`;
    },
  },
  created() {
  },
};
</script>

<style>
.custom-multiselect-edit {
  
  .multiselect__element {
    display: flex;
    align-items: center;
  }

  .multiselect__element img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .multiselect__tags {
    border: none!important;
    padding-left: 0px!important;
  }

  .multiselect__select {
    display: none;
  }

  .multiselect__single {
    padding-left: 0px!important;
  }

  .multiselect__content-wrapper {
    min-width: 330px!important;
    max-width: 330px!important;
    overflow-x: none!important;
    border-top: 1px rgb(216, 216, 216) solid;
  }

  .multiselect__option {
    min-width: 320px!important;
    max-width: 320px!important;
  }
}

.custom-multiselect-edit-width {
  width: 100%;
}

.custom-multiselect-edit-width-idle {
  width: 40px;
}

.assignee-selector {
  width: 100%;
  position: relative;
}

.assignee-selector-idle {
  width: 40px;
  position: relative;
}
</style>
