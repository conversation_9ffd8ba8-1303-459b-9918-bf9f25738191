<template>
  <div v-if="!isOpenInput" class="inline-flex flex items-center">
    <div class="text-gray-400 text-xs mr-2 mb-1">Collaborators</div>
    <div v-if="selectedUsers && selectedUsers.length" v-for="user in selectedUsers.slice(0, 3)" :key="user.id">
      <img
          v-if="user?.imgUrl" 
          class="flex rounded-full w-[20px] h-[20px] object-cover mr-[1px]"
          alt="avatar-image"
          :src="user.imgUrl"
          referrerpolicy="no-referrer"
        >
        <div v-else :style="{ backgroundColor: __getColorByInitial(user.first_name[0]) }" class="text-[8px] mr-[1px] rounded-full pt-1 w-[22px] h-[22px] font-medium text-center bg-white uppercase border-[1px]">
          {{ __generateInitial(user?.first_name) }} 
        </div>
    </div>
    <div v-if="selectedUsers && selectedUsers.length > 3" class="text-xs font-medium">
      +{{ selectedUsers.length - 3 }}
    </div>
    <PlusIcon @click="isOpenInput = true" class="ml-1 h-4 w-4" />
  </div>
  <div class="assignee-selector">
    <multiselect
      v-if="isOpenInput"
      v-model="selectedUsers" 
      :options="users" 
      :searchable="true" 
      :close-on-select="false"
      :multiple="true"
      :custom-label="customLabel"
      no-result="No matches found"
      placeholder="Search for collaborator"
      @close="closeDropdown"
      @select="select"
      @remove="removeAssign"
      @open="openDropdown"
      :show-labels="false"
      track-by="id"
      value="id"
      class="custom-multiselect-collaborator"
      label="first_name">
      <!-- Custom display for selected options -->
      <template #tag="{ option, remove }">
        <span class="inline-flex items-center mr-2 chip-collab mb-1">
          <img
            v-if="option?.imgUrl"
            class="rounded-full size-7 mr-2 object-cover"
            :src="option?.imgUrl"
            alt="avatar-image"
            referrerpolicy="no-referrer"
          >
          <div v-else :style="{ backgroundColor: __getColorByInitial(option.first_name[0]) }" class="text-[8px] mr-2 rounded-full h-6 w-6 font-medium pt-[4px] text-center bg-white uppercase border-2">
            {{ __generateInitial(option.first_name) }}
          </div>
          <div class="text-xs text-gray-700 mr-2">{{ option.first_name }}</div>
          <!-- Show remove button -->
          <button class="text-xs ml-1 text-gray-600 font-bold" @click.prevent="remove(option)">✕</button>
        </span>
      </template>
      <template #option="{ option }">
        <div class="flex items-center">
            <img
              v-if="option.imgUrl" 
              class="rounded-full size-7 object-cover border-2"
              :src="option.imgUrl"
              alt="avatar-image"
              referrerpolicy="no-referrer"
            >
          <div class="flex items-center" v-else>
            <div :style="{ backgroundColor: __getColorByInitial(option.first_name[0]) }" class="text-[10px] text-black mr-2 rounded-full w-[26px] h-[26px] pt-[4px] font-medium text-center bg-white uppercase border-[1px]">
              {{ __generateInitial(option.first_name) }} 
            </div>
          </div>
          <span class="">{{option.first_name}}</span>
        </div>
      </template>
      <template #noResult>
        <span>No matches found</span>
      </template>
    </multiselect>
  </div>
</template>


<script>
import {
  UserAddIcon,
  PlusIcon,
} from '@heroicons/vue/outline';
import Multiselect from 'vue-multiselect';
import "vue-multiselect/dist/vue-multiselect.css";
export default {
  components: { Multiselect, UserAddIcon, PlusIcon },
  data() {
    return {
      keyword: '',
      selectedUsers: null,
      showDropdown: false,
      defaultAvatar: 'path-to-default-avatar',
      isOpenInput: false,
    };
  },
  props: {
    users: {
    },
    currentCollab: {
    }
  },
  mounted() {
    setTimeout(() => {
      if (this.currentCollab) {
        this.selectedUsers = this.currentCollab
      }
    }, 500);

  },
  methods: {
    removeAssign() {
      this.$emit('remove', this.selectedUsers)
    },
    select(event) {
      this.$emit('select', this.selectedUsers)
    },
    closeDropdown() {
      this.showDropdown = false;
      this.isOpenInput = false;
    },
    openDropdown() {
      this.showDropdown = true;
    },
    customLabel({ first_name, email }) {
      return `${first_name}`;
    },
  },
  created() {
  },
};
</script>

<style scoped>
.custom-multiselect-collaborator {
  .multiselect__element {
    display: flex;
    align-items: center;
  }

  .chip-collab {
    border: 1px solid lightgray;
    background-color: white;
    border-radius: 10px;
    padding: 2px 4px 2px 4px;
  }

  .multiselect__element img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .multiselect__tags {
    max-height: 60px!important;
    min-height: 60px!important;
    height: 60px!important;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap; /* Ensure selected items are displayed in a row */
  }

  .multiselect__tag {
    margin: 2px 4px 2px 0;
    max-height: 60px!important;
    min-height: 60px!important;
    height: 60px!important;
  }

  .multiselect__single {
    padding-left: 0px !important;
  }

  .multiselect__content-wrapper {
    min-width: 330px!important;
    max-width: 330px!important;
    border-top: 1px rgb(216, 216, 216) solid;
    
  }

  .multiselect__option {
    min-width: 320px!important;
    max-width: 320px!important;
  }
}

.assignee-selector {
  margin-left: 2px!important;
  min-width: 370px;
  max-width: 370px;
  position: relative;
}
</style>

