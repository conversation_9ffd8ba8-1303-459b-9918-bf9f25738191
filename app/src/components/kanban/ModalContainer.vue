<template>
  <!-- Modal Container for Centered Modals -->
  <section
    v-if="$store.state.application.isModal || $store.state.application.mutate"
    class="fixed inset-0 z-50"
  >
    <!-- Content - The components now handle their own backdrop and positioning -->
    <TaskShow v-if="$store.state.application.inquiry.show" />

    <!-- AddEditBoardOrInquiry - Now renders as centered modal -->
    <AddEditBoardOrInquiry
      v-if="
        $route.name !== 'InquiryAlone' && $route.name !== 'InquiryAloneFull' && $route.name !== 'InquiryDetail' && $route.name !== 'InquiryDetailFull' && (
        $store.state.application.board.add ||
        $store.state.application.board.edit ||
        $store.state.application.inquiry.add ||
        $store.state.application.inquiry.edit)
      "
    />

    <!-- AddEditBoardOrInquiryAlone - Now renders as centered modal -->
    <AddEditBoardOrInquiryAlone
      v-if="
        ($route.name === 'InquiryAlone' || $route.name === 'InquiryAloneFull' || $route.name === 'InquiryDetailFull' || $route.name === 'InquiryDetail') && (
        $store.state.application.board.add ||
        $store.state.application.board.edit ||
        $store.state.application.inquiry.add ||
        $store.state.application.inquiry.edit)
      "
    />
  </section>
</template>


<script>
import TaskShow from './Main/InquiryShow.vue';
import AddEditBoardOrInquiry from './Boards/AddEditBoardOrInquiry.vue';
import AddEditBoardOrInquiryAlone from './Boards/AddEditBoardOrInquiryAlone.vue';

export default {
  name: 'ModalContainer',
  components: {
    TaskShow,
    AddEditBoardOrInquiry,
    AddEditBoardOrInquiryAlone
  },
  // The modal components now handle their own state and positioning
  // This container simply provides the conditional rendering logic
};
</script>

<style scoped>
/* Modal container styles - components handle their own styling */
</style>
