<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm" @click.self="close()">
    <!-- Modal Container -->
    <div class="relative bg-white dark:bg-kb_dark_grey w-full max-w-lg rounded-lg shadow-2xl transform transition-all duration-300 ease-out scale-100" @click.stop>

      <!-- <PERSON><PERSON>er -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white truncate pr-4">
          {{ store.inquiry.show.title }}
        </h2>
        <div class="flex items-center space-x-2">
          <!-- Menu <PERSON> -->
          <button @click="toggleMenu" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 relative">
            <svg width="5" height="20" xmlns="http://www.w3.org/2000/svg">
              <g fill="currentColor" fill-rule="evenodd">
                <circle cx="2.308" cy="2.308" r="2.308" />
                <circle cx="2.308" cy="10" r="2.308" />
                <circle cx="2.308" cy="17.692" r="2.308" />
              </g>
            </svg>
            <EllipsisMenu mode="Inquiry" class="w-40 md:w-48 mt-2 -right-4" ref="ellipsMenu" />
          </button>

          <!-- Close Button -->
          <button @click="close()" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" title="Close">
            <XIcon class="h-5 w-5" />
          </button>
        </div>
      </div>

      <!-- Modal Content -->
      <div class="p-6 space-y-6 max-h-96 overflow-y-auto">
        <!-- Description -->
        <div v-if="store.inquiry.show.description">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
            {{ store.inquiry.show.description }}
          </p>
        </div>

        <!-- Subinquiries -->
        <div v-if="count" class="space-y-3">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Subinquiries ({{ count }})
          </label>
          <div class="space-y-2">
            <div v-for="(subinquiry, index) in store.inquiry.show.subInquiry" :key="index">
              <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                <input
                  class="mr-3 cursor-pointer h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  type="checkbox"
                  :id="`check${index}`"
                  v-model="store.inquiry.show.subInquiry[index].isCompleted"
                />
                <label
                  :for="`check${index}`"
                  class="text-sm font-medium cursor-pointer flex-1"
                  :class="_class(index)"
                >
                  {{ subinquiry.title }}
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Status -->
        <div class="space-y-3">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <statusSelect @change="statusChanged" class="w-full" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    onMounted
  } from 'vue';
  import store from "../../../pages/kanban/store.js"
  import {
    onKeyStroke
  } from "@vueuse/core"
  import EllipsisMenu from "../EllipsisMenu.vue"
  import {
    XIcon,
  } from '@heroicons/vue/outline';

  // count subInquiry and completed
  let sum, done
  const count = computed(() => {
    ;
    (sum = 0), (done = 0)
    store.inquiry.show.subInquiry.forEach((el) => {
      sum++
      if (el.isCompleted) {
        done++
      }
    })
    if (sum === 0) {
      return 0
    } else {
      return `${done} of ${sum}`
    }
  })

  const ellipsMenu = ref(null)
  const toggleMenu = () => {
    if (ellipsMenu.value) {
      ellipsMenu.value.showMenu = !ellipsMenu.value.showMenu
    }
  }

  // if completed class for this subinquiry
  const _class = (index) => {
    return store.inquiry.show.subInquiry[index].isCompleted ?
      "line-through dark:no-underline" :
      ""
  }

  // event from statusSelect
  const statusChanged = (newStatus) => {
    // newStatus: {title:xxx, index:(index of the NEW status column in data)}

    // splice the inquiry out of the current column and push it to the new column
    store.changeStatus(newStatus.index)

    close()
  }

  const close = () => {
    // reset all relevant members in store
    store.inquiry.active = ""
    store.inquiry.show = undefined
    store.inquiry.status = ""
    store.inquiry.activeIndex = -1
    store.inquiry.columnIndex = -1
    store.mutate = false
  }
  onKeyStroke("Escape", (e) => {
    close()
  })
</script>

<style scoped>
  input[type="checkbox"] {
    accent-color: #635fc7;
  }
</style>