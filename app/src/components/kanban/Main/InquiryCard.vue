<template>
  <article class="w-[17.5rem] rounded-lg bg-white dark:bg-kb_dark_grey dark:text-white shadow-card cursor-pointer">
    <div class="dark:bg-kb_dark_grey rounded-md p-2 shadow-sm my-2 relative border-gray-100 border-[1px]" :class="{'bg-[#F7FCEB]': isCompleted, 'bg-[#FFF4F4]': isDueDateReached}">
      <div class="flex items-center pt-2">
      {{  }}
        <CheckCircleIcon @click.stop="updateComplete(inquiry)" class="h-4 w-4 ml-2 pointer text-gray-400 mr-2"
          :class="{'text-green-600': inquiry.status === 'completed'}" aria-hidden="true"></CheckCircleIcon>
        <div class="text-xs font-medium">{{ inquiry.title }}</div>
      </div>
      <!-- assignee / due date -->
      <div class="pl-2 mt-6 flex items-center">
        <AssigneeDirect @click.stop="" @select="assigneeSelected" @remove="assigneeRemoved" :key="`assignee-${inquiry.id}`" class="w-full"
          :currentAssignee="inquiry.assign" :users="users"></AssigneeDirect>
        <div class="absolute left-[50px]">
          <DatePickerInquiry class="font-medium" @select="datePickerSelect" :key="`datepicker-${inquiry.id}`" :currentDate="inquiry.due_date">
          </DatePickerInquiry>
        </div>
        <div v-if="inquiry && inquiry.meta && inquiry.meta.totalComments" class="absolute right-[8px] flex items-center mr-1">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
          </svg>
          <div class="text-xs ml-1" >{{ inquiry.meta?.totalComments }}</div>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
  import { mapGetters, mapActions } from 'vuex';
  import { CheckCircleIcon } from '@heroicons/vue/solid';
  import { onKeyStroke } from '@vueuse/core';
  import AssigneeDirect from '@/components/kanban/AssigneeDirect.vue';
  import DatePickerInquiry from '@/components/kanban/DatePickerInquiry.vue';
  import inquiriesApi from '@/api/inquiries';
  import startOfDay from 'date-fns/startOfDay';

  export default {
    name: 'InquiryCard',
    components: {
      CheckCircleIcon,
      AssigneeDirect,
      DatePickerInquiry
    },
    props: {
      inquiry: {
        type: Object,
        default: null,
      },
      index: {
        type: Number,
        default: 0,
      },
      users: {},
    },
    data() {
      return {
        due_date: '',
        assign_to: null,
        title: '',
        isUpdating: false, // Prevent concurrent updates
        updateTimeout: null, // For debouncing
      };
    },
    computed: {
      ...mapGetters({
        getOnlineUsers: 'application/getOnlineUsers',
        getActiveProject: 'application/getActiveProject',
        getActiveColumn: 'application/getActiveColumn',
        getActiveInquiry: 'application/getActiveInquiry',
        getBoardColsLength: 'application/getBoardColsLength',
      }),
      project() {
        return this.getActiveProject;
      },
      isCompleted() {
        return this.inquiry?.status === 'completed'
      },
      isDueDateReached() {
        if (this.inquiry.due_date) {
          const today = startOfDay(new Date()); // Normalize today's date to start of day
          const due_date = startOfDay(new Date(this.inquiry.due_date)); // Normalize due_date to start of day
          return due_date < today; // Check if the due date is today or in the past
        }
        return false; // Return false if there's no due_date
      }
    },
    methods: {
      ...mapActions({
        updateInquiryInStore: 'application/updateInquiryInStore2', // Use the safer update method
        deleteInquiryInStore: 'application/deleteInquiryInStore',
      }),
      datePickerSelect(date) {
        this.due_date = date ? this.__dateFormatISO(date) : '';
        if (this.inquiry?.id && !this.isUpdating) {
          this.debouncedUpdate();
        }
      },
      assigneeSelected(event) {
        this.assign_to = event?.id || this.inquiry?.assign?.id
        if (this.inquiry?.id && !this.isUpdating) {
          this.assignInquiry();
        }
      },

      // Debounced update method to prevent rapid successive calls
      debouncedUpdate() {
        if (this.updateTimeout) {
          clearTimeout(this.updateTimeout);
        }

        this.updateTimeout = setTimeout(() => {
          this.updateInquiry();
        }, 300); // 300ms debounce
      },
      assignInquiry() {
        // Prevent concurrent updates
        if (this.isUpdating) {
          return;
        }

        this.isUpdating = true;

        const params = {
          assign_to: this.assign_to,
        }

        // Create optimistic update data
        const optimisticData = {
          ...this.inquiry,
          assign: this.assign_to ? { id: this.assign_to } : null
        };

        // Optimistic update - immediately update the UI
        this.updateInquiryInStore(optimisticData);

        // Define the callback function that will be called after the API request is successful
        const callback = (response) => {
          this.isUpdating = false;

          const data = response.data;
          // Update with server response to ensure consistency
          let finalData = {
            ...this.inquiry, // Use current inquiry data as base
            ...data // Merge with the new data from the API response
          };
          this.updateInquiryInStore(finalData);
        };

        // Define the error callback function to handle any errors during the API request
        const errCallback = (err) => {
          this.isUpdating = false;

          // Revert optimistic update on error
          this.updateInquiryInStore(this.inquiry);

          const message = err?.response?.data?.message || 'Error occurred while updating the inquiry';
          this.__showNotif('error', 'Error', message);
        };

        // Make the API request to update the inquiry
        inquiriesApi.updateInquiry(this.inquiry?.id, params, callback, errCallback);
      },
      assigneeRemoved() {
        this.assign_to = null
        if (this.inquiry?.id) {
          this.assignInquiry()
        }
      },

      // Placeholder for future enhancements
      close() {},
      updateInquiry() {
        // Prevent concurrent updates
        if (this.isUpdating) {
          return;
        }

        this.isUpdating = true;

        // Prepare update parameters
        let params = {
          title: this.title,
          due_date: this.due_date ? this.__dateFormatISO(this.due_date) : '',
          assign_to: this.assign_to || this.inquiry?.assign?.id,
          project_id: this.project?.id,
          type: this.$store.state.application.inquiry.type
        }

        // Create optimistic update data using the current inquiry
        const optimisticData = {
          ...this.inquiry, // Use current inquiry, not getActiveInquiry
          ...params,
          // Ensure we maintain the correct structure
          assign: params.assign_to ? { id: params.assign_to } : this.inquiry.assign,
          due_date: params.due_date || this.inquiry.due_date
        };

        // Optimistic update - immediately update the UI
        this.updateInquiryInStore(optimisticData);

        const callback = (response) => {
          this.isUpdating = false;

          const data = response.data;
          const finalData = {
            ...this.inquiry, // Use current inquiry data as base
            ...data, // Apply server response
          };

          this.updateInquiryInStore(finalData);
        }

        const errCallback = (err) => {
          this.isUpdating = false;

          // Revert optimistic update on error
          this.updateInquiryInStore(this.inquiry);

          const message = err.response?.data?.message || 'Failed to update inquiry';
          this.__showNotif('error', 'Error', message);
        }

        inquiriesApi.update(this.inquiry?.id, params, callback, errCallback)
      },
      updateComplete(inquiry) {
        this.$store.state.application.inquiry.addDirectly = false;

        // Update the inquiry properties in the store
        this.$store.state.application.inquiry = inquiry;
        this.$store.state.application.inquiry.active = inquiry.id;
        this.$store.state.application.inquiry.status = inquiry.status;
        this.$store.state.application.inquiry.type = inquiry.type;

        const callback = (response) => {
          const data = response.data;

          // Merge the updated data with the existing inquiry data
          const finalData = {
            ...this.getActiveInquiry, // Existing inquiry data
            ...data, // New data from the server
          };
          this.updateInquiryInStore(finalData);
        };
        let params = {
          status: !inquiry.status || inquiry.status === 'incomplete' ? 'complete' : 'incomplete'
        }

        // Make the API call to update the inquiry on the server
        inquiriesApi.completeInquiry(inquiry.id, params, callback, (error) => {
          console.error('Error updating inquiry:', error);
        });
      },
    },
    mounted() {
      onKeyStroke('Escape', this.close);
    },
    beforeUnmount() {
      // Clean up timeout to prevent memory leaks
      if (this.updateTimeout) {
        clearTimeout(this.updateTimeout);
      }
    },
  };
</script>
