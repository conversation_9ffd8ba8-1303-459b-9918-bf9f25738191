<template>
  <div style="height: calc(100vh - 220px);">
    <div class="relative ml-6 flex flex-row gap-4">
      <template v-if="getBoardColsLength > 0 && !isShow" >
        <InquiryColumn
          ref="columns"
          v-if="users"
          :users="users"
          :column="col"
          v-for="(col, index) in cols"
          :colIndex="index"
          :colTitle="col.title"
          :key="col.title"
          :activeCol="activeCol"
          @dragEnd="handleDragEnd"
          @dragStart="handleDragStart"
          @afterInquiryAdded="handleAfterInquiryAdded"
          @columnChange="columnChange"
          />
      </template>
      <template v-if="getBoardColsLength > 0 && isShow" >
        <InquiryColumn
          ref="columns"
          v-if="users"
          :users="users"
          :column="col"
          v-for="(col, index) in cols"
          :colIndex="index"
          :colTitle="col.title"
          :key="col.title"
          :activeCol="activeCol"
          @dragEnd="handleDragEnd"
          @dragStart="handleDragStart"
          @afterInquiryAdded="handleAfterInquiryAdded"
          @columnChange="columnChange"
          />
      </template>
    <InquiryColumn
      :column="columnAdd"
      :colIndex="cols?.length + 1"
      :isAddColumn="true"
      @columnChange="columnChange"
      ></InquiryColumn>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import userApi from "@/api/user";
import inquiriesApi from "@/api/inquiries";
import { delay } from '@/libraries/helper';

export default {

  data() {
    return {
      users: [],
      currentActiveInquiry: {},
      currentActiveCol: -1,
      columnAdd: {
        title: 'Add Section New Desidia',
        inquiries: [],
      },
      isShow: true,
    };
  },
  computed: {
    ...mapGetters({
      getActiveBoard: 'application/getActiveBoard',
      getBoardColsLength: 'application/getBoardColsLength',
      getActiveProject: 'application/getActiveProject',
      getActiveInquiry: 'application/getActiveInquiry',
    }),
    cols() {
      return this.getActiveBoard?.columns;
    },
    activeCol() {
      return this.currentActiveCol
    }
  },
  watch: {
    
  },
  methods: {
    ...mapActions({
      updateInquiryInStore: 'application/updateInquiryInStore2', // Use the safer update method
      updateInquiryInStoreSocket: 'application/updateInquiryInStoreSocket',
      updateOneInquiryInStoreSocket: 'application/updateOneInquiryInStoreSocket',
      deleteInquiryInStore: 'application/deleteInquiryInStore',
      showDetailInquiry: 'application/showDetailInquiry',
    }),
    handleAfterInquiryAdded(dataForOrdering) {
      const inquiries = this.cols.map((col) => {
        return {
          type: col.name,
          inquiry_ids: col.inquiries.map(inquiry => inquiry.id)
        };
      });

      let inquiryGroup = inquiries.find(inquiry => inquiry.type === dataForOrdering.type);

      if (inquiryGroup) {
        if (dataForOrdering.position === 'top') {
          inquiryGroup.inquiry_ids.unshift(dataForOrdering.id);
        } else {
          inquiryGroup.inquiry_ids.push(dataForOrdering.id);
        }
      }
      const finalParams = {
        type: inquiryGroup.type,
        inquiry_ids: inquiryGroup.inquiry_ids,
        inquiry_id: dataForOrdering.id,
      };
      this.reorderInquiry(finalParams);
    },
    getInquiryIdsByType(type) {
      const inquiryGroup = inquiries.find(inquiry => inquiry.type === type);
      return inquiryGroup ? inquiryGroup.inquiry_ids : [];
    },

    fetchUsers(keyword = null) {
      const callback = (response) => {
        const data = response.data;
        this.users = data
      }
      const errCallback = (err) => {
        console.log(err)
      }
        const params = {
        limit: 9999,
        order_by: "first_name",
        sort_by: "asc",
      }
        userApi.getList(params, callback, errCallback)
      
    },
    handleDragEnd(payloadIdAndType) {
      // Collect inquiry IDs from all columns
      const payload = this.cols.map((col) => {
        return {
          type: col.name,  
          inquiry_ids: col.inquiries.map(inquiry => inquiry.id)
        };
      });
      let indexColumn = parseInt(payloadIdAndType?.payload?.indexColumn)
      let finalParams = payload[indexColumn]
      finalParams.inquiry_id = payloadIdAndType?.payload?.inquiry_id
      // delay(() => {
        this.reorderInquiry(finalParams);
      // }, 500);
    },
    reorderInquiry(paramsFinal) {
      // Update the inquiry properties in the store
      const callback = (response) => {
        const data = response.data;
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      }
      inquiriesApi.reorder(paramsFinal, callback, errCallback)
    },
    columnChange(data, additionalInfoAddingColumn) {
      this.$emit('columnChange', data, additionalInfoAddingColumn)
    },
    columnRemove(columnId) {
      this.$emit('columnRemove', columnId)
    },
  },
  mounted() {
    if (this.$route.params.slug) {
      this.showDetailInquiry(atob(this.$route.params.slug));
    }
  },
  beforeUnmount () {
    const roomId = this.getActiveProject?.slug;
    this.$soketio.emit('leave', roomId);
  },
  created() {
    this.fetchUsers();
    const roomId = this.getActiveProject?.slug;
    this.$soketio.emit('join', roomId);
    // main logic of socket, the core fo the core, si kodingnya ada di application store ya maniez
    this.$soketio.on('inquiry_reorder', (reorderData) => {
      console.log('🔄 Socket: inquiry_reorder received:', reorderData);

      // Validate reorderData
      if (!reorderData || !reorderData.new_inquiries || !Array.isArray(reorderData.new_inquiries)) {
        console.warn('Invalid reorder data received:', reorderData);
        return;
      }

      this.isShow = false
      delay(() => {
        this.isShow = true
      }, 200);

      for (let index = 0; index < reorderData.new_inquiries.length; index++) {
        const newInquiry = reorderData.new_inquiries[index];

        // Validate inquiry data
        if (!newInquiry || !newInquiry.id) {
          console.warn('Invalid inquiry data at index', index, ':', newInquiry);
          continue;
        }

        const element = {
          inquiry: newInquiry,
        }

        if (index + 1 === reorderData.new_inquiries.length) {
          // Check if oldInquiry exists and has required properties
          if (reorderData.oldInquiry && reorderData.oldInquiry.id) {
            const oldInquiryExistsInNewInquiries = reorderData.new_inquiries.some(newInquiry =>
              newInquiry.id === reorderData.oldInquiry.id && newInquiry.type === reorderData.oldInquiry.type
            );
            if (!oldInquiryExistsInNewInquiries) {
              element.oldInquiry = reorderData.oldInquiry;
              this.updateInquiryInStoreSocket(element);
            }
          }
        }
        this.updateInquiryInStoreSocket(element);
      }
    });

    // delete inquiry
    this.$soketio.on('inquiry_delete', (inquiry) => {
      delay(() => {
        this.deleteInquiryInStore(inquiry)
      }, 500);
    });

    // update inquiry
    this.$soketio.on('inquiry_update', (inquiry) => {
      // Use the safer update method that preserves position
      this.updateInquiryInStore(inquiry)
    });
  }
};
</script>

<style scoped></style>
