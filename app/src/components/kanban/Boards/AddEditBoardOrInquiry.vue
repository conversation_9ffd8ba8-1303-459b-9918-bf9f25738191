<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm" @click.self="close">
    <!-- Modal Container -->
    <section class="w-full max-w-4xl h-[90vh] bg-white dark:bg-kb_dark_grey rounded-lg shadow-2xl relative flex flex-col overflow-hidden transform transition-all duration-300 ease-out scale-100" @click.stop>

      <!-- Loading State -->
      <div class="h-96 flex items-center justify-center" v-if="isFetching">
        <loader-circle v-if="isFetching" />
      </div>

      <!-- Modal Header -->
      <div v-if="!isFetching" class="flex flex-row justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex-shrink-0">
        <div class="flex items-center">
          <CheckCircleIcon @click.stop="updateComplete(inquiry || workData)" class="h-6 w-6 pointer transition-colors duration-200" aria-hidden="true"
          :class="{'text-green-600': inquiry?.status === 'completed' || workData?.status === 'completed', 'text-gray-400 hover:text-green-500': !(inquiry?.status === 'completed' || workData?.status === 'completed')}"></CheckCircleIcon>
          <div class="ml-3 font-medium text-gray-900 dark:text-gray-100">Mark Complete</div>
        </div>
        <div class="flex items-center space-x-2">
          <Menu v-if="isAdmin" as="div" class="relative">
            <div>
              <MenuButton class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                <DotsHorizontalIcon class="h-5 w-5" aria-hidden="true"></DotsHorizontalIcon>
              </MenuButton>
            </div>
            <transition enterActiveClass="transition ease-out duration-100" enterFromClass="transform opacity-0 scale-95"
              enterToClass="transform opacity-100 scale-100" leaveActiveClass="transition ease-in duration-75"
              leaveFromClass="transform opacity-100 scale-100" leaveToClass="transform opacity-0 scale-95">
              <MenuItems
                class="absolute right-0 top-full mt-2 w-40 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                <MenuItem
                  @click="deleteInquiry(inquiry)"
                  v-slot="{ active }"
                >
                  <div :class="[active ? 'bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-300' : 'text-gray-700 dark:text-gray-300', 'block px-4 py-2 text-sm cursor-pointer']">
                    {{ $t('Delete') }}
                  </div>
                </MenuItem>
              </MenuItems>
            </transition>
          </Menu>
          <button @click="copyURL()" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" title="Copy URL">
            <LinkIcon class="h-5 w-5" aria-hidden="true"></LinkIcon>
          </button>
          <!-- <button @click="toggleRouteFullscreen" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" title="Toggle Fullscreen">
            <ExternalLinkIcon class="h-5 w-5" aria-hidden="true"></ExternalLinkIcon>
          </button> -->
          <!-- Close button -->
          <button @click="close" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" title="Close">
            <XIcon class="h-5 w-5" aria-hidden="true"></XIcon>
          </button>
        </div>
      </div>

      

      <!-- Modal Content -->
      <div ref="scrollContainerContent" v-if="!isFetching" class="flex-1 overflow-hidden flex flex-col">
        <!-- Header Section -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <!-- Breadcrumbs -->
          <div v-if="workData?.parent_id" class="flex items-center text-primary-600 hover:text-primary-700 cursor-pointer transition-colors duration-200 mb-4" @click="goBackParent()">
            <ChevronLeftIcon class="h-4 w-4 mr-1" aria-hidden="true"></ChevronLeftIcon>
            <span class="text-sm font-medium">{{ workData?.parent?.title }}</span>
          </div>

          <!-- Inquiry Title -->
          <div class="mb-4 flex item-center">
            <kbInput ref="inputTitle" controlType="titleInquiry" @blur="updateInquiry()" v-model="workData.title" :ph="ph" class="text-xl font-semibold w-full">
            </kbInput>
            <div class="flex items-center mr-5">
              <label class="text-nowrap block text-sm font-medium text-gray-700 dark:text-gray-300 mr-4">
                {{ $t('Due') }}
              </label>
              <DatePickerTaskEdit
                v-if="!isSocketHit"
                :currentDate="workData.due_date"
                @updateDatepicker="updateDatepicker"
                @select="datePickerSelect"
                class="w-full">
              </DatePickerTaskEdit>
              <DatePickerTaskEdit
                v-if="isSocketHit"
                :currentDate="workData.due_date"
                @updateDatepicker="updateDatepicker"
                @select="datePickerSelect"
                class="w-full">
              </DatePickerTaskEdit>
            </div>
          </div>

          <!-- Project Info & Status -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('Project') }}</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-md">
                {{ getActiveProject?.name || inquiry.projectName }}
              </span>
            </div>
            <div class="flex items-center">
              <statusSelect :_mode="statusMode" :workDataParent="workData" @change="handleStatusChange" v-if="mode === 'inquiry' && !this.workData.parent_id && !isSocketHit"></statusSelect>
              <statusSelect :_mode="statusMode" :workDataParent="workData" @change="handleStatusChange" v-if="mode === 'inquiry' && !this.workData.parent_id && isSocketHit"></statusSelect>
              <span v-if="this.workData.parent_id && !isSocketHit" class="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-md">
                {{ workData?.type }}
              </span>
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="px-6 border-b border-gray-200 dark:border-gray-700">
          <nav class="flex space-x-8" aria-label="Tabs">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
              ]"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="flex-1 overflow-y-auto">
          <div class="p-6 space-y-6">
            <!-- Details Tab - Keep original layout and logic -->
            <div v-if="activeTab === 'details'">
              <!-- Inquiry Description -->
              <div class="space-y-3">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Description
                </label>
                <CommentBox
                  :description="workData.description"
                  @update:description="updateTextCKEditor"
                  :ph="'Inquiry description'"
                  class="w-full">
                </CommentBox>
              </div>

              <!-- Subinquiries -->
              <div class="space-y-3">
                <ListColumnsOrSubinquiries
                  v-if="!isSocketHit"
                  :users="users"
                  @openInquiry="openInquiry"
                  :parent_id="inquiry?.id || workData.id"
                  :projectId="project?.id"
                  ref="col_sub"
                  :data="workData"
                  :mode="mode"
                  class="w-full" />
                <ListColumnsOrSubinquiries
                  v-else
                  :users="users"
                  @openInquiry="openInquiry"
                  :parent_id="inquiry?.id"
                  :projectId="project?.id"
                  ref="col_sub"
                  :data="workData"
                  :mode="mode"
                  class="w-full" />
              </div>

              <!-- Activity & Comments -->
              <div class="space-y-3">
                <Activity
                  v-if="!isFetching"
                  ref="activity"
                  :workDataSubinquiry="workData"
                  :users="users"
                  :mode="mode"
                  class="w-full" />
              </div>
            </div>

            <!-- Collaborators Tab -->
            <InquiryCollaboratorsTab
              v-if="activeTab === 'collaborators'"
              :inquiryData="workData"
              @assignment-updated="handleAssignmentUpdated"
            />

            <!-- Attachment Tab -->
            <InquiryAttachmentTab
              v-if="activeTab === 'attachment'"
              :inquiryData="workData"
            />

            <!-- Notes Tab -->
            <InquiryNotesTab
              v-if="activeTab === 'notes'"
              :inquiryData="workData"
              @notes-updated="handleNotesUpdated"
            />
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div v-if="!isFetching && activeTab === 'details'" class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-6 flex-shrink-0">
        <!-- Comment Input -->
        <div class="flex w-full items-center">
          <div class="flex-1">
            <kbInput
              ref="inputComment"
              v-model="comment"
              controlType="textarea2"
              class="w-full"
              :ph="'Add a comment...'">
            </kbInput>
          </div>
          <div>
          <!-- Submit Comment Button -->
          <t-button
            :type="'submit'"
            :color="`primary-solid`"
            :isLoading="isCommenting"
            :isDisabled="!comment"
            @click="createComments()"
            class="ml-4 py-0">
              <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
          </t-button>
          </div>
        </div>
      </div>

    </section>
  </div>
</template>


<script>
import {
  mapGetters,
  mapActions
} from 'vuex';
  import {
    CheckCircleIcon
  } from '@heroicons/vue/solid';
  import {
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    DotsHorizontalIcon,
    ThumbUpIcon,
    CalendarIcon,
    PaperClipIcon,
    DownloadIcon,
    DocumentTextIcon,
    ChevronLeftIcon
  } from '@heroicons/vue/outline';
import { ref, toRaw, onMounted } from 'vue';
import { onKeyStroke } from '@vueuse/core';
import statusSelect  from "@/components/kanban/StatusSelect.vue";
import CommentBox  from "@/components/kanban/CommentBox.vue";
import Loader  from "@/components/loader/LoaderCircle.vue";
import store from '../../../pages/kanban/store.js';
import kbInput  from "@/components/kanban/kbInput.vue";
import TButton from '@/components/global/Button.vue';
import ListColumnsOrSubinquiries  from "@/components/kanban/Boards/ListColumnsOrSubinquiries.vue";
import Activity  from "@/components/kanban/Boards/Activity.vue";
import Assignee  from "@/components/kanban/Assignee.vue";
import Collaborator  from "@/components/kanban/Collaborator.vue";
import DatePickerTaskEdit  from "@/components/kanban/DatePickerInquiryEdit.vue";

import InquiryCollaboratorsTab from "@/components/inquiry/InquiryCollaboratorsTab.vue";
import InquiryAttachmentTab from "@/components/inquiry/InquiryAttachmentTab.vue";
import InquiryNotesTab from "@/components/inquiry/InquiryNotesTab.vue";
import userApi from "@/api/user";
import inquiriesApi from "@/api/inquiries";
import fileApi from '@/api/files';
import attachmentApi from '@/api/attachment';




export default {
  name: 'YourComponentName',
  components: {
    PaperClipIcon,
    Loader,
    kbInput,
    statusSelect,
    CommentBox,
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    ThumbUpIcon,
    DotsHorizontalIcon,
    CheckCircleIcon,
    CalendarIcon,
    TButton,
    ListColumnsOrSubinquiries,
    Activity,
    Assignee,
    DatePickerTaskEdit,
    Collaborator,
    DocumentTextIcon,
    DownloadIcon,
    ChevronLeftIcon,

    InquiryCollaboratorsTab,
    InquiryAttachmentTab,
    InquiryNotesTab
  },
  data() {
    return {
      isUploading: false,
      workData: { title: '', description: '', status: '', sub_inquiries: [], columns: [] },
      vmTitle: '',
      vmDescription: '',

      // Tab management
      activeTab: 'details',
      tabs: [
        { id: 'details', name: 'Details' },
        { id: 'collaborators', name: 'Collaborators' },
        { id: 'attachment', name: 'Attachment' },
        { id: 'notes', name: 'Notes' }
      ],

      // Original properties that were removed
      comment: '',
      isCommenting: false,
      isUploadingFile: false,
      validation: false,
      col_sub: null,
      head: '',
      ph: '',
      mode: '',
      statusCaption: '',
      statusMode: '',
      users: [],
      isFetching: false,
      comment: null,
      isSocketHit: false,
      isFull: false,
      // upload
      fileUrl: null,
      isUploadingFile: false,
    };
  },
  watch: {
    // Watch for changes in the active inquiry from store
    getActiveInquiry: {
      handler(newInquiry) {
        if (newInquiry && this.$store.state.application.inquiry.edit) {
          console.log('Active inquiry changed:', newInquiry);
          this.workData = { ...newInquiry };
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.isFetching = true;
    setTimeout(() => {
      this.initializeData();
    }, 500);
    this.$soketio.on('project_update', (data) => {
      if (this.project.id === data.id) this.setDataProject(data); 
    });
    this.$soketio.on('kanban_update', (kanbanData) => {
      if (!this.getActiveColumn) {
        this.$store.state.application.inquiry.type = kanbanData?.name;
        return;  // Exit once a match is found
      }
    });
    this.$soketio.on('inquiry_update', (inquiry) => {
      if (this.workData?.id === inquiry?.id) {
        this.isSocketHit = true
        setTimeout(() => {
          this.workData = inquiry
          this.isSocketHit = false
        }, 200);
      }
    });
    this.$soketio.on('attachment_add', (attachment) => {
      this.workData.attachments.push(attachment);
    });
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveInquiry: 'application/getActiveInquiry',
      getBoardColsLength: 'application/getBoardColsLength',
      user: 'auth/user',
      isAdmin: 'auth/isAdmin',
      getActiveBoard: 'application/getActiveBoard',
    }),
    project() {
      return this.getActiveProject;
    },
    inquiry() {
      return this.getActiveInquiry;
    }
  },
  watch: {
    inquiry() {
      if (this.inquiry) {
          let inquiryActive = JSON.parse(JSON.stringify(toRaw(this.getActiveInquiry)));
          this.getInquiry(atob(this.$route.params.slug), inquiryActive)
      }
    },
    // Watch for route changes to determine if it's in fullscreen mode
    '$route.name': function(newRoute) {
      this.isFull = newRoute === 'InquiryDetailKanbanFull';
    },
    '$route.params.slug': function(newRoute) {
      this.getInquiry(atob(this.$route.params.slug))
      this.isFetching = true;
      this.isSocketHit = true
      setTimeout(() => {
        this.isSocketHit = false
      }, 200);
    }
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      changeStatus: 'application/changeStatus',
      updateInquiryInStore: 'application/updateInquiryInStore2', // Use the safer update method
      resetStoreInquiry: 'application/resetStoreInquiry',
      setDataProject: 'application/setDataProject',
    }),


    toggleRouteFullscreen() {
      // Check if the current path already ends with "/f"
      if (this.$route.path.endsWith('/f')) {
        // If the current route ends with "/f", navigate back to the original path
        this.$router.push(this.$route.path.slice(0, -2)); // Remove the "/f"
      } else {
        // Otherwise, append "/f" to the current path
        this.$router.push(`${this.$route.path}/f`);
      }
    },
    getFileName(url) {
      // Split the URL by '/' and return the last part
      return url.split('/').pop();
    },
    downloadFile(url) {
      // Create a new anchor element
      const link = document.createElement("a");

      // Set the href to the file URL
      link.href = url;

      // Set the target to '_blank' to open in a new tab
      link.target = "_blank";

      // Set the download attribute (optional, use only if you want to prompt download)
      // link.download = this.fileUrl.split('/').pop(); // Uncomment if you want to force download

      // Append the link to the body (required for Firefox)
      document.body.appendChild(link);

      // Programmatically click the link to trigger the download or open in new tab
      link.click();

      // Remove the link from the document
      document.body.removeChild(link);
    },
    getFileExtension(url) {
      if (!url) return ''; // Return empty if no URL

      // Split the URL by '.' and get the last element
      const parts = url.split('.');
      const extension = parts.length > 1 ? parts.pop().toUpperCase() : ''; // Get extension and convert to uppercase

      // Append additional text based on the file type
      if (extension === 'JPG' || extension === 'JPEG' || extension === 'PNG' || extension === 'GIF') {
        return `${extension} (Image)`; // For image files
      } else if (extension) {
        return `${extension} (File)`; // For other file types
      }
      
      return ''; // Return empty if no extension found
    },
    createComments() {
      this.$refs.activity.createComments(this.comment)
      this.comment = null;
      const container = this.$refs.scrollContainerContent;
      setTimeout(() => {
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth', // Smooth scrolling
          });
        }
      }, 500);
    },
    assigneeSelected(event) {
      this.workData.assign_to = event?.id || this.workData?.assign?.id
      this.workData.assign = event || this.workData?.assign
      if (this.inquiry?.id || this.workData?.id) {
        this.assignInquiry();
      }
    },
    assignInquiry() {
      const callback = (response) => {
        const data = response.data;
        let finalData = {
          ...this.inquiry, // Fetch the existing inquiry data from the store
          ...data // Merge with the new data from the API response
        };
        if( this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) { this.updateInquiryInStore(finalData); }
      };

      const errCallback = (err) => {
        const message = err.response?.data?.message || 'Error occurred while updating the inquiry';
        this.__showNotif('error', 'Error', message);
        this.handleErrors(err.response.data);
      };

      const params = {
        assign_to: this.workData.assign_to,
      }

      inquiriesApi.updateInquiry(this.inquiry?.id || this.workData?.id, params, callback, errCallback);
    },
    assigneeRemoved() {
      this.workData.assign_to = null
      if (this.inquiry?.id || this.workData?.id) {
        this.assignInquiry();
      }
    },
    assigneeSelectedCollab(collabs) {
      let collabsFinal = collabs.map(item => item.id);
      this.workData.collaboratorIds = collabsFinal
      if (this.inquiry?.id || this.workData?.id) {
        this.updateInquiry();
      }
    },
    assigneeRemovedCollab(collabs) {
      let collabsFinal = collabs.map(item => item.id);
      this.workData.collaboratorIds = collabsFinal
      if (this.inquiry?.id || this.workData?.id) {
        this.updateInquiry();
      }
    },
    updateDatepicker(date) {
      this.workData.due_date = date ? this.__dateFormatISO(date) : '';
    },

    datePickerSelect(date) {
      this.workData.due_date = date ? this.__dateFormatISO(date) : '';
      if (this.inquiry?.id || this.workData?.id) {
        this.updateInquiry();
      }
    },

    handleAssignmentUpdated(collaborators) {
      // Update the workData with new collaborators
      this.workData.collaborator = collaborators;

      // Update the inquiry if it exists
      if (this.inquiry?.id || this.workData?.id) {
        this.updateInquiry();
      }
    },
    initializeData() {
      console.log('Initializing AddEditBoardOrInquiry data...');

      this.head = (this.$store.state.application.inquiry.edit ? 'Edit' : 'Add New') + ' Inquiry';
      this.ph = 'Inquiry Name';
      this.mode = 'inquiry';
      this.statusCaption = (this.$store.state.application.inquiry.add ? '' : 'Current ') + 'Status';
      this.statusMode = store.inquiry.add ? 'new' : 'edit';

      if (this.$store.state.application.inquiry.edit) {
        let inquiryActive = JSON.parse(JSON.stringify(toRaw(this.getActiveInquiry)));
        console.log('Editing existing inquiry:', inquiryActive);
        this.getInquiry(atob(this.$route.params.slug), inquiryActive)
      } else if (this.$store.state.application.inquiry.add) {
        // Initialize workData for new inquiry
        console.log('Creating new inquiry');
        this.workData = {
          title: '',
          description: '',
          status: '',
          type: this.$store.state.application.inquiry.status || '',
          sub_inquiries: [],
          columns: [],
          due_date: '',
          assign_to: null,
          collaboratorIds: []
        };
      }

      console.log('WorkData initialized:', this.workData);
    },
    close() {
      this.resetStoreInquiry();
      this.$store.state.application.mutate = false;
      if( this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull') {
        this.$router.push(`/e/kanban/${this.__encryptProjectData(this.getActiveProject?.id, this.getActiveProject?.slug)}`)
      } else {
        this.$router.push(`/inquiries`)
      }
      
    },

    updateTextCKEditor(newValue) {
      this.workData.description = newValue || '';
      if ((this.inquiry?.id || this.workData?.id) && newValue !== '<p> </p>') {
        this.updateInquiry();
      }
    },

    handleStatusChange(statusChangeData) {
      console.log('🔄 Status change received in modal:', statusChangeData);

      // Safety check for statusChangeData
      if (!statusChangeData) {
        console.warn('Invalid status change data received');
        return;
      }

      // Safety check for workData
      if (!this.workData) {
        console.warn('WorkData is not available');
        return;
      }

      // Handle optimistic UI update for column movement
      if (statusChangeData.oldType !== statusChangeData.newType) {
        console.log(`Moving inquiry from "${statusChangeData.oldType}" to "${statusChangeData.newType}"`);

        // Update the workData immediately
        this.workData.type = statusChangeData.newType;

        // Update store immediately for optimistic UI
        if (this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
          const optimisticData = {
            ...this.inquiry,
            ...this.workData
          };
          this.updateInquiryInStore(optimisticData);
        }

        // Emit event to parent component to handle column movement
        this.$emit('inquiryStatusChanged', {
          inquiry: this.workData,
          oldType: statusChangeData.oldType,
          newType: statusChangeData.newType,
          optimistic: true
        });
      }

      // If there's a rollback, handle it
      if (statusChangeData.rollback) {
        console.log('🔄 Rolling back status change');
        this.workData.type = statusChangeData.oldType;

        if (this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
          const revertedData = {
            ...this.inquiry,
            ...this.workData
          };
          this.updateInquiryInStore(revertedData);
        }

        this.$emit('inquiryStatusChanged', {
          inquiry: this.workData,
          oldType: statusChangeData.newType,
          newType: statusChangeData.oldType,
          rollback: true
        });
      }
    },

    updateInquiry() {
      console.log('Updating inquiry...', this.workData);

      // OPTIMISTIC UPDATE: Update store immediately
      if (this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
        const optimisticData = {
          ...this.inquiry,
          ...this.workData
        };
        this.updateInquiryInStore(optimisticData);
      }

      // Define the callback function that will be called after the API request is successful
      const callback = (response) => {
        const data = response.data;
        console.log('✅ Inquiry update successful:', data);

        // Confirm the optimistic update with server data
        let finalData = {
          ...this.inquiry, // Fetch the existing inquiry data from the store
          ...data // Merge with the new data from the API response
        };

        if( this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
          this.updateInquiryInStore(finalData);
        }

        // Update local workData with confirmed server data
        this.workData = { ...this.workData, ...data };
      };

      // Define the error callback function to handle any errors during the API request
      const errCallback = (err) => {
        console.error('❌ Inquiry update failed:', err);
        const message = err.response?.data?.message || 'Error occurred while updating the inquiry';
        this.__showNotif('error', 'Error', message);
        this.handleErrors(err.response.data);

        // TODO: Implement rollback logic if needed
      };

      // Format the due_date before sending it to the server
      this.workData.due_date = this.workData.due_date ? this.__dateFormatISO(this.workData.due_date) : '';
      // Make the API request to update the inquiry
      inquiriesApi.update(this.inquiry?.id || this.workData?.id, this.workData, callback, errCallback);
    },

    inputRefs(el, index) {
      if (el === null) {
        return;
      }
      if (this.workData.title.length === 0) {
        if (this.validation && index === -1) {
          el.isError = true;
        }
      } else {
        el.isError = false;
      }
    },
    fetchUsers(keyword = null) {
      const callback = (response) => {
        const data = response.data;
        this.users = data
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
      }
      setTimeout(() => {
        const params = {
        limit: 9999,
        order_by: "first_name",
        sort_by: "asc",
      }
        userApi.getList(params, callback, errCallback)
      }, 500);
    },

    deepMerge(target, source) {
      for (const key in source) {
        if (Array.isArray(source[key])) {
          // Concatenate arrays
          target[key] = (target[key] || []).concat(source[key]);
        } else if (source[key] !== null && typeof source[key] === 'object') {
          // Recursively merge objects
          target[key] = this.deepMerge(target[key] || {}, source[key]);
        } else {
          // Overwrite primitives
          target[key] = source[key];
        }
      }
      return target;
    },

    deleteInquiry(item) {
      const callback = (response) => {
        const data = response.data;
        this.$store.state.application.mutate = false;
        this.close()
        this.resetStoreInquiry();
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
      }
      inquiriesApi.delete(this.workData.id, callback, errCallback)
    },
    
    getInquiry(id, inquiryActive) {
        this.isFetching = true;
      const callback = (response) => {
        this.isFetching = false
        const data = response.data;
        const comments = data.comments;
        this.$nextTick(()=> {
          this.$refs.activity.reinitComments(comments)
        })
        this.workData = data
        this.workData.description = data.description || '';
        this.$store.state.application.inquiry.type = data.type;

        const roomId = this.workData?.slug;
        this.$soketio.emit('join', roomId);
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isFetching = false
      }

      inquiriesApi.get(id, callback, errCallback)
    },
    openInquiry(item) {
      // need to add new component to show detail subinquiry
    },
    updateComplete(inquiry) {
      console.log('Updating inquiry completion status...', inquiry);

      const newStatus = !inquiry.status || inquiry.status === 'incomplete' ? 'complete' : 'incomplete';

      // OPTIMISTIC UPDATE: Update UI immediately
      const optimisticInquiry = { ...inquiry, status: newStatus };
      this.workData = { ...this.workData, status: newStatus };

      if( this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
        this.updateInquiryInStore(optimisticInquiry);
      }

      const callback = (response) => {
        const data = response.data;
        console.log('✅ Inquiry completion update successful:', data);

        // Confirm the optimistic update with server data
        let finalData = {
          ...this.inquiry, // Fetch the existing inquiry data from the store
          ...data // Merge with the new data from the API response
        };

        if( this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
          this.updateInquiryInStore(finalData);
        }

        // Update local workData with confirmed server data
        this.workData = { ...this.workData, ...data };
      };

      const errorCallback = (error) => {
        console.error('❌ Error updating inquiry completion:', error);

        // ROLLBACK: Revert optimistic changes
        const revertedInquiry = { ...inquiry, status: inquiry.status };
        this.workData = { ...this.workData, status: inquiry.status };

        if( this.$route.name !== 'InquiryDetail' && this.$route.name !== 'InquiryDetailFull' && !this.workData.parent_id) {
          this.updateInquiryInStore(revertedInquiry);
        }

        this.__showNotif('error', 'Error', 'Failed to update inquiry status');
      };

      let params = {
        status: newStatus
      }

      // Make the API call to update the inquiry on the server
      inquiriesApi.completeInquiry(inquiry?.id, params, callback, errorCallback);
    },

    copyURL() {
      const baseURL = import.meta.env.VITE_APP_URL; // Get the base URL from environment variables
      const fullURL = baseURL + `/e/${btoa(this.project.id)}/${this.$route.params.slug}/f`; // Combine base URL and current route
      // Copy the full URL to the clipboard
      navigator.clipboard.writeText(fullURL)
        .then(() => {
          // Optional: Notify the user that the URL has been copied
          this.__showNotif('success', 'success', 'URL Copied to clipboard');
        })
        .catch(err => {
          console.error("Failed to copy the URL: ", err);
        });
    },
    
    goBackParent() {
      if (this.$route.name === 'InquiryDetailKanbanFull' || this.$route.name === 'InquiryDetailKanban') {
        this.$router.push(`/e/kanban/${this.__encryptProjectData(this.getActiveProject?.id, this.getActiveProject.slug)}/${btoa(this.workData?.parent?.slug)}`)
      } else if (this.$route.name === 'InquiryDetailFull' || this.$route.name === 'InquiryDetail') {
        this.$router.push(`/inquiries/${btoa(this.workData?.project?.id)}/${btoa(this.workData?.parent?.slug)}`)
      } else if (this.$route.name === 'InquiryAloneFull') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(this.workData?.parent?.slug)}/f`)
      } else if (this.$route.name === 'InquiryAlone') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(this.workData?.parent?.slug)}`)
      }
    },

    // Handle notes updated from InquiryNotesTab
    handleNotesUpdated(updatedInquiry) {
      // Update the workData with the new notes
      this.workData = { ...this.workData, ...updatedInquiry };

      // Update the inquiry in the store if needed
      if (this.updateInquiryInStore) {
        this.updateInquiryInStore(updatedInquiry);
      }
    },
  },
  mounted() {
    onKeyStroke('Escape', this.close);
    this.fetchUsers()

    // fullscreen check
    if (this.$route.name === 'InquiryDetailKanbanFull') {
      this.isFull = true;
    }
  },
  beforeUnmount () {
    this.resetStoreInquiry();
    this.$store.state.application.mutate = false;
    const roomId = this.workData?.slug;
    this.$soketio.emit('leave', roomId);
  }
};
</script>

<style scoped></style>
