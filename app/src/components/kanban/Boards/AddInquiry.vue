<template>
  <div :id="id" class=" bg-white dark:bg-kb_dark_grey rounded-md p-2 shadow-sm border-gray-100 border-[1px] my-2 relative">
    <!-- Name / Title -->
    <kbInput ref="inputTitle" @blur="blur" @keyup.enter="blur"  v-model="name" :ph="ph" class="w-full pl-4">
    </kbInput>
    <!-- end name -->

    <!-- assignee / due date -->
    <div class="pl-2 mt-4 flex items-center">
      <AssigneeDirect @remove="assigneeRemoved" @click.stop="" :users="users" @select="assigneeSelected" class="w-full"></AssigneeDirect>
      <div class="absolute left-[50px]"><DatePickerInquiry @updateDatepicker="updateDatepicker"></DatePickerInquiry></div>
    </div>
  </div>
</template>

<script>
import {
    mapGetters,
    mapActions
  } from 'vuex';
  import {
    CheckCircleIcon,
  } from '@heroicons/vue/solid';
  import {
    CalendarIcon
  } from '@heroicons/vue/outline';
import { ref, toRaw, onMounted } from 'vue';
import { onKeyStroke } from '@vueuse/core';
import kbInput  from "@/components/kanban/kbInput.vue";
import store from '../../../pages/kanban/store.js';
import TButton from '@/components/global/Button.vue';
import AssigneeDirect  from "@/components/kanban/AssigneeDirect.vue";
import DatePickerInquiry  from "@/components/kanban/DatePickerInquiry.vue";
import inquiriesApi from "@/api/inquiries";



export default {
  name: 'YourComponentName',
  components: {
    kbInput,
    CheckCircleIcon,
    CalendarIcon,
    TButton,
    AssigneeDirect,
    DatePickerInquiry
  },
  props: {
    users: {
      type: Array,
      default: [],
    },
    id: {},
    addDirectly: {},
    colIndex: {},
    position: {
      type: String,
      default: 'top',
    },

  },
  data() {
    return {
      ph: 'Inquiry name',
      due_date: '',
      assign_to: null,
      title: '',
      inquiry: null,
    };
  },
  created() {
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveInquiry: 'application/getActiveInquiry',
    }),
    project() {
      return this.getActiveProject
    },
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      changeStatus: 'application/changeStatus',
      updateInquiryInStore: 'application/updateInquiryInStore2', // Use the safer update method
      resetStore: 'application/resetStore',
      resetStoreInquiry: 'application/resetStoreInquiry',
    }),
    blur(input) {
      if (this.inquiry?.id) {
        this.updateInquiry();
        return
      }
      this.createInquiry();
    },
    assigneeSelected(event) {
      this.assign_to = event?.id
      if (this.inquiry?.id) {
        this.updateInquiry();
      }
    },
    assigneeRemoved() {
      this.assign_to = null
      if (this.inquiry?.id) {
        this.updateInquiry();
      }
    },
    updateDatepicker(date) {
      this.due_date = date ? this.__dateFormatISO(date) : '';
      if (this.inquiry?.id) {
        this.updateInquiry();
      }
    },
    
    createInquiry() {
      if (this.name) {
        const callback = (response) => {
          const data = response.data;
          console.log(data);
          this.inquiry = data; // Store the newly created inquiry
          // Add the new inquiry to the store
          const newInquiry = {
            ... this.$store.state.application.inquiry,
            ...data, // All inquiry properties from the API response
            type:  this.$store.state.application.inquiry.type, // Ensure the inquiry has the correct type
            position: this.position,
            projectName: this.project?.name,
          };
          this.$emit('afterInquiryAdded', newInquiry);
          this.updateInquiryInStore(newInquiry);
          this.resetStoreInquiry()
          this.$store.state.application.inquiry.addDirectly = false;
          this.$emit('hideInquiry', false);
          this.assignInquiry()

        };

        const errCallback = (err) => {
          const message = err?.response?.data?.message;
          this.__showNotif('error', 'Error', message);
          this.$store.state.application.inquiry.addDirectly = false;
          this.$emit('hideInquiry', false);
        };

        let params = {
          title: this.name,
          due_date: this.due_date,
          assign_to: this.assign_to,
          project_id: this.project?.id,
          type: this.$store.state.application.inquiry.type
        };

        inquiriesApi.create(params, callback, errCallback);
      }
    },
    assignInquiry() {
      // Define the callback function that will be called after the API request is successful
      const callback = (response) => {
        const data = response.data;
      };

      // Define the error callback function to handle any errors during the API request
      const errCallback = (err) => {
        const message = err?.response?.data?.message || 'Error occurred while updating the inquiry';
        this.__showNotif('error', 'Error', message);
        // this.handleErrors(err.response.data); //TODO: please check this function, nowhere else to be found in this file/global
      };

      const params = {
        assign_to: this.assign_to,
      }
      // Make the API request to update the inquiry
      inquiriesApi.updateInquiry(this.inquiry?.id, params, callback, errCallback);
    },
    updateInquiry() {
      const callback = (response) => {
        const data = response.data;
        this.inquiry = data
        this.$store.state.application.inquiry.addDirectly = false;
        this.$emit('hideInquiry', false);
        const message = response.message || 'Inquiry updated successfully';
        this.__showNotif('success', 'Success', message);
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      }
      let params = {
        title: this.name,
        assign_to: this.assign_to,
        project_id: this.project?.id,
        type: this.$store.state.application.inquiry.type
      }
      if (this.due_date) params.due_date = this.__dateFormatISO(this.due_date)

      inquiriesApi.update(this.inquiry?.id, params, callback, errCallback)
    },

    close() {
      this.$emit('hideInquiry', false);
    },
  },
  mounted() {
    onKeyStroke('Escape', this.close);
  },
  beforeUnmount() {
	},
};
</script>

<style scoped></style>
