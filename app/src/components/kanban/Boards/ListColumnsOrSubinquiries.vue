<template>
  <div>
    <div
      class="flex flex-row items-center justify-between px-2 py-1 hover:bg-gray-200 border-t-[1px] border-b-[1px] border-gray-200"
      v-for="(item, index) in items"
      :key="index"
      @click.stop="showInquiry(item)"
    >
      <div class="flex items-center">
        <CheckCircleIcon  @click.stop="updateComplete(item)" class="h-6 w-6 pointer mr-1" aria-hidden="true"
        :class="{'text-green-600': item?.status === 'completed'}"></CheckCircleIcon>
        <kbInput
          controlType="subinquiryInput"
          :ref="`subinquiry${index}`"
          v-model="item.title"
          :value="item.title"
          @blur="handleBlur(item, index)"
          @focus="handleFocus(item)"
          @click.stop=""
          @keyupEnter="handleBlur(item, index)"
          :style="{ minWidth: '50px', maxWidth: '400px', width: 'auto' }"
        ></kbInput>
      </div>
      <div class="flex items-center">
        <AssigneeDirectSubInquiry @click.stop="" :inquiry="item" @select="assigneeSelected" @remove="assigneeRemoved" :key="item.assign?.id" class="w-full"
          :currentAssignee="item.assign" :users="users"></AssigneeDirectSubInquiry>
        <ChevronRightIcon @click.stop="showInquiry(item)" class="pointer ml-2 w-4 h-4" />
      </div>
    </div>
    <t-button @click="addColumn()" :color="`primary-white`" class="mt-5 px-2 py-1 mt-1">
      <span class="text-sm">+ {{ mode === "board" ? $t("Column") : $t("Subinquiry") }}</span>
    </t-button>
  </div>
</template>

<script>
import AssigneeDirectSubInquiry from "@/components/kanban/AssigneeDirectSubInquiry.vue";
import store from "../../../pages/kanban/store.js";
import TButton from '@/components/global/Button.vue';
import { ChevronRightIcon } from '@heroicons/vue/outline';
import { CheckCircleIcon } from '@heroicons/vue/solid';
import inquiriesApi from "@/api/inquiries";
import { delay } from '@/libraries/helper';
import {
  mapGetters,
  mapActions
} from 'vuex';

export default {
  components: {
    TButton,
    ChevronRightIcon,
    AssigneeDirectSubInquiry,
    CheckCircleIcon
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    mode: {
      type: String,
      required: true
    },
    parent_id: {
      type: Number,
      required: false
    },
    projectId: {
      type: Number,
      required: false
    },
    users: {},
  },
  data() {
    return {
      itemRefs: [],
      items: [],
      hasMounted: false, // New state to track if component has mounted
      assign_to: null,
      inquiry: null,
    };
  },
  created() {
    this.$soketio.on('inquiry_add', (inquiry) => {
      delay(() => {
        if (inquiry?.parent_id === this.parent_id) {
          // Find if the inquiry already exists in the current items array
          const inquiryIndex = this.items.findIndex(item => item.id === inquiry.id);

          if (inquiryIndex !== -1) {
            // If the inquiry exists, update it
            this.items.splice(inquiryIndex, 1, inquiry);
          } else {
            // If the inquiry doesn't exist, add it to the list
            this.items.push(inquiry);
          }
          // Optionally update the data.sub_inquiries array as well
          this.data.sub_inquiries = Array.from(this.items);
        }
      }, 1000);
    });

    this.$soketio.on('inquiry_delete', (inquiry) => {
      if (inquiry?.parent_id === this.parent_id) {
        // Find the index of the inquiry in the current items array
        const inquiryIndex = this.items.findIndex(item => item.id === inquiry.id);

        if (inquiryIndex !== -1) {
          // If the inquiry exists, remove it from the array
          this.items.splice(inquiryIndex, 1);

          // Optionally update the data.sub_inquiries array as well
          this.data.sub_inquiries = Array.from(this.items);
        }
      }
    });
  },

  mounted() {
    this.initSubinquiry()

    // Set the component as mounted after initialization
    this.$nextTick(() => {
      this.hasMounted = true;
    });
  },
  computed: {
    ...mapGetters({
      getActiveProject: 'application/getActiveProject',
      user: 'auth/user',
      isAdmin: 'auth/isAdmin',
    }),
  },
  methods: {
    ...mapActions({
      updateInquiryInStore: 'application/updateInquiryInStore2', // Use the safer update method
    }),
    initSubinquiry() {
      console.log(this.data);
      this.data.sub_inquiries.forEach((el) => {
        el.interacted = false; // Add the interacted flag
        this.items.push(el);
      });
    },
    assigneeSelected(event, inquiry) {
      this.assign_to = event?.id
      this.inquiry = inquiry
      if (inquiry?.id) {
        this.assignInquiry();
      }
    },
    assignInquiry() {
      // Define the callback function that will be called after the API request is successful
      const callback = (response) => {
        const data = response.data;
        // this.updateInquiryInStore(data);
        this.inquiry = null
      };

      // Define the error callback function to handle any errors during the API request
      const errCallback = (err) => {
        const message = err?.response?.data?.message || 'Error occurred while updating the inquiry';
        this.__showNotif('error', 'Error', message);
        // this.handleErrors(err.response.data);
        this.inquiry = null
      };

      const params = {
        assign_to: this.assign_to,
      }
      // Make the API request to update the inquiry
      inquiriesApi.updateInquiry(this.inquiry?.id, params, callback, errCallback);
    },
    assigneeRemoved(inquiry) {
      this.assign_to = null
      this.inquiry = inquiry
      if (inquiry?.id) {
        this.assignInquiry()
      }
    },
    addColumn() {
      this.items.push({ 
        title: '',
        due_date: '',
        assign_to: '',
        project_id: this.projectId,
        type: this.$store.state.application.inquiry.type,
        parent_id: this.parent_id,
        interacted: false // Add interacted flag for new columns or subinquiries
      });
      this.data.sub_inquiries = Array.from(this.items);
      setTimeout(() => {
        const lastRef = this.$refs[`subinquiry${this.items.length - 1}`];
        lastRef[0].setFocusSectionSubinquiry();
      }, 200);
    },
    delColumn(idx, item) {
      this.confirmDelete(idx, item)
    },
    confirmDelete(idx, item) {
      const callback = (response) => {
          const data = response.data;
          this.items.splice(idx, 1);
          this.data.sub_inquiries = Array.from(this.items);
      }
      const errCallback = (err) => {
          console.log(err)
      }
      inquiriesApi.delete(item.id, callback, errCallback)
    },
    handleBlur(item, index) {
      // Only proceed if the component has mounted and the field has been interacted with
      if (this.hasMounted && item.interacted) {
        if (item.id) {
          // Update the existing inquiry
          this.updateInquiry(item, index);
        } else {
          // Create a new subinquiry
          this.createSubinquiry(item, index);
        }
      }
    },

    openInquiry(item) {
      this.$emit('openInquiry', item)
    },
    handleFocus(item) {
      // Mark the field as interacted when it gains focus
      item.interacted = true;
    },
    updateInquiry(item) {
      const callback = (response) => {
        const data = response.data;
        const inquiryIndex = this.items.findIndex(t => t.id === data.id);
        if (inquiryIndex !== -1) {
          Object.assign(this.items[inquiryIndex], data);
        }
      };
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      };
      item.project = item.project.id;
      inquiriesApi.update(item.id, item, callback, errCallback);
    },
    createSubinquiry(item, index) {
      const callback = (response) => {
        const data = response.data;
        data.interacted = true;
        if (index !== -1) {
          this.items[index] = data;
        }
        // If you also need to update the `data.sub_inquiries` array
        this.data.sub_inquiries = Array.from(this.items);
        this.inquiry = data
        this.assignInquiry()
      };

      const errCallback = (err) => {
        const message = err.response.data.message;
      };

      inquiriesApi.create(item, callback, errCallback);
    },
    showInquiry(inquiry) {
      if (this.$route.name === 'InquiryDetailKanbanFull' || this.$route.name === 'InquiryDetailKanban') {
        this.$router.push(`/e/kanban/${this.__encryptProjectData(this.getActiveProject.id, this.getActiveProject.slug)}/${btoa(inquiry?.slug)}`)
      } else if (this.$route.name === 'InquiryDetailFull' || this.$route.name === 'InquiryDetail') {
        this.$router.push(`/inquiries/${btoa(this.getActiveProject.id)}/${btoa(inquiry.slug)}`)
      } else if (this.$route.name === 'InquiryAloneFull') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(inquiry?.slug)}/f`)
      } else if (this.$route.name === 'InquiryAlone') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(inquiry?.slug)}`)
      }
      this.$store.state.application.inquiry = inquiry
      this.$store.state.application.inquiry.active = inquiry.id;
      this.$store.state.application.inquiry.status = inquiry.status;
      this.$store.state.application.inquiry.type = inquiry.type;
      this.$store.state.application.inquiry.addDirectly = false;
      this.$store.state.application.inquiry.edit = true;
      this.$store.state.application.inquiry.add = false;
      this.$store.state.application.mutate = true;
    },

    updateComplete(inquiry) {
        const callback = (response) => {
          const data = response.data;

          // Merge the updated data with the existing inquiry data
          const finalData = {
            ...this.getActiveInquiry, // Existing inquiry data
            ...data, // New data from the server
          };

          // Update the status in the items array
          const inquiryIndex = this.items.findIndex(item => item.id === inquiry.id);
          if (inquiryIndex !== -1) {
            this.items[inquiryIndex].status = data.status;
          }

          // Optionally, update the data.sub_inquiries array as well
          this.data.sub_inquiries = Array.from(this.items);
        };
        let params = {
          status: !inquiry.status || inquiry.status === 'incomplete' ? 'complete' : 'incomplete'
        }
        // Make the API call to update the inquiry on the server
        inquiriesApi.completeInquiry(inquiry.id, params, callback, (error) => {
          console.error('Error updating inquiry:', error);
        });
      },
  },
  expose: ['validate']
};
</script>
