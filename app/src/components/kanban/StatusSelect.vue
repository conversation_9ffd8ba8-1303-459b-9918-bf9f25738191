<template>
  <div ref="statusWrapper" class="text-sm relative bg-white flex flex-row items-center cursor-pointer">
    <!-- Active board name -->
    <p class="py-2 ml-4 w-full body_l text-kb_black dark:text-white" @click="toggleMenu">
      {{ selItem }}
    </p>
    <!-- chevron down -->
    <svg v-if="!showMenu" @click="toggleMenu" class="mr-3" width="24" height="7" xmlns="http://www.w3.org/2000/svg">
      <path stroke="gray" stroke-width="2" fill="none" d="m1 1 4 4 4-4" />
    </svg>
    <!-- chevron up -->
    <svg v-else @click="toggleMenu" class="mr-3" width="24" height="7" xmlns="http://www.w3.org/2000/svg">
      <path stroke="gray" stroke-width="2" fill="none" d="M9 6 5 2 1 6" />
    </svg>
    <!-- show menu -->
    <div ref="menu" v-if="showMenu"
      class="absolute right-2 top-7 min-w-[150px] z-10 w-full bg-white dark:bg-kb_very_dark_grey rounded-lg shadow-board_menu">
      <a @click="clicked(item)"
        class="block ml-4 first:mt-4 last:mb-4 mt-2 body_l  hover:text-kb_dark_grey dark:hover:text-kb_light_grey"
        href="#" v-for="item in selectItems" :key="item.index">
        {{ item.title || item.name }}
      </a>
    </div>
  </div>
</template>

<script>
import { onClickOutside } from '@vueuse/core';
import { delay } from '@/libraries/helper';
import { mapGetters } from 'vuex';
import inquiriesApi from '@/api/inquiries';
import { toRaw } from 'vue';

export default {
  name: 'StatusSelect',
  props: {
    _mode: {
      type: String,
      required: true
    },
    workDataParent: {
      type: Object,
      required: false
    }
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveBoard: 'application/getActiveBoard',
      getActiveColumn: 'application/getActiveColumn',
      getActiveInquiry: 'application/getActiveInquiry',
      getBoardColsLength: 'application/getBoardColsLength',
    }),
    cols() {
      return this.getActiveBoard?.columns;
    },
  },
  emits: ['change'],
  data() {
    return {
      showMenu: false,
      selectItems: [],
      selItem: '',
      workData: null,
      isShow: true,
      isUpdating: false, // Prevent multiple API calls
    };
  },
  mounted() {
    this.initializeData();
    this.workData = this.workDataParent || JSON.parse(JSON.stringify(toRaw(this.getActiveInquiry)));

    // We are now targeting the wrapper div around the textarea
    const statusWrapperElement = this.$refs.statusWrapper;

    // onClickOutside from VueUse to detect outside clicks
    onClickOutside(statusWrapperElement, () => {
      this.showMenu = false; // Close textarea on outside click
    });
  },
  beforeUnmount() {
    // Clean up any listeners before unmounting the component
    document.removeEventListener('click', this.handleClickOutside);
  },
  created() {
    this.$soketio.on('kanban_update', (kanbanData) => {
      this.fetchKanban();
    });
    // need add additionalInfoAddingColumn
    this.$soketio.on('kanban_add', (data) => {
      this.fetchKanban();
    });
    this.$soketio.on('kanban_delete', (data) => {
      this.fetchKanban();
    });
    // this.$soketio.on('inquiry_reorder', (reorderData) => {
    //   setTimeout(() => {
    //     this.initializeData();
    //     this.workData = JSON.parse(JSON.stringify(toRaw(this.getActiveInquiry)));
    //   }, 200);
    // });
  },
  watch: {
    '$route.params.slug': function(newRoute) {
      this.initializeData()
    },

    // Watch for changes in workDataParent
    workDataParent: {
      handler(newData) {
        if (newData) {
          console.log('WorkDataParent changed:', newData);
          this.workData = newData;
          this.selItem = newData.type || '';
        }
      },
      deep: true,
      immediate: true
    },

    // Watch for changes in active board
    getActiveBoard: {
      handler(newBoard) {
        if (newBoard && newBoard.columns) {
          console.log('Active board changed, reinitializing...');
          this.initializeData();
        }
      },
      deep: true
    }
  },
  methods: {

    // Function to close the textarea when clicked outside
    handleClickOutside(event) {
      const textareaElement = this.$refs.statusWrapper?.$el || this.$refs.statusWrapper;
      if (textareaElement && !textareaElement.contains(event.target)) {
        this.showMenu = false;
        // Remove the click listener after hiding the textarea
        document.removeEventListener('click', this.handleClickOutside);
      }
    },
  fetchKanban() {
    console.log('Fetching kanban data for StatusSelect...');
    this.selectItems = [];

    const board = this.getActiveBoard;
    if (!board || !board.columns) {
      console.warn('No active board or columns found');
      return;
    }

    board.columns.forEach((element, index) => {
      this.selectItems.push({
        name: element.name,
        title: element.title || element.name, // Fallback for display
        index
      });
    });

    console.log('Select items populated:', this.selectItems);

    // Set current selection
    if (!this.getActiveColumn && this.$store.state.application.inquiry?.type) {
      this.selItem = this.$store.state.application.inquiry.type;
    }
  },

  initializeData() {
    console.log('Initializing StatusSelect data...');
    this.selectItems = [];

    const board = this.getActiveBoard;
    if (!board || !board.columns) {
      console.warn('No active board or columns found during initialization');
      return;
    }

    console.log('Active board:', board);

    board.columns.forEach((element, index) => {
      this.selectItems.push({
        name: element.name,
        title: element.title || element.name, // Fallback for display
        index
      });
    });

    console.log('Select items initialized:', this.selectItems);

    // Set current selection from workData or store
    if (this.workData?.type) {
      this.selItem = this.workData.type;
    } else if (this.$store.state.application.inquiry?.type) {
      this.selItem = this.$store.state.application.inquiry.type;
    }

    console.log('Current selection:', this.selItem);
  },
  toggleMenu() {
    this.showMenu = !this.showMenu;
    document.addEventListener('click', this.handleClickOutside);

  },
  clicked(item) {
    console.log('StatusSelect clicked:', item);

    // Prevent multiple API calls
    if (this.isUpdating) {
      console.log('Update already in progress, skipping...');
      return;
    }

    // Store the previous column before updating
    if (item.name !== this.selItem) {
      console.log(`Changing status from "${this.selItem}" to "${item.name}"`);

      const oldType = this.workData.type;
      const newType = item.name;

      this.isUpdating = true;

      // OPTIMISTIC UPDATE: Update UI immediately
      this.selItem = newType;
      this.workData.type = newType;
      this.workData.due_date = this.workData.due_date ? this.__dateFormatISO(this.workData.due_date) : '';

      // Close the menu
      this.showMenu = false;

      // Emit change event immediately for optimistic UI update
      this.$emit('change', {
        title: this.selItem,
        index: this.cols.findIndex(col => col.name === this.selItem || col.title === this.selItem),
        oldType: oldType,
        newType: newType,
        inquiry: this.workData
      });

      // Update the inquiry and handle column changes
      this.$nextTick(() => {
        this.updateInquiry();
      });
    } else {
      console.log('Same status selected, no update needed');
      this.showMenu = false;
    }
  },
  


  updateInquiry() {
    console.log('Updating inquiry status...', this.workData);

    // Store original values for rollback
    const originalType = this.workData.type;
    const originalSelItem = this.selItem;

    // Update the inquiry properties in the store
    const callback = (response) => {
      console.log('✅ Status update successful:', response.data);
      this.isUpdating = false; // Reset the flag

      // Confirm the optimistic update was correct
      const updatedInquiry = response.data;
      if (updatedInquiry) {
        this.workData = { ...this.workData, ...updatedInquiry };
        this.selItem = updatedInquiry.type || this.selItem;
      }
    }

    const errCallback = (err) => {
      console.error('❌ Status update failed:', err);

      // ROLLBACK: Revert optimistic changes
      this.workData.type = originalType;
      this.selItem = originalSelItem;

      // Emit rollback event
      this.$emit('change', {
        title: originalSelItem,
        index: this.cols.findIndex(col => col.name === originalType || col.title === originalType),
        rollback: true,
        oldType: originalType,
        newType: this.workData.type,
        inquiry: this.workData
      });

      this.isUpdating = false; // Reset the flag
      const message = err.response?.data?.message || 'Error updating inquiry status';
      this.__showNotif('error', 'Error', message);
    }

    // Find the target column
    const columnIndex = this.cols.findIndex(col => col.name === this.workData.type);

    if (columnIndex === -1) {
      console.error('Target column not found:', this.workData.type);
      this.isUpdating = false;
      return;
    }

    // Prepare the reorder payload
    const payload = {
      type: this.workData.type,
      inquiry_id: this.workData.id,
      inquiry_ids: [this.workData.id] // Just this inquiry for status change
    };

    console.log('Sending reorder request:', payload);
    inquiriesApi.reorder(payload, callback, errCallback);
  },
}
};
</script>

<style scoped>
</style>
