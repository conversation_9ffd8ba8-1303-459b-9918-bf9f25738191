
<template>
  <div class="bg-white w-full border-[1px] border-gray-200 rounded-xl h-full">
      <div class="flex items-center p-4">
        <div class="font-medium text-lg ml-2">
          {{ $t('My Project') }}
        </div>
        <Menu
          as="div"
          class="ml-8 relative"
        >
          <div>
            <MenuButton class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none">
              {{ selectedFilter }} <ChevronDownIcon class="h-4 w-4 ml-2 text-gray-400"></ChevronDownIcon>
            </MenuButton>
          </div>
          <transition
            enterActiveClass="transition ease-out duration-100"
            enterFromClass="transform opacity-0 scale-95"
            enterToClass="transform opacity-100 scale-100"
            leaveActiveClass="transition ease-in duration-75"
            leaveFromClass="transform opacity-100 scale-100"
            leaveToClass="transform opacity-0 scale-95"
          >
            <MenuItems class="origin-top-right absolute right-0 mt-2 w-28 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
              <MenuItem
                @click="filter('recent')"
                v-slot="{ active }"
              >
                <a
                  href="#"
                  :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                >Recent</a>
              </MenuItem>
              <MenuItem
              @click="filter('alphabetical')"
                v-slot="{ active }"
              >
                <a
                  href="#"
                  :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                >Alphabetical</a>
              </MenuItem>
              <MenuItem
                @click="filter('pre')"
                v-slot="{ active }"
              >
                <a
                  href="#"
                  :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                >Pre</a>
              </MenuItem>
              <MenuItem
                @click="filter('active')"
                v-slot="{ active }"
              >
                <a
                  href="#"
                  :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                >Active</a>
              </MenuItem>
              <MenuItem
                @click="filter('completed')"
                v-slot="{ active }"
              >
                <a
                  href="#"
                  :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                >Completed</a>
              </MenuItem>
            </MenuItems>
          </transition>
        </Menu>
      </div>
      <div class="mt-4 min-h-[96px]">
        <div>
          <div v-for="project in projects" :key="project.id" @click="gotoDetail(project)" class="pointer flex justify-between py-[12px] border-gray-200" :class="{'border-b-[1px]': projects.length >  1}">
            <div class="flex px-4">
              <div class="ml-2 font-medium text-gray-600">{{ project.name }}</div>
            </div>
            <div class="px-4">
              <div v-if="project?.status === 'pre'"  class="bg-[#FAB312] rounded-lg px-4 py-[2px] text-xs text-white">Pre</div>
              <div v-if="project?.status === 'active'" class="bg-[#14b8a6] rounded-lg px-4 py-[2px] text-xs text-white">Active</div>
              <div v-if="project?.status === 'waiting_for_client'" class="bg-[#67E2AE] rounded-lg px-4 py-[2px] text-xs text-white">Waiting</div>
              <div v-if="project?.status === 'hold'" class="bg-[#F05b5b] rounded-lg px-4 py-[2px] text-xs text-white">Hold</div>
              <div v-if="project?.status === 'completed'" class="bg-[#2563EB] rounded-lg px-4 py-[2px] text-xs text-white">Completed</div>
              <div v-if="project?.status === 'incoming'" class="bg-[#7610dd] rounded-lg px-4 py-[2px] text-xs text-white">Incoming</div>
            </div>
          </div>
          <div class="p-4 font-medium flex justify-center" v-if="!projects.length">{{ $t('No records found') }}</div>
        </div>
      </div>
      <div class="text-gray-400 font-medium p-4 text-sm pointer"  v-if="projects.length >= 4" @click="$router.push('/projects')">{{ $t('See all Projects') }}</div>
      <ModalGeneral :isShow="isShowModalClientPre" @update:isShow="isShowModalClientPre = $event">
        <template #header>
          <p class="text-base font-medium text-gray-800 dark:text-white">
            {{$t('Project Details')}}
          </p>
        </template>
        <template #body>
          <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
          <p class="text-sm text-gray-800 dark:text-neutral-400"> {{$t('Admin still reviewing the quotation for this event')}} </p>
          <!-- </div> -->
        </template>
        <template #footer>
          <t-button class="px-6" :color="`primary-solid`" @click="isShowModalClientPre = false;">
            {{ $t('Ok') }}
          </t-button>
        </template>
      </ModalGeneral>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import projectApi from "@/api/project";
import ModalGeneral from "@/components/modal/ModalGeneral.vue";
// Components
import {
	Menu,
	MenuButton,
	MenuItem,
	MenuItems,
} from '@headlessui/vue';
import {
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    DotsHorizontalIcon,
    ThumbUpIcon,
    CheckCircleIcon,
    CalendarIcon,
    ChevronDownIcon,
  } from '@heroicons/vue/outline';
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
	components: {
    CheckCircleIcon,
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
    ChevronDownIcon,
    ModalGeneral,
	},
	props: {
	},
	data() {
		return {
      selectedFilter: 'Recent',
      projects: [],
      order_by: 'created_at',
      isShowModalClientPre: false,
		};
	},
	computed: {
    ...mapGetters({
      user: 'auth/user',
      isFetchingUser: 'auth/isFetchingUser',
      isAdmin: 'auth/isAdmin',
      isClient: 'auth/isClient',
      getActiveProject: 'application/getActiveProject',
    }),
  },
	watch: {},
	created() {},
	mounted() {
    this.getProjects()
  },
	beforeUnmount() {},
	methods: {
    ...mapActions({
        resetStore: 'application/resetStore',
      }),
    handleAvatarError(itemWithError) {
      // Find the index of the item in the items array
      const index = this.items.findIndex(item => item.email === itemWithError.email);
      // If the item is found, update its avatarError property
      if (index !== -1) {
        const item = this.items[index];
        item.imgUrl = null;
        Object.assign(this.items[index], item);
      }
    },
    filter(filter) {
      this.getProjects(filter)
    },
    getProjects(filter) {
				const callback = (response) => {
					const data = response.data;
					this.projects = data;
          if (!this.projects.length) this.$emit('emptyDaya')
				}
				const errCallback = (err) => {
          const message = err?.response?.data?.message;
          this.__showNotif('error', 'Error', message);
				}
        let status = ''
        if (filter === 'recent') this.order_by = 'created_at';
        if (filter === 'alphabetical') this.order_by = 'name';
        if (filter === 'pre') status = 'pre';
        if (filter === 'active') status = 'active';
        if (filter === 'completed') status = 'completed';
				const params = {
					order_by: this.order_by,
					sort_by: 'desc',
					page: 1,
					limit: 6,
          status,
				}
				projectApi.getList(params, callback, errCallback)
			},
      gotoDetail(item) {
        this.resetStore()
        
        
        if (item.status === 'pre' && !this.isAdmin) this.isShowModalClientPre = true
        if (item.status === 'review' && !this.isAdmin) this.isShowModalClientPre = true
        
        if (item.status === 'waiting_for_client' && this.isClient) this.$router.push({
          name: 'EventQuotationClient',
          params: {
            id: item.id
          }
        });

        if (item.status === 'active' || item.status === 'completed') this.$router.push({
          name: 'EventKanban',
          params: {
            id: this.__encryptProjectData(item.id, item.slug)
          }
        });

        if (item.status === 'active_revision') this.$router.push({
          name: 'EventKanban',
          params: {
            id: this.__encryptProjectData(item.id, item.slug)
          }
        });

        if (item.status === 'waiting_for_client' && !this.isClient) this.$router.push({
          name: 'EventQuotation',
          params: {
            id: item.id
          }
        });

        if (item.status === 'hold' && this.isClient) this.$router.push({
          name: 'EventQuotationClient',
          params: {
            id: item.id
          }
        });
        else if ((item.status === 'pre' || item.status === 'review') && !this.isClient) this.$router.push({ name: 'EventQuotation', params: { id: item.id } });
        
        if (item.status === 'hold' && !this.isClient) this.$router.push({ name: 'EventQuotation', params: { id: item.id } });

      },
  },
};
</script>