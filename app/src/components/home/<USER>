
<template>
  <div class="bg-white w-full border-[1px] border-gray-200 rounded-xl h-full">
    <div class="flex items-center p-4 ">
        <img v-if="user && user?.imgUrl" class="rounded-full size-9 object-cover" :src="user?.imgUrl" alt="avatar-image"
          referrerpolicy="no-referrer" @error="handleAvatarError(user)">
        <div v-else>
          <div
            class="text-sm rounded-full h-9 w-9 font-medium pt-[6px] text-center bg-white text-black uppercase border-[1px] border-gray-200">
            {{ __generateInitial(user?.first_name) }}
          </div>
        </div>
        <div class="font-medium text-lg ml-2">
          {{ $t('My Task') }}
        </div>
    </div>
    <div v-if="inquiriesDueDate.length || inquiriesUpComing.length" class="border-b border-gray-200">
      <nav class="flex gap-x-3" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
        <button @click="isUpcoming = true; isOverDue = false" type="button" class="ml-4 hs-tab-active:font-semibold hs-tab-active:border-primary-600 hs-tab-active:text-primary-600 py-4 px-1 inline-flex items-center gap-x-2 border-b-2 border-transparent text-sm whitespace-nowrap text-gray-500 hover:text-primary-600 focus:outline-none focus:text-primary-600 disabled:opacity-50 disabled:pointer-events-none active" id="tabs-with-underline-item-1" aria-selected="true" data-hs-tab="#tabs-with-underline-1" aria-controls="tabs-with-underline-1" role="tab">
          Upcoming
        </button>
        <button @click="isUpcoming = false; isOverDue = true" type="button" class="hs-tab-active:font-semibold hs-tab-active:border-primary-600 hs-tab-active:text-primary-600 py-4 px-1 inline-flex items-center gap-x-2 border-b-2 border-transparent text-sm whitespace-nowrap text-gray-500 hover:text-primary-600 focus:outline-none focus:text-primary-600 disabled:opacity-50 disabled:pointer-events-none" id="tabs-with-underline-item-2" aria-selected="false" data-hs-tab="#tabs-with-underline-2" aria-controls="tabs-with-underline-2" role="tab">
          Overdue
        </button>
      </nav>
      </div>
      <div class="mt-3">
        <div id="tabs-with-underline-1" role="tabpanel" aria-labelledby="tabs-with-underline-item-1">
          <div v-for="inquiry in inquiries" :key="inquiry.id" v-if="inquiries && inquiries.length" class="flex justify-between mb-2 py-2 border-b-[1px] border-gray-200 pointer" @click="goToDetail(inquiry)">
            <div class="flex px-4">
              <CheckCircleIcon :class="{'text-green-600': inquiry.status === 'completed'}" @click.stop="updateComplete(inquiry)" class="h-6 w-6 pointer text-gray-400" aria-hidden="true"></CheckCircleIcon>
              <div class="ml-2 font-medium text-gray-600 truncate">{{inquiry.name}}</div>
            </div>
            <div class="px-4">
              <div v-if="inquiry?.type === 'Back-log'" class="w-[15px] h-[15px] bg-[#a8a8a8] rounded-full"></div>
              <div v-if="inquiry?.type === 'Pre Production'" class="w-[15px] h-[15px] bg-[#12c789] rounded-full"></div>
              <div v-if="inquiry?.type === 'Production'" class="w-[15px] h-[15px] bg-[#67E2AE] rounded-full"></div>
              <div v-if="inquiry?.type === 'Post Production'" class="w-[15px] h-[15px] bg-[#debe0b] rounded-full"></div>
              <div v-if="inquiry?.type === 'Revision'" class="w-[15px] h-[15px] bg-[#ff7b1a] rounded-full"></div>
              <div v-if="inquiry?.type === 'Complete'" class="w-[15px] h-[15px] bg-[#7610dd] rounded-full"></div>
            </div>
          </div>
          <div class="text-center my-3 font-medium min-h-[80px] pt-4" :class="{'pt-6': inquiriesDueDate.length || inquiriesUpComing.length}" v-else>{{ $t('No records found') }}</div>
        </div>
        <div id="tabs-with-underline-2" class="hidden" role="tabpanel" aria-labelledby="tabs-with-underline-item-2">
          <div v-for="inquiry in inquiries" :key="inquiry.id" v-if="inquiries && inquiries.length" class="flex justify-between mb-2 py-2 border-b-[1px] border-gray-200 pointer" @click="goToDetail(inquiry)">
            <div class="flex px-4">
              <CheckCircleIcon :class="{'text-green-600': inquiry.status === 'completed'}" @click.stop="updateComplete(inquiry)" class="h-6 w-6 pointer text-gray-400" aria-hidden="true"></CheckCircleIcon>
              <div class="ml-2 font-medium text-gray-600 truncate">{{inquiry.name}}</div>
            </div>
            <div class="px-4">
              <div v-if="inquiry?.type === 'Back-log'" class="w-[15px] h-[15px] bg-[#a8a8a8] rounded-full"></div>
              <div v-if="inquiry?.type === 'Pre Production'" class="w-[15px] h-[15px] bg-[#12c789] rounded-full"></div>
              <div v-if="inquiry?.type === 'Production'" class="w-[15px] h-[15px] bg-[#67E2AE] rounded-full"></div>
              <div v-if="inquiry?.type === 'Post Production'" class="w-[15px] h-[15px] bg-[#debe0b] rounded-full"></div>
              <div v-if="inquiry?.type === 'Revision'" class="w-[15px] h-[15px] bg-[#ff7b1a] rounded-full"></div>
              <div v-if="inquiry?.type === 'Complete'" class="w-[15px] h-[15px] bg-[#7610dd] rounded-full"></div>
            </div>
          </div>
          <div class="text-center my-3 font-medium min-h-[80px] pt-4" :class="{'pt-6': inquiriesDueDate.length || inquiriesUpComing.length}" v-else>{{ $t('No records found') }}</div>
        </div>
      </div>
      <div class="text-gray-400 font-medium px-4 pt-2 pb-4 text-sm pointer"  v-if="inquiries.length" @click="$router.push('/inquiries')">{{ $t('Show More') }}</div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import inquiriesApi from "@/api/inquiries";
import {
    HSStaticMethods
} from "preline";
import {
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    DotsHorizontalIcon,
    ThumbUpIcon,
    CheckCircleIcon,
    CalendarIcon
  } from '@heroicons/vue/outline';
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
	components: {
    CheckCircleIcon,
	},
	props: {
	},
	data() {
		return {
      isOverDue: false,
      isUpcoming: true,
      inquiries: [],
      inquiriesDueDate: [],
      inquiriesUpComing: [],
		};
	},
	computed: {
    ...mapGetters({
			user: 'auth/user',
		}),
  },
	watch: {
    isUpcoming() {
      this.getInquiries()
    }
  },
	created() {},
	mounted() {
    this.getInquiries()
    this.getInquiriesDuedate()
    this.getInquiriesUpComing()
  },
	beforeUnmount() {},
	methods: {
    ...mapActions({
      showDetailInquiry: 'application/showDetailInquiry',
      resetStore: 'application/resetStore',
    }),
    handleAvatarError(itemWithError) {
      // Find the index of the item in the items array
      const index = this.items.findIndex(item => item.email === itemWithError.email);
      // If the item is found, update its avatarError property
      if (index !== -1) {
        const item = this.items[index];
        item.imgUrl = null;
        Object.assign(this.items[index], item);
      }
    },
    async getInquiries() {
      this.isFetching = true;
      const callback = (response) => {
        const data = response.data;
        this.inquiries = data;
        this.isFetching = false;
        setTimeout(() => {
              HSStaticMethods.autoInit();
          }, 500);

      };
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isFetching = false;
      };
      let params = {
        order_by: 'created_at',
        sort_by: 'asc',
        limit: 10,
      }
      if (this.isUpcoming) params.isUpcoming = 1
      if (this.isOverDue) params.isOverDue = 1
      inquiriesApi.getList(params, callback, errCallback);
    },
    async getInquiriesDuedate() {
      this.isFetching = true;
      const callback = (response) => {
        const data = response.data;
        this.inquiriesDueDate = data;
        this.isFetching = false;
        setTimeout(() => {
              HSStaticMethods.autoInit();
          }, 500);
      };
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isFetching = false;
      };
      let params = {
        order_by: 'created_at',
        sort_by: 'asc',
        limit: 1,
      }
      params.isOverDue = 1
      inquiriesApi.getList(params, callback, errCallback);
    },
    async getInquiriesUpComing() {
      this.isFetching = true;
      const callback = (response) => {
        const data = response.data;
        this.inquiriesUpComing = data;
        this.isFetching = false;
        setTimeout(() => {
              HSStaticMethods.autoInit();
          }, 500);
      };
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isFetching = false;
      };
      let params = {
        order_by: 'created_at',
        sort_by: 'asc',
        limit: 1,
      }
      params.isUpcoming = 1
      inquiriesApi.getList(params, callback, errCallback);
    },
    updateComplete(inquiry) {
        const callback = (response) => {
          const data = response.data;
          const index = this.inquiries.findIndex(item => item.id === data.id);
          if (index !== -1) {
            // Replace the old item with the updated one
            Object.assign(this.inquiries[index], data);
          } else {
            console.log('Item not found');
          }
        };
        let params = {
          status: !inquiry.status || inquiry.status === 'incomplete' ? 'complete' : 'incomplete'
        }

        // Make the API call to update the inquiry on the server
        inquiriesApi.completeInquiry(inquiry.id, params, callback, (error) => {
          console.error('Error updating inquiry:', error);
        });
      },
      goToDetail(inquiry) {
        this.resetStore()
        const baseURL = import.meta.env.VITE_APP_URL; // Get the base URL from environment variables
        this.showDetailInquiry(inquiry.slug);
        this.$router.push(`/inquiries/${btoa(inquiry?.project?.id)}/${btoa(inquiry.slug)}`) // Combine base URL and current route
    }
  },
};
</script>