<template>
	<input
		class="vue-scrubber"
		@focus="onFocus"
		@blur="onBlur"
		@mousedown="handleMouseDown"
		@keypress="handleInput"
		@keydown.up="handleKeyCodeUp"
		@keyup="handleKeyUp"
		@keydown.down="handleKeyCodeDown"
		@change="handleChange"
		@keyup.enter="handleInputEnter"
		:value="constrainedValue"
	/>
</template>

<script>
export default {
	name: '<PERSON><PERSON><PERSON><PERSON>',
	// props that the scrubber can receive
	// value: value model
	// min: minimum value
	// max: maximum value
	// steps: increments for each pixel the mouse is moved
	props: {
		value: {},
		min: {
			type: Number,
			default: 1,
		},
		max: {
			default: 100,
		},
		steps: {
			type: Number,
			default: 1,
		},
		allowNegative: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			isMouseDown: false,
			initialMouse: null,
			// value: 0,
		};
	},
	computed: {
		// returns the number of decimals based on the step value
		// e.g. "0.25" returns "2"
		decimals() {
			return (
				this.steps.toString().substr(this.steps.toString().indexOf('.'))
					.length - 1
			);
		},

		// every time the value changes, we need to make sure it stays inside the min/max
		constrainedValue() {
			return this.constrain(
				this.value,
				this.min,
				this.max,
				this.decimals,
			);
		},
	},

	// the template

	methods: {
		// constrains a number to not exceed the min/max
		// decimals: rounding precision
		constrain(value, min, max, decimals) {
			// eslint-disable-next-line no-param-reassign
			decimals = typeof decimals !== 'undefined' ? decimals : 0;

			// eslint-disable-next-line
			if (min != undefined && max != undefined) {
				return this.round(
					Math.min(Math.max(parseFloat(value), min), max),
					decimals,
				);
			}
			return value;
		},

		// method to round a number to given decimals
		round(value, decimals) {
			return Number(`${Math.round(`${value}e${decimals}`)}e-${decimals}`);
		},

		handleInput(event) {
			// only allow numeric keys
			if (!this.allowNegative) {
				if (event.keyCode < 48 || event.keyCode > 57) {
					event.preventDefault();
				}
			} else if (event.keyCode === 47 || event.keyCode === 46 || event.keyCode < 45 || event.keyCode > 57) {
				event.preventDefault();
			}
		},
		handleKeyUp(event) {
			// prevent user input extreme number
			if (event.target.value >= this.max) {
			// eslint-disable-next-line
				event.target.value = this.max;
			}
		},
		handleChange(event) {
			// eslint-disable-next-line no-restricted-globals
			const value = isNaN(parseFloat(event.target.value)) ? 0 : parseFloat(event.target.value);
			this.updateValue(value);
		},
		handleInputEnter(event) {
			event.preventDefault();
			// eslint-disable-next-line no-restricted-globals
			let value = isNaN(parseFloat(event.target.value)) ? 0 : parseFloat(event.target.value);
			if (value < this.min) {
				value = this.min;
			}
			// set to min negative number
			if (value < 0 && value < this.min) {
				value = this.min;
			}
			this.$emit('keyenter', value);
		},
		handleKeyCodeUp(event) {
			event.preventDefault();
			// this.value += parseFloat(this.steps);
			// eslint-disable-next-line no-restricted-globals
			const value = this.value + isNaN(parseFloat(this.steps)) ? 0 : parseFloat(this.steps);
			this.updateValue(value);
		},

		handleKeyCodeDown(event) {
			event.preventDefault();
			// this.value -= parseFloat(this.steps);
			// eslint-disable-next-line no-restricted-globals
			const value = this.value - isNaN(parseFloat(this.steps)) ? 0 : parseFloat(this.steps);
			this.updateValue(value);
		},

		// mouse handler
		handleMouseDown(event) {
			// enable scrubbing
			this.mouseDown = true;

			// remember the initial mouse position when the scubbing started
			this.initialMouse = {
				x: event.clientX,
				y: event.clientY,
			};

			// remember the initial value
			// this.initialValue = this.value;

			// register global event handlers because now we are not bound to the component anymore
			document.addEventListener('mousemove', this.handleMouseMove);

			// global mouse up listener
			document.addEventListener('mouseup', this.handleMouseUp);
		},
		handleMouseUp() {
			// disable scrubbing
			this.mouseDown = false;

			document.removeEventListener('mousemove', this.handleMouseMove);
			document.removeEventListener('mouseup', this.handleMouseUp);
		},

		// the actual translation of mouse movement to value change…
		handleMouseMove(event) {
			// scrub if the mouse is being pressed
			if (this.mouseDown) {
				const newValue = this.value + (((event.clientX - this.initialMouse.x) * this.steps) / 25);

				// constrain the value to the min/max
				const value = this.constrain(
					newValue,
					this.min,
					this.max,
					this.decimals,
				);

				this.updateValue(value);
			}
		},

		updateValue(value) {
			this.$emit('input', value);
		},

		onFocus() {
			this.$emit('focus');
		},

		onBlur() {
			this.$emit('blur');
		},
	},
};

// var app = new Vue({
//   el: "#app",
//   data: {
//     min: 0,
//     max: 200,
//     steps: 1,
//     value: 100,
//     demo: "<scrubber :min='10' :max='100' :steps='1' :value='50'></scrubber>"
//   }
// })
</script>

<style scoped>
.vue-scrubber {
	cursor: ew-resize;
}
</style>
