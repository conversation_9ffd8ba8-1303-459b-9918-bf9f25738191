<template>
    <Multiselect v-model="localSelectedUsers" :options="users" :multiple="true" track-by="id" label="name"
        placeholder="Select User" :custom-label="customLabel" id="assigned-user" @update:modelValue="updateSelectedUsers"
        class="text-sm user-multiselect">
        <template #tag="{ option, remove }">
            <div class="selected-item">
                <img :src="getUserImage(option)" class="user-avatar" alt="Avatar" />
                <span class="user-name">{{ option.first_name }}</span>
                <t-button type="button" class="remove-button" @click="remove(option)">x</t-button>
            </div>
        </template>
        <template #option="{ option }">
            <div class="option-item">
                <img :src="getUserImage(option)" class="user-avatar" alt="Avatar" />
                <span class="user-name">{{ option.first_name }}</span>
            </div>
        </template>
    </Multiselect>
</template>

<script>
    import Multiselect from 'vue-multiselect';
    import "vue-multiselect/dist/vue-multiselect.css";

    export default {
        components: {
            Multiselect
        },
        props: {
            users: {
                type: Array,
                required: true,
            },
            selectedUsers: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                localSelectedUsers: this.selectedUsers,
            };
        },
        watch: {
            selectedUsers(newVal) {
                this.localSelectedUsers = newVal;
            },
        },
        methods: {
            customLabel({
                name
            }) {
                return name;
            },
            updateSelectedUsers(value) {
                console.log(value)
                this.$emit('updateSelected', value);
            },
            getUserImage(user) {
                if (user.img_url) {
                    return user.img_url;
                } else {
                    return this.generateInitialPlaceholder(user.first_name);
                }
            },
            generateInitialPlaceholder(name) {
                const initials = name
                    .split(' ')
                    .map((part) => part.charAt(0))
                    .join('')
                    .toUpperCase();

                const canvas = document.createElement('canvas');
                canvas.width = 32;
                canvas.height = 32;
                const ctx = canvas.getContext('2d');

                // Draw background
                ctx.fillStyle = '#ccc'; // Placeholder background color
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw initials
                ctx.fillStyle = '#555'; // Text color
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(initials, canvas.width / 2, canvas.height / 2);

                return canvas.toDataURL();
            },
        },
    };
</script>

<style scoped>
    .user-multiselect-container {
        max-width: 100%;
        margin: 20px auto;
    }

    .user-multiselect-label {
        font-weight: bold;
        margin-bottom: 8px;
        display: block;
    }

    .user-multiselect {
        width: 100%;
    }

    .selected-item {
        display: flex;
        align-items: center;
        background-color: #f0f0f0;
        border-radius: 20px;
        padding: 5px 10px;
        margin-right: 8px;
        margin-bottom: 5px;
    }

    .selected-item img,
    .selected-item span,
    .selected-item button {
        margin-right: 5px;
    }

    .selected-item button {
        margin-right: 0;
    }

    .multiselect__tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 5px 10px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 8px;
        object-fit: cover;
    }

    .user-name {
        font-size: 14px;
        color: #333;
    }

    .remove-button {
        background: none;
        border: none;
        cursor: pointer;
        margin-left: 8px;
        font-size: 14px;
        color: #ff4d4f;
    }

    .remove-button:hover {
        color: #ff0000;
    }

    .note {
        font-size: 12px;
        color: #888;
        margin-top: 8px;
        text-align: left;
    }
</style>