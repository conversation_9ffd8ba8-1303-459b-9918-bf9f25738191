<template>
	<input
		class="vue-scrubber"
		@focus="onFocus"
		@blur="onBlur"
		@mousedown="handleMouseDown"
		@keypress="handleInput"
		@keydown.up="handleKeyCodeUp"
		@keydown.down="handleKeyCodeDown"
		@keydown.enter="handleEnter"
		@change="handleChange"
		:value="constrainedValue"
	/>
</template>

<script>
// import MaskedInput from 'vue-masked-input';

export default {
	name: 'ScrubberMasked',
	// props that the scrubber can receive
	// value: value model
	// min: minimum value
	// max: maximum value
	// steps: increments for each pixel the mouse is moved
	components: {
		// MaskedInput,
	},
	props: {
		value: {
			default: '00:00:00',
		},
		min: {
			type: String,
			default: '1',
		},
		max: {
			default: '216000',
		},
		steps: {
			type: Number,
			default: 1,
		},
	},
	data() {
		return {
			isMouseDown: false,
			initialMouse: null,
			// value: 0,
		};
	},
	computed: {
		// returns the number of decimals based on the step value
		// e.g. "0.25" returns "2"
		decimals() {
			return (
				this.steps.toString().substr(this.steps.toString().indexOf('.'))
					.length - 1
			);
		},

		// every time the value changes, we need to make sure it stays inside the min/max
		constrainedValue() {
			let defaultValue = this.stringToSeconds('00:00:00');
			if (this.value) {
				defaultValue = this.stringToSeconds(this.value);
			}
			const value = this.constrain(
				defaultValue,
				parseInt(this.min),
				parseInt(this.max),
				this.decimals,
			);

			const finalValue = this.secondToString(value);
			return finalValue;
		},
	},

	// the template

	methods: {
		// constrains a number to not exceed the min/max
		// decimals: rounding precision
		constrain(value, min, max, decimals) {
			// eslint-disable-next-line no-param-reassign
			decimals = typeof decimals !== 'undefined' ? decimals : 0;

			// eslint-disable-next-line eqeqeq
			if (min != undefined && max != undefined) {
				return this.round(
					Math.min(Math.max(parseFloat(value), min), max),
					decimals,
				);
			}
			const finalValue = this.secondToString(value);

			return finalValue;
		},

		// method to round a number to given decimals
		round(value, decimals) {
			const numDec = `${value}e${decimals}`;
			const epsDec = `e-${decimals}`;
			const numRound = Number(`${Math.round(numDec)}${epsDec}`);
			return numRound;
		},

		handleInput(event) {
			// only allow numeric keys
			if (event.keyCode < 48 || event.keyCode > 57) {
				event.preventDefault();
			}
		},

		handleChange(event) {
			// eslint-disable-next-line no-restricted-globals
			const value = isNaN(parseFloat(this.stringToSeconds(event.target.value))) ? 0 : parseFloat(this.stringToSeconds(event.target.value));
			this.updateValue(value);
		},
		handleEnter(event) {
			event.preventDefault();
			// eslint-disable-next-line no-restricted-globals
			const value = isNaN(parseFloat(this.stringToSeconds(event.target.value))) ? 0 : parseFloat(this.stringToSeconds(event.target.value));
			this.updateValueEnter(value);
		},
		handleKeyCodeUp(event) {
			event.preventDefault();
			// this.stringToSeconds(this.value) += parseFloat(this.steps);
			const value = this.stringToSeconds(this.value) + parseFloat(this.steps);
			this.updateValue(value);
		},

		handleKeyCodeDown(event) {
			event.preventDefault();
			// this.stringToSeconds(this.stringToSeconds(this.value)) -= parseFloat(this.steps);
			const value = this.stringToSeconds(this.value) - parseFloat(this.steps);
			this.updateValue(value);
		},

		// mouse handler
		handleMouseDown(event) {
			// enable scrubbing
			this.mouseDown = true;
			// remember the initial mouse position when the scubbing started
			this.initialMouse = {
				x: event.clientX,
				y: event.clientY,
			};
			// remember the initial value
			// this.initialValue = this.stringToSeconds(this.value);
			// register global event handlers because now we are not bound to the component anymore
			document.addEventListener('mousemove', this.handleMouseMove);
			// global mouse up listener
			document.addEventListener('mouseup', this.handleMouseUp);
		},
		handleMouseUp() {
			// disable scrubbing
			this.mouseDown = false;
			document.removeEventListener('mousemove', this.handleMouseMove);
			document.removeEventListener('mouseup', this.handleMouseUp);
		},

		// the actual translation of mouse movement to value change…
		handleMouseMove(event) {
			// scrub if the mouse is being pressed
			if (this.mouseDown) {
				const newValue = this.stringToSeconds(this.value) + (((event.clientX - this.initialMouse.x) * this.steps) / 25);

				// constrain the value to the min/max
				const value = this.constrain(
					newValue,
					this.min,
					this.max,
					this.decimals,
				);

				this.updateValue(value);
			}
		},

		updateValue(value) {
			const finalValue = this.secondToString(value);
			this.$emit('input', finalValue);
		},
		updateValueEnter(value) {
			const finalValue = this.secondToString(value);
			this.$emit('keyenter', finalValue);
		},
		onFocus() {
			this.$emit('focus');
		},

		onBlur() {
			this.$emit('blur');
		},
		onEnter() {
			this.$emit('keyenter');
		},
		secondToString(value) {
			const secNum = parseInt(value); // don't forget the second param
			let hours = Math.floor(secNum / 3600);
			let minutes = Math.floor((secNum - (hours * 3600)) / 60);
			let seconds = secNum - (hours * 3600) - (minutes * 60);
			if (hours < 10) { hours = `0${hours}`; }
			if (minutes < 10) { minutes = `0${minutes}`; }
			if (seconds < 10) { seconds = `0${seconds}`; }
			return `${hours}:${minutes}:${seconds}`;
		},
		stringToSeconds(sec) {
			const hms = sec; // your input string
			const a = hms.split(':'); // split it at the colons
			// eslint-disable-next-line no-restricted-globals
			const first = isNaN((+a[0]) * 60 * 60) ? 0 : (+a[0]) * 60 * 60;
			// eslint-disable-next-line no-restricted-globals
			const second = isNaN((+a[1]) * 60 * 60) ? 0 : (+a[1]) * 60;
			// eslint-disable-next-line no-restricted-globals
			const third = isNaN((+a[2])) ? 0 : (+a[2]);
			// minutes are worth 60 seconds. Hours are worth 60 minutes.
			const seconds = first + second + third;
			return seconds;
		},
	},
};

// var app = new Vue({
//   el: "#app",
//   data: {
//     min: 0,
//     max: 200,
//     steps: 1,
//     value: 100,
//     demo: "<scrubber :min='10' :max='100' :steps='1' :value='50'></scrubber>"
//   }
// })
</script>

<style scoped>
.vue-scrubber {
	cursor: ew-resize;
}
</style>
