<template>
    <div>
        <div :id="id"
            class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 end-0 transition-all duration-300 transform size-full flex-1 flex flex-col sm:w-[400px] z-[80] bg-white border-s"
            role="dialog" tabindex="-1" aria-labelledby="hs-pro-dutoo-label">
            <!-- Close Button -->
            <div class="absolute top-2 end-4 z-10">
                <button type="button"
                    class="size-6 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none"
                    aria-label="Close" :data-hs-overlay="`#${id}`">
                    <span class="sr-only">Close</span>
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" /></svg>
                </button>
            </div>
            <!-- End Close Button -->

            <!-- Header -->
            <div>
                <slot name="header"></slot>
            </div>
            <!-- End Header -->

            <!-- Body -->
            <div class="h-full overflow-y-auto">
                <slot name="body"></slot>
            </div>
            <!-- End Body -->

            <!-- Footer -->
            <div>
                <slot name="footer"></slot>
                
            </div>
            
            <!-- End Footer -->
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            id: {
                type: String,
                default: 'hs-hinoo-dutoo',
            },
        },
        watch: {},
        created() {},
        methods: {},
    };
</script>