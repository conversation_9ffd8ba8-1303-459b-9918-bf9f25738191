<template>
	<textarea
		:rows="rows"
		:class="theClass"
		:name="name"
		:value="value"
		:placeholder="placeholder"
		:autocomplete="autocomplete"
		:disabled="isDisabled"
		@input="onInput"
		@change="onChange"
	/>
</template>

<script>
export default {
	components: {
	},
	props: {
		value: {
			type: String,
			default: () => '',
			required: true,
		},
		placeholder: {
			type: String,
			default: () => '',
		},
		autocomplete: {
			type: String,
			default: () => 'off',
		},
		rows: {
			type: String,
			default: () => '3',
		},
		isDisabled: {
			type: Boolean,
			default: () => false,
		},
	},
	data() {
		return {
		};
	},
	computed: {
		name() {
			return this.value ? this.value.toLowerCase() : '';
		},
		theClass() {
			const myClass = `font-size-14px block w-full sm:text-sm placeholder-gray-400 focus:ring-primary-500 focus:border-primary-500 border border-gray-300 rounded-md shadow-sm`;
			return myClass;
		},
	},
	watch: {},
	created() {},
	mounted() {},
	beforeUnmount() {},
	methods: {
		onInput(event) {
			// Can add validation here
			this.$emit('update:modelValue', event.target.value);
		},
		onChange(event) { // Supports .lazy
			// Can add validation here
			this.$emit('update:modelValue', event.target.value);
		},
	},
};
</script>
<style>
.font-size-14px {
  font-size: 14px!important;
}
</style>