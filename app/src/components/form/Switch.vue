<template>
  <div class="relative inline-block">
    <!-- Custom switch background -->
    <div
      class="w-11 h-6 rounded-full cursor-pointer transition-colors duration-200"
      :class="{
        'bg-gray-300': disabled,
        'bg-orange-500': !disabled && value,
        'bg-gray-200': !disabled && !value
      }"
      @click="handleClick"
    >
      <!-- Custom switch toggle dot -->
      <div
        class="absolute top-[2px] left-[2px] w-5 h-5 bg-white rounded-full transition-transform duration-200 border border-gray-300"
        :class="{
          'dot-active': value,
          'bg-gray-400': disabled
        }"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TSwitch",
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value(newVal, oldVal) {
      console.log('🔄 TSwitch value changed from', oldVal, 'to', newVal);
    }
  },
  methods: {
    handleClick() {
      console.log('🔄 TSwitch clicked, current value:', this.value, 'disabled:', this.disabled);
      if (!this.disabled) {
        const newValue = !this.value;
        console.log('✅ TSwitch emitting new value:', newValue);
        this.$emit('input', newValue);
      } else {
        console.log('❌ TSwitch is disabled, not toggling');
      }
    },
  },
};
</script>

<style scoped>
.dot-active {
  transform: translateX(1.25rem); /* 20px to match w-5 */
}
</style>
