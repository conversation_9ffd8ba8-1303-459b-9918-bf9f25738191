<template>
    <Drawer class="z-99 min-w-full md:min-w-[420px]" v-model:visible="visibleRight" header="Right Drawer" position="right">
        <template #header class="border-b-2">
            <slot name="header"></slot>
        </template>
        <slot name="body"></slot>
        <template #footer>
            <slot name="footer"></slot>
        </template>
    </Drawer>
</template>

<script>

import Drawer from 'primevue/drawer';

    export default {
        data() {
            return {
                visibleRight: false
            }
        },
        components: {
            Drawer
        },
        props: {
            id: {
                type: String,
                default: 'hs-hinoo-dutoo',
            },
        },
        watch: {
          visibleRight() {
            if (!this.visibleRight) {
              this.$emit('close')
            }
          }
        },
        created() {},
        methods: {
        },
    };
</script>
<style >
/* This targets the header container directly within the Drawer */
    .p-drawer-header {
        padding: 0 !important; /* Override padding */
        border-bottom-width: 2px;
    }
    .p-drawer-content {
        padding: 0 !important; /* Override padding */
    }
    .p-drawer-footer {
        padding: 0 !important; /* Override padding */
    }
</style>