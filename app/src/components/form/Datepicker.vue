<template>
  <div id="main-dtpicker" class="shadow p-1 border border-gray-200 md:px-[30px] md:py-[30px]">
    <VueDatePicker v-model="date" :min-date="new Date()" :markers="markers" inline auto-apply :is-24="true" :disabled-dates="disabledDates"
    @update-month-year="onMonthYearChange" :month-change-on-scroll="false">
      <template #month-year="{ month, year, months, years, updateMonthYear, handleMonthYearChange }">
        <div class="icons">
          <span class="custom-icon" @click="handleMonthYearChange(false)">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
            </svg>
          </span>
        </div>
        <div class="custom-month-year-component">
          <select class="month-input font-medium" :value="month" @change="updateMonth($event, updateMonthYear, year)">
            <option v-for="m in fullMonths" :key="m.value" :value="m.value">
              {{ m.text }}
            </option>
          </select>
          /
          <select class="year-input font-medium" :value="year" @change="updateYear($event, updateMonthYear, month)">
            <option v-for="y in years" :key="y.value" :value="y.value">
              &nbsp; {{ y.text }}
            </option>
          </select>
        </div>
        <div class="icons">
          <span class="custom-icon" @click="handleMonthYearChange(true)">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
            </svg>
          </span>
        </div>
      </template>
      <template #time-picker="{ time, updateTime }">
        <div class="custom-time-picker-component">
          <label class="time-label font-bold">Start time :</label>
          <div class="time-picker-wrapper mt-4">
            <div class="time-input-group">
              <select :disabled="!date" class="time-input" :value="hours" @change="onHourChange($event.target.value)">
                <option v-for="h in hoursArray" :key="h.value" :value="h.value" :disabled="isHourUnavailable(h.value)">
                  {{ h.text }}
                </option>
              </select>
              <div class="time-input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4 ml-2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M7 15l5 5 5-5" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M7 9l5-5 5 5" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #action-preview="{ value }"></template>
      <template #marker="{ marker, day, date }">
        <span class="custom-marker"></span>
      </template>
    </VueDatePicker>
    <div v-if="isShowEstimated" class="mt-4">
      <label class="time-label font-bold">{{ $t('Estimated event duration') }}</label>
      <div class="time-label text-xs text-gray-400">{{ $t('Consider extra time for rehearsal and preparation') }}</div>
      <div class="flex">
        <div
          v-for="hour in hoursExtend"
          :key="hour.value"
          @click="!isDurationDisabled(hour) && selectDuration(hour)"
          :class="{
            'bg-[#2563EB] text-white': duration === hour.value && !isDurationDisabled(hour),
            'bg-gray-200  text-gray-500 cursor-not-allowed': isDurationDisabled(hour),
          }"
          class="text-sm mt-4 font-bold shadow pointer max-w-[80px] mr-4 p-1 text-center rounded-md w-full border border-gray-300 hover:bg-[#2563EB] hover:text-white mb-6"
        >
          {{ hour.label }}
        </div>
      </div>
    </div>

    <div v-if="isButtonAction" class="mt-4 h-[60px]">
      <div class="border border-gray-300 w-full"></div>
      <div class="flex justify-between items-center mt-2">
        <div class="text-primary-600 font-bold text-sm" @click="customizeEvent()">{{  $t('Customize Event') }}</div>
        <t-button
          :type="'submit'"
          :color="`primary-solid`"
          :isLoading="isBooking"
          :isDisabled="!isFormValid || isBooking || !duration || allDurationsDisabled"
          @click="bookStudio()"
        >
          {{ $t('Book Now') }}
        </t-button>
      </div>
    </div>
  </div>
</template>


<script>
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import addDays from 'date-fns/addDays';
import TButton from '@/components/global/Button.vue';

export default {
  components: { VueDatePicker, TButton },
  data() {
    return {
      isSaving: false,
      date: null,
      hours: 9, // Default hour in 24-hour format
      fullMonths: [
        { text: 'January', value: 0 }, { text: 'February', value: 1 }, { text: 'March', value: 2 },
        { text: 'April', value: 3 }, { text: 'May', value: 4 }, { text: 'June', value: 5 },
        { text: 'July', value: 6 }, { text: 'August', value: 7 }, { text: 'September', value: 8 },
        { text: 'October', value: 9 }, { text: 'November', value: 10 }, { text: 'December', value: 11 },
      ],
      hoursExtend: [
        { label: '2 HRS', value: 2 },
        { label: '4 HRS', value: 4 },
        { label: '8 HRS', value: 8 },
      ],
      duration: 2,
    };
  },
  props: {
    isShowEstimated: {
      type: Boolean,
      default: false,
    },
    isButtonAction: {
      type: Boolean,
      default: false,
    },
    isBooking: {
      type: Boolean,
      default: false,
    },
    schedules: {
      type: Array,
      default: [],
    },
  },
  computed: {
    allDurationsDisabled() {
      return this.hoursExtend.every(hour => this.isDurationDisabled(hour));
    },
    hoursArray() {
      return Array.from({ length: 16 }, (_, i) => { // Change length to 16 for 8:00 to 23:00
        const hour = i + 8; // 08:00 to 23:00
        return {
          value: hour,
          text: hour.toString().padStart(2, '0') + ":00",
        };
      });
    },

    isFormValid() {
      return this.date && this.hours;
    },
    disabledDates() {
      return this.schedules
        .filter(item => item.status === "Not Available")
        .map(item => new Date(item.date));
    },
    markers() {
      return this.schedules
        .filter(item => item.status === "Not Available")
        .map(item => ({
          date: new Date(item.date),
          type: 'dot'
        }));
    },
  },
  mounted() {
    this.duration = 2,
    this.$emit('updateDatepicker', this.date);
    this.$nextTick(() => {
      this.highlightDisabledDates();
    });
  },
  updated() {
    this.highlightDisabledDates();
  },
  methods: {
    isDurationDisabled(hourDuration) {
      // Get the selected hour
      const startHour = this.hours;

      // Loop through the hours and check if any of the required hours for the duration are unavailable
      for (let i = 0; i < hourDuration.value; i++) {
        const checkHour = startHour + i;

        // If we go past 23, we can't select more hours
        if (checkHour > 23 || this.isHourUnavailable(checkHour)) {
          return true; // Disable the button if any required hour is unavailable
        }
      }

      return false; // Enable the button if all required hours are available
    },

    onMonthYearChange(event) {
      this.$emit('fetchShcedules', event)
    },
    highlightDisabledDates() {
      const calendarItems = this.$el.querySelectorAll('.dp__calendar_item');

      calendarItems.forEach(item => {
        if (item.querySelector('.dp__cell_disabled')) {
          item.classList.add('highlight-disabled');
        } else {
          item.classList.remove('highlight-disabled');
        }
      });
    },
    selectDuration(hour) {
      this.duration = hour.value;
      this.$emit('updateDuration', hour.value);
    },
    bookStudio() {
      this.$emit('actionRight');
    },
    customizeEvent() {
      this.$emit('actionLeft');
    },
    onHourChange(value) {
      this.hours = value;
      this.duration = 2;
      this.updateDate();
    },

    updateDate() {
      const date = new Date(this.date);
      date.setHours(this.hours);
      date.setMinutes(0); // Set minutes to 00
      date.setSeconds(0); // Reset seconds
      this.date = date;
    },
    isHourUnavailable(hour) {
      if (!this.date) return false;

      const selectedDate = this.date.toISOString().split('T')[0];
      const unavailableEntry = this.schedules.find(schedule => {
        const scheduleDate = new Date(schedule.date).toISOString().split('T')[0];
        return scheduleDate === selectedDate;
      });

      if (!unavailableEntry) return false;

      const hourString = hour.toString().padStart(2, '0') + ":00";
      return unavailableEntry.hours.includes(hourString);
    },

    recalculateDisabledHours() {
      // Method to force recalculation of the available hours when date changes
      this.hoursArray = this.hoursArray.map(hour => {
        return {
          ...hour,
          disabled: this.isHourUnavailable(hour.value),
        };
      });
      this.$emit('updateDatepicker', this.date);
    },
    updateMonth(event, updateMonthYear, year) {
      updateMonthYear(+event.target.value, year);
    },
    updateYear(event, updateMonthYear, month) {
      updateMonthYear(month, +event.target.value);
    },
    
  },
  watch: {
    date(newDate) {
      if (newDate) {
        // Ensure the minutes and seconds are reset
        newDate.setMinutes(0);
        newDate.setSeconds(0);

        // Get the current hour
        let selectedHour = newDate.getHours();

        // Adjust the hour to the nearest valid value between 9 and 23
        if (selectedHour < 8) {
          selectedHour = 8;
        } else if (selectedHour > 23) {
          selectedHour = 23;
        }

        // Now check if the selectedHour is available
        // If it is unavailable, find the next available hour
        while (this.isHourUnavailable(selectedHour)) {
          selectedHour++;
          if (selectedHour > 23) {
            selectedHour = 8; // Wrap around to the beginning of the valid range if we go past 23
          }

          // Break to avoid infinite loop if no hours are available (this should not happen normally)
          if (selectedHour === newDate.getHours()) {
            break;
          }
        }

        // Update the date object with the available hour
        newDate.setHours(selectedHour);

        // Set the hours data property
        this.hours = selectedHour;

        // Trigger reactivity by ensuring computed properties are recalculated
        this.recalculateDisabledHours();
        this.$emit('updateDatepicker', this.date);
      }
    },

  },
};
</script>



<style lang="scss">
  .dp__menu {
    border: none!important;
    padding: 0;
    margin: 0;
  }
  .dp__menu_inner {
    padding: 0;
  }
  .custom-time-picker-component {
    margin-top: 10px;
  }

  .time-label {
    margin-right: 10px;
    font-size: 14px;
  }

  .time-picker-wrapper {
    display: flex;
    align-items: center;
  }

  .time-input-group {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 0 5px;
    margin-right: 5px;
  }

  .time-input {
    border: none;
    outline: none; /* Remove blue border on focus */
    appearance: none;
    width: 60px;
    text-align: left;
    font-size: 14px;
    background-color: transparent;
    padding: 5px;
  }

  .time-input-icon {
    position: absolute;
    right: 5px;
    pointer-events: none;
  }

  .time-separator {
    font-size: 14px;
    margin-right: 5px;
    padding: 5px 0;
  }

  .time-input-group select {
    cursor: pointer!important;
    font-size: 14px!important;
    outline: none!important; /* Remove default outline */
    box-shadow: none!important; /* Remove box-shadow if added by browser */
  }

  .dp__flex_display {
    display: block !important;
  }

  .custom-time-picker-component {
    align-items: center!important;
    justify-content: center!important;
  }

  .dp__today {
    border: none!important;
    font-weight: bold!important;
    color: var(--dp-primary-color)!important;
  }

  .time-input {
    background-image: none!important;
  }

  .dp__calendar_header {
    font-weight: lighter!important;
    color: grey!important;
  }

  .dp__calendar_row {
    justify-content: space-between!important;
    padding: 0!important;
    margin: 0!important;
  }

  .dp__active_date {
    border-radius: 100%!important;
    color: white!important;
    font-weight: lighter!important;
  }

  .dp__calendar_item {
    box-sizing: border-box!important;
    /* Include padding and border in the element's total width and height */
    border: 1px solid #ccc!important;
    /* Border color */
    padding: 5px 10px 5px 10px!important;
    /* Adjust padding for better spacing */
    margin: 0!important;
    /* Remove margin to align items properly */
    text-align: center!important;
    /* Center align text */
    position: relative!important;
    /* Relative position for markers */
  }

  .dp__calendar_item[data-marker] {
    background-color: #f8d7da!important;
    /* Background color for marked dates */
    border: 1px solid #f5c6cb!important;
    /* Border color for marked dates */
  }

  .month-input {
    border: none;
    min-width: 80px;
    padding-right: 1rem;
    background-image: none;
  }

  .year-input {
    border: none;
    width: 80px;
    padding-right: 0.5rem;
    background-image: none;
  }

  .custom-month-year-component {
    display: flex;
    align-items: center;
    margin: 0 auto;
  }

  .icons {
    display: flex;
    box-sizing: border-box;
  }

  .custom-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 25px;
    color: var(--dp-icon-color);
    text-align: center;

    svg {
      height: 20px;
      width: 20px;
    }

    &:hover {
      background: var(--dp-hover-color);
    }
  }

  .dp__cell_disabled {
    color: rgba(64, 59, 59, 0.827)!important;
  }

  .dp__calendar_item.highlight-disabled {
    background-color: hsla(0, 0%, 9%, 0.274) !important;
    color: black !important;
  }

  .custom-marker {
    position: absolute;
    top: -6px;
    left: -11px;
    height: 45px;
    width: 58px;
    background-color: #ED7070;
    pointer-events: none;
    /* Disable pointer events on markers */
    z-index: -1;
  }

  .dp__calendar_item.selected {
    background-color: #007bff!important;
    /* Background color for selected date */
    color: white!important;
    /* Text color for selected date */
  }

  .dp__pointer {
    z-index: 2;
  }
</style>
