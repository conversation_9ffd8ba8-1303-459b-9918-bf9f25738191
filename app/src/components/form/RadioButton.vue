<template>
	<!-- 
	USAGE
	<t-radio-button
		:label="`Public access`"
		:sub-label="`Everyone with the link will see this event`"
		:value="`public`"
		:name="'inputType'"
		:id="`public-selected`"
		:selected-value="event.type"
		v-model="event.type"
	/>
-->
	<div class="relative flex items-start">
		<div class="absolute flex items-center h-5">
			<input
				:id="id"
				:name="name"
				aria-describedby="radio-input"
				type="radio"
				class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
				:checked="value === selectedValue"
				@change="toggle($event)"
			>
		</div>
		<div class="pl-7 text-sm">
			<label
				v-if="label"
				:for="id"
				class="font-semibold text-gray-700"
			>{{ label }}</label>
			<p
				v-if="subLabel"
				id="checkbox-description"
				class="text-gray-500"
			>
				{{ subLabel }}
			</p>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		id: {
			type: String,
			default: '',
		},
		name: {
			type: String,
			default: '',
		},
		value: {
			type: String,
			default: '',
		},
		selectedValue: {
			type: String,
			default: '',
		},
		label: {
			type: String,
			default: '',
		},
		subLabel: {
			type: String,
			default: '',
		},
	},
	mounted() {
	},
	methods: {
		toggle() {
			this.$emit('update:modelValue', this.value);
		},
	}
};
</script>