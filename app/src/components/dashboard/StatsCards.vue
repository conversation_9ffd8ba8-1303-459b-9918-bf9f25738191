<template>
	<!-- Stats Cards Section -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 mb-6">
		<!-- Stats Card 1 -->
		<div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm font-medium text-gray-600 mb-1">{{ statsCards[0].title }}</p>
					<p class="text-3xl font-bold text-gray-900">{{ statsCards[0].value }}</p>
					<p class="text-sm text-gray-500 mt-1">{{ statsCards[0].subtitle }}</p>
				</div>
			</div>
		</div>

		<!-- Stats Card 2 -->
		<div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm font-medium text-gray-600 mb-1">{{ statsCards[1].title }}</p>
					<p class="text-3xl font-bold text-gray-900">{{ statsCards[1].value }}</p>
					<p class="text-sm text-gray-500 mt-1">{{ statsCards[1].subtitle }}</p>
				</div>
			</div>
		</div>

		<!-- Stats Card 3 -->
		<div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm font-medium text-gray-600 mb-1">{{ statsCards[2].title }}</p>
					<p class="text-3xl font-bold text-gray-900">{{ statsCards[2].value }}</p>
					<p class="text-sm text-gray-500 mt-1">{{ statsCards[2].subtitle }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'StatsCards',
	props: {
		statsCards: {
			type: Array,
			default: () => [
				{
					title: 'Stats Title 1',
					value: '1234',
					subtitle: 'Sub sentence information'
				},
				{
					title: 'Stats Title 2',
					value: '76',
					subtitle: 'Sub sentence information'
				},
				{
					title: 'Stats Title 3',
					value: '456',
					subtitle: 'Sub sentence information'
				}
			]
		}
	}
};
</script>
