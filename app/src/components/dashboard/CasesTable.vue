<template>
  <div>
    <t-button :color="`primary-solid`" @click="handleAddCase">
      <PlusIcon class="h-5 w-5 text-white" />
      <span class="text-sm text-white">Add Case</span>
    </t-button>
  </div>
	<div class="bg-white mt-4">
		<div class="md:flex justify-between md:px-4 pt-4">
			<div class="mt-2">
				<label for="table-header" class="font-medium text-gray-800">All Cases</label>
			</div>
			<div class="flex">
        <div class="relative mr-2">
          <input type="text" v-model="keyword" v-value="keyword" @input="onInputSearch"
                        class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-primary-500 focus:ring-primary-500 disabled:opacity-50 disabled:pointer-events-none"
                :placeholder="$t('Search')">
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
                <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                </svg>
            </div>
        </div>
				<div class="relative mr-2">
					<t-button :color="`secondary-solid`" class="inline-flex items-center gap-2 px-4">
						<svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707v4.586a1 1 0 01-.293.707L9 21.414a1 1 0 01-.707.293H8a1 1 0 01-1-1v-5.586a1 1 0 00-.293-.707L.293 7.707A1 1 0 010 7V4z" />
						</svg>
						Filter
					</t-button>
				</div>
				
			</div>
		</div>
		<!-- Table Section -->
		<div v-if="!isFetching && items.length"
			class="m-2 overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
			<div class="min-w-full inline-block align-middle">
				<!-- Table -->
				<table class="min-w-full divide-y divide-gray-200">
					<thead>
						<tr>
							<th scope="col" class="w-[10px]">
								<div class="text-gray-800"></div>
							</th>
							<th scope="col" class="min-w-[150px]">
								<div
									class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
									<!-- Sort Dropdown -->
									<div class="hs-dropdown relative inline-flex w-full cursor-pointer">
										<button id="hs-pro-dutnms" type="button"
											class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
											aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
											Case Title
											<svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
												height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
												stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
												<path d="m7 15 5 5 5-5" />
												<path d="m7 9 5-5 5 5" /></svg>
										</button>

										<!-- Dropdown -->
										<div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
											role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
											<div class="p-1">
												<button type="button" @click="sortAsc()"
													class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
													<svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
														width="24" height="24" viewBox="0 0 24 24" fill="none"
														stroke="currentColor" stroke-width="2" stroke-linecap="round"
														stroke-linejoin="round">
														<path d="m5 12 7-7 7 7" />
														<path d="M12 19V5" /></svg>
													Sort ascending
												</button>
												<button type="button" @click="sortDsc()"
													class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
													<svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
														width="24" height="24" viewBox="0 0 24 24" fill="none"
														stroke="currentColor" stroke-width="2" stroke-linecap="round"
														stroke-linejoin="round">
														<path d="M12 5v14" />
														<path d="m19 12-7 7-7-7" /></svg>
													Sort descending
												</button>
											</div>
										</div>
										<!-- End Dropdown -->
									</div>
									<!-- End Sort Dropdown -->
								</div>
							</th>
							<th scope="col" class="min-w-10">
								<div
									class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
									<span> Status </span>
								</div>
							</th>
							<th scope="col" class="min-w-10">
								<div
									class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
									<span> Priority </span>
								</div>
							</th>
							<th scope="col" class="min-w-10">
								<div
									class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
									<span> Assigned To </span>
								</div>
							</th>
							<th scope="col">
								<div
									class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
								</div>
							</th>
						</tr>
					</thead>

					<tbody class="divide-y divide-gray-200">
						<tr v-for="(item, index) in items" :key="item.id" @mouseover="onHover(item)"
						:class="{'bg-slate-100': item.id === curerentActive}">
							<td class="whitespace-nowrap py-3">
								<div class="w-[50px] flex items-center">
									<div >
										<span class="text-sm font-medium ml-4 text-gray-800">
											{{ (index + 1) + ((page-1)*limit) }}
										</span>
									</div>
								</div>
							</td>
							<td class="whitespace-nowrap pe-4 py-2" @dblclick="editCase(item)">
								<div class="w-full flex items-center gap-x-3">
									<div class="grow">
										<span class="text-sm font-normal text-gray-800">
											{{ item.title }}
										</span>
									</div>
									<div class="w-[16px]">
										<div class="hs-dropdown hs-dropdown-example relative inline-flex w-[32px]"
											:class="{'hidden': item.id !== curerentActive}">
											<button id="hs-dropdown-example" type="button"
												@click="this.selectedItem = item"
												class="pl-1 h-[30px] text-start w-full flex items-center text-sm text-nowrap font-normal text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100"
												aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
												<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
													stroke-width="2.5" stroke="currentColor" class="size-5 p-1">
													<path stroke-linecap="round" stroke-linejoin="round"
														d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
												</svg>
											</button>

											<div class="hs-dropdown-menu z-50 transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 
											hidden min-w-60 bg-white shadow-md rounded-md p-1 !mt-[-0.5rem] dark:bg-neutral-800 dark:border dark:border-neutral-700 "
												role="menu" aria-orientation="vertical"
												aria-labelledby="hs-dropdown-custom-icon-trigger">
												<a @click="viewCase(item)"
													class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none 
													focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
													href="#">
													View Details
												</a>
												<a @click="editCase(item)"
													class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
													href="#">
													Edit
												</a>
												<hr class="border-1">
												<a @click="deleteCase(item)"
													class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
													href="#">
													Delete
												</a>
											</div>
										</div>
									</div>
								</div>
							</td>
							<td class="whitespace-nowrap px-4 py-3">
								<span :class="getStatusClass(item.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
									{{ item.status }}
								</span>
							</td>
							<td class="whitespace-nowrap px-4 py-3">
								<span :class="getPriorityClass(item.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
									{{ item.priority }}
								</span>
							</td>
							<td class="whitespace-nowrap px-4 py-3">
								<span class="text-sm text-gray-600">
									{{ item.assignedTo }}
								</span>
							</td>
							<td class="whitespace-nowrap px-4 py-3">
								<div class="flex">

								</div>
							</td>
						</tr>
					</tbody>
				</table>
				<!-- End Table -->
			</div>
		</div>
		<!-- End Table Section -->
		
		<!-- Footer -->
		<!-- Modern Pagination -->
		<ModernPagination
			v-if="!isFetching && items.length"
			:total="total"
			:current-page="page"
			:last-page="maxPage"
			:per-page="limit"
			:is-fetching="isFetching"
			@page-changed="onPageChanged"
			@per-page-changed="onPerPageChanged"
		/>
		<!-- End Footer -->
		
		<div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
			No cases found
		</div>
	</div>
</template>

<script>
import TButton from '@/components/global/Button.vue';
import ModernPagination from '@/components/global/ModernPagination.vue';
import { PlusIcon } from "@heroicons/vue/solid";

export default {
	name: 'CasesTable',
	components: {
		TButton,
		ModernPagination,
		PlusIcon
	},
	props: {
		items: {
			type: Array,
			default: () => []
		},
		isFetching: {
			type: Boolean,
			default: false
		},
		page: {
			type: Number,
			default: 1
		},
		maxPage: {
			type: Number,
			default: 1
		},
		total: {
			type: Number,
			default: 0
		},
		limit: {
			type: Number,
			default: 10
		}
	},
	data() {
		return {
			keyword: "",
			curerentActive: -1,
			selectedItem: null,
		};
	},
	methods: {
		onInputSearch() {
			this.$emit('search', this.keyword);
		},
		sortAsc() {
			this.$emit('sort', 'asc');
		},
		sortDsc() {
			this.$emit('sort', 'desc');
		},
		onHover(item) {
			this.curerentActive = item.id;
		},
		nextPage() {
			this.$emit('next-page');
		},
		prevPage() {
			this.$emit('prev-page');
		},

		onPageChanged(page) {
			if (page > this.page) {
				this.$emit('next-page');
			} else if (page < this.page) {
				this.$emit('prev-page');
			}
		},

		onPerPageChanged(per_page) {
			this.$emit('per-page-changed', per_page);
		},
		handleAddCase() {
			this.$emit('add-case');
		},
		viewCase(item) {
			this.$emit('view-case', item);
		},
		editCase(item) {
			this.$emit('edit-case', item);
		},
		deleteCase(item) {
			this.$emit('delete-case', item);
		},
		getStatusClass(status) {
			const classes = {
				'Open': 'bg-yellow-100 text-yellow-800',
				'In Progress': 'bg-primary-100 text-primary-800',
				'Resolved': 'bg-green-100 text-green-800',
				'Closed': 'bg-gray-100 text-gray-800'
			};
			return classes[status] || 'bg-gray-100 text-gray-800';
		},
		getPriorityClass(priority) {
			const classes = {
				'Critical': 'bg-red-100 text-red-800',
				'High': 'bg-orange-100 text-orange-800',
				'Medium': 'bg-yellow-100 text-yellow-800',
				'Low': 'bg-green-100 text-green-800'
			};
			return classes[priority] || 'bg-gray-100 text-gray-800';
		}
	}
};
</script>
