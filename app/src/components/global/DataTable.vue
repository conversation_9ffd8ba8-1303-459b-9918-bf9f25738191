<template>
  <div>
    <!-- Table Header with Search and Actions -->
    <div class="flex justify-between items-center mb-4" v-if="showHeader">
      <div class="flex items-center gap-3">
        <!-- Add Button -->
        <TButton
          v-if="showAddButton"
          @click="$emit('add-new')"
          color="primary-solid"
          class="text-sm"
        >
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          {{ addButtonText }}
        </TButton>
      </div>
      
      <div class="flex items-center gap-3">
        <!-- Search -->
        <div class="relative" v-if="showSearch">
          <TInput
            :value="searchKeyword"
            @input="$emit('search', $event.target.value)"
            type="text"
            :placeholder="searchPlaceholder"
            customStyle="padding-left: 2.5rem; padding-right: 2rem;"
          />
          <div class="absolute inset-y-0 left-0 flex items-center pl-3">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
        </div>
        
        <!-- Filter -->
        <TButton
          v-if="showFilter"
          @click="$emit('toggle-filter')"
          color="secondary-solid"
          class="text-sm"
        >
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
          </svg>
          Filter
        </TButton>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isFetching" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!items.length" class="text-center py-8">
      <div class="text-gray-500">{{ emptyMessage }}</div>
    </div>

    <!-- Table -->
    <div v-else class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table class="min-w-full divide-y divide-gray-300">
        <thead class="bg-gray-50">
          <tr>
            <!-- Row Number Column -->
            <th v-if="showRowNumbers" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              No
            </th>
            
            <!-- Dynamic Columns -->
            <th
              v-for="column in columns"
              :key="column.key"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              :class="column.headerClass"
            >
              {{ column.title }}
            </th>
            
            <!-- Actions Column -->
            <th v-if="showActions" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(item, index) in items" :key="getItemKey(item, index)" class="hover:bg-gray-50">
            <!-- Row Number -->
            <td v-if="showRowNumbers" class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ getRowNumber(index) }}
            </td>
            
            <!-- Dynamic Columns -->
            <td
              v-for="column in columns"
              :key="column.key"
              class="px-6 py-4 whitespace-nowrap"
              :class="column.cellClass"
            >
              <slot
                :name="`column-${column.key}`"
                :item="item"
                :value="getColumnValue(item, column.key)"
                :index="index"
              >
                <span class="text-sm text-gray-900">{{ getColumnValue(item, column.key) }}</span>
              </slot>
            </td>
            
            <!-- Actions -->
            <td v-if="showActions" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <slot name="actions" :item="item" :index="index">
                <div class="flex items-center space-x-2">
                  <button
                    v-if="showEditAction"
                    @click="$emit('edit-item', item)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                  </button>
                  <button
                    v-if="showDeleteAction"
                    @click="$emit('delete-item', item)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <ModernPagination
      v-if="showPagination && !isFetching && items.length"
      :total="meta.total"
      :current-page="meta.current_page"
      :last-page="meta.last_page"
      :per-page="meta.per_page"
      :is-fetching="isFetching"
      @page-changed="$emit('page-changed', $event)"
      @per-page-changed="$emit('per-page-changed', $event)"
    />
  </div>
</template>

<script>
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import ModernPagination from '@/components/global/ModernPagination.vue';

export default {
  name: 'DataTable',
  components: {
    TButton,
    TInput,
    ModernPagination,
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    meta: {
      type: Object,
      default: () => ({
        total: 0,
        current_page: 1,
        last_page: 1,
        per_page: 10
      })
    },
    isFetching: {
      type: Boolean,
      default: false
    },
    searchKeyword: {
      type: String,
      default: ''
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showFilter: {
      type: Boolean,
      default: true
    },
    showAddButton: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    showRowNumbers: {
      type: Boolean,
      default: true
    },
    showActions: {
      type: Boolean,
      default: true
    },
    showEditAction: {
      type: Boolean,
      default: true
    },
    showDeleteAction: {
      type: Boolean,
      default: true
    },
    addButtonText: {
      type: String,
      default() { return this.$t ? this.$t('Add New') : 'Add New'; }
    },
    searchPlaceholder: {
      type: String,
      default: 'Search'
    },
    emptyMessage: {
      type: String,
      default: 'No data available'
    },
    itemKey: {
      type: String,
      default: 'id'
    }
  },
  emits: [
    'add-new',
    'edit-item',
    'delete-item',
    'search',
    'toggle-filter',
    'page-changed',
    'per-page-changed'
  ],
  methods: {
    getItemKey(item, index) {
      return item[this.itemKey] || index;
    },
    
    getRowNumber(index) {
      return (this.meta.current_page - 1) * this.meta.per_page + (index + 1);
    },
    
    getColumnValue(item, key) {
      return key.split('.').reduce((obj, k) => obj?.[k], item);
    }
  }
};
</script>
