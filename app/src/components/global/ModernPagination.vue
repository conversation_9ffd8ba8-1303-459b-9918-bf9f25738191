<template>
  <div v-if="!isFetching && total > 0" class="mt-5 flex flex-wrap justify-between items-center gap-2">
    <div class="text-sm text-gray-700">
      {{ total }} result(s)
    </div>
    
    <!-- Pagination -->
    <div class="flex items-center space-x-2">
      <!-- First Page Button -->
      <button
        @click="goToPage(1)"
        :disabled="currentPage <= 1"
        class=" py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"/>
        </svg>
      </button>
      
      <!-- Previous Button -->
      <button
        @click="prevPage"
        :disabled="currentPage <= 1"
        class=" py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
      </button>
      
      <!-- Page Numbers -->
      <div class="flex items-center space-x-1">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          :class="[
            'px-3 py-1 text-sm rounded',
            page === currentPage
              ? 'bg-primary-600 text-white'
              : 'text-gray-700 hover:bg-gray-100'
          ]"
        >
          {{ page }}
        </button>
      </div>
      
      <!-- Next Button -->
      <button
        @click="nextPage"
        :disabled="currentPage >= lastPage"
        class=" py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
      </button>
      
      <!-- Last Page Button -->
      <button
        @click="goToPage(lastPage)"
        :disabled="currentPage >= lastPage"
        class=" py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>
        </svg>
      </button>
      
      <!-- Per Page Selector -->
      <div v-if="isShowLimit" class="relative ml-4">
        <select
          :value="perPage"
          @change="changePerPage"
          class="appearance-none bg-white border border-gray-300 rounded px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-primary-600"
        >
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernPagination',
  props: {
    total: {
      type: Number,
      required: true
    },
    currentPage: {
      type: Number,
      required: true
    },
    lastPage: {
      type: Number,
      required: true
    },
    perPage: {
      type: Number,
      required: true
    },
    isFetching: {
      type: Boolean,
      default: false
    },
    isShowLimit: {
      type: Boolean,
      default: true
    }
  },
  emits: ['page-changed', 'per-page-changed'],
  computed: {
    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.currentPage - 2);
      const end = Math.min(this.lastPage, this.currentPage + 2);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  methods: {
    goToPage(page) {
      if (page !== this.currentPage && page >= 1 && page <= this.lastPage) {
        this.$emit('page-changed', page);
      }
    },
    
    prevPage() {
      if (this.currentPage > 1) {
        this.$emit('page-changed', this.currentPage - 1);
      }
    },
    
    nextPage() {
      if (this.currentPage < this.lastPage) {
        this.$emit('page-changed', this.currentPage + 1);
      }
    },
    
    changePerPage(event) {
      const newPerPage = parseInt(event.target.value);
      this.$emit('per-page-changed', newPerPage);
    }
  }
};
</script>
