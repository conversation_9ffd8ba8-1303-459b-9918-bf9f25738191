<template>
  <div class="relative">
    <!-- Filter Dropdown -->
    <div 
      v-if="showFilter" 
      class="absolute right-0 top-0 z-50 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg"
    >
      <div class="p-4 space-y-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Sort & Filter</h3>
        
        <!-- Sort By Field -->
        <div>
          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Sort By
          </label>
          <select
            v-model="localOrderBy"
            @change="updateFilter"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
          >
            <option 
              v-for="option in sortOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>
        
        <!-- Sort Direction -->
        <div>
          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Order
          </label>
          <select
            v-model="localSortBy"
            @change="updateFilter"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
          >
            <option value="desc">Newest First</option>
            <option value="asc">Oldest First</option>
          </select>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
          <t-button
            :color="'secondary-solid'"
            @click="resetFilter"
            class="px-3 py-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
          >
            Reset
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SortFilter',
  props: {
    showFilter: {
      type: Boolean,
      default: false
    },
    orderBy: {
      type: String,
      default: 'id'
    },
    sortBy: {
      type: String,
      default: 'desc'
    },
    sortOptions: {
      type: Array,
      default: () => [
        { value: 'created_at', label: 'Created Date' },
        { value: 'updated_at', label: 'Updated Date' },
        { value: 'name', label: 'Name' },
        { value: 'title', label: 'Title' }
      ]
    }
  },
  emits: ['update:orderBy', 'update:sortBy', 'update:showFilter', 'filter-changed'],
  data() {
    return {
      localOrderBy: this.orderBy,
      localSortBy: this.sortBy
    };
  },
  watch: {
    orderBy(newVal) {
      this.localOrderBy = newVal;
    },
    sortBy(newVal) {
      this.localSortBy = newVal;
    }
  },
  methods: {
    updateFilter() {
      this.$emit('update:orderBy', this.localOrderBy);
      this.$emit('update:sortBy', this.localSortBy);
      this.$emit('filter-changed', {
        order_by: this.localOrderBy,
        sort_by: this.localSortBy
      });
    },
    
    resetFilter() {
      this.localOrderBy = this.sortOptions[0].value;
      this.localSortBy = 'desc';
      this.updateFilter();
    },
    
    closeFilter() {
      this.$emit('update:showFilter', false);
    }
  }
};
</script>
