<template>
  <div ref="dropdownWrapper" class="relative inline-block">
    <Menu as="div" class="relative inline-block">
      <!-- <PERSON>u <PERSON>ton -->
      <MenuButton as="div" @click="toggleMenu">
        <slot name="button"></slot>
      </MenuButton>

      <!-- <PERSON>u Items -->
      <transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <MenuItems
          :style="{ top: `${menuPosition.top}px`, left: `${menuPosition.left}px` }"
          class="fixed p-2 bg-white shadow-lg rounded-md z-50 w-48 border-none outline-none"
          :class="customClassItems"
        >
          <slot name="items"></slot>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>

<script>
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/vue";

export default {
  name: "DropdownMenu",
  props: {
		customClassItems: {
			type: String,
			default: () => '',
		},
  },
  components: {
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
  },
  data() {
    return {
      isMenuOpen: false,
      showSubmenu: false, // For controlling submenu visibility
      menuPosition: {
        top: 0,
        left: 0,
      },
    };
  },
  methods: {
    toggleMenu() {
      this.isMenuOpen = !this.isMenuOpen;
      if (this.isMenuOpen) {
        this.$nextTick(() => {
          this.setMenuPosition();
        });
      }
    },
    setMenuPosition() {
      const button = this.$refs.dropdownWrapper;
      const rect = button.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      // Calculate top and left positions relative to the viewport
      this.menuPosition.top = rect.bottom + scrollTop;
      this.menuPosition.left = rect.left + scrollLeft;
    },
    setStatus(item, status) {
      item.status = status;
      console.log(`Status for ${item.name} set to: ${status}`);
    }
  },
};
</script>


<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
</style>

