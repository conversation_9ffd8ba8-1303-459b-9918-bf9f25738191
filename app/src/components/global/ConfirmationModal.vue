<template>
  <!-- Global Confirmation Modal -->
  <Modal
    :isShow="isShow"
    @onClose="handleCancel"
    :customClass="customClass || 'sm:max-w-md'"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Content -->
      <div class="flex-1 overflow-y-auto px-4 sm:px-6 py-2">
        <div class="flex items-center mb-4">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full" 
               :class="iconBackgroundClass">
            <!-- Warning Icon (default) -->
            <svg v-if="type === 'warning' || !type" class="h-6 w-6" :class="iconClass" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            
            <!-- Danger/Delete Icon -->
            <svg v-else-if="type === 'danger'" class="h-6 w-6" :class="iconClass" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            
            <!-- Info Icon -->
            <svg v-else-if="type === 'info'" class="h-6 w-6" :class="iconClass" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            
            <!-- Question Icon -->
            <svg v-else-if="type === 'question'" class="h-6 w-6" :class="iconClass" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>

        <div class="text-center">
          <h3 class="text-lg sm:text-xl font-medium text-gray-900 mb-2">
            {{ title || $t('Confirmation.Confirm Action') }}
          </h3>
          <p class="text-xs sm:text-sm text-gray-500">
            {{ message || $t('Confirmation.Are you sure you want to proceed?') }}
          </p>
        </div>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 px-4 pt-4 border-t border-gray-200">
        <div class="flex justify-center gap-3">
          <TButton
            type="button"
            @click="handleCancel"
            color="secondary-solid"
            class="text-xs sm:text-sm"
            :disabled="isLoading"
          >
            {{ cancelText || $t('Buttons.Cancel') }}
          </TButton>
          <TButton
            type="button"
            @click="handleConfirm"
            :color="confirmButtonColor"
            :isLoading="isLoading"
            color="primary-solid"
            class="text-xs sm:text-sm"
          >
            {{ confirmText || $t('Buttons.Confirm') }}
          </TButton>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
import Modal from '@/components/global/Modal.vue';
import TButton from '@/components/global/Button.vue';

export default {
  name: 'ConfirmationModal',
  components: {
    Modal,
    TButton
  },
  props: {
    // Modal visibility
    isShow: {
      type: Boolean,
      default: false
    },
    
    // Modal content
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      default: ''
    },
    
    // Modal type affects icon and colors
    type: {
      type: String,
      default: 'warning', // warning, danger, info, question
      validator: (value) => ['warning', 'danger', 'info', 'question'].includes(value)
    },
    
    // Button customization
    confirmText: {
      type: String,
      default: ''
    },
    cancelText: {
      type: String,
      default: ''
    },
    confirmButtonColor: {
      type: String,
      default: 'red-solid' // red-solid, primary-solid, etc.
    },
    
    // Loading state
    isLoading: {
      type: Boolean,
      default: false
    },
    
    // Custom styling
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconBackgroundClass() {
      switch (this.type) {
        case 'danger':
          return 'bg-red-100';
        case 'warning':
          return 'bg-yellow-100';
        case 'info':
          return 'bg-blue-100';
        case 'question':
          return 'bg-gray-100';
        default:
          return 'bg-yellow-100';
      }
    },
    
    iconClass() {
      switch (this.type) {
        case 'danger':
          return 'text-red-600';
        case 'warning':
          return 'text-yellow-600';
        case 'info':
          return 'text-blue-600';
        case 'question':
          return 'text-gray-600';
        default:
          return 'text-yellow-600';
      }
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    },
    
    handleCancel() {
      this.$emit('cancel');
      this.$emit('onClose'); // For compatibility with Modal component
    }
  }
};
</script>
