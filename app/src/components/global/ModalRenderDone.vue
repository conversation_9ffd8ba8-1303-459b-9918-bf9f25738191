
<template>
	<t-modal
		:show="isShow"
		:customClass="customClass"
	>
		<div
			class="bg-[#BBBBBB] h-[5px] w-full absolute top-0 left-0"
		/>
		<div
			class="bg-[#4f46e5] h-[5px] absolute top-0 left-0"
			:style="progressResult"
		/>
		<div class="sm:items-center justify-center relative">
			<button
				type="button"
				class="bg-white fixed right-0 mr-5 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
				@click="onClose"
			>
				<span class="sr-only">Close</span>
				<XIcon
					class="h-6 w-6"
					aria-hidden="true"
				/>
			</button>
			<div
				v-if="isRendering"
				class="mt-3 text-center mt-0 sm:ml-4 text-center"
			>
				<svg
					class="animate-spin text-center top-0 mt-[3em] bottom-0 my-auto mx-auto right-0 left-0 absolute mx-auto h-10 w-10 text-primary-600"
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
				>
					<circle
						class="opacity-10"
						cx="12"
						cy="12"
						r="10"
						stroke="currentColor"
						stroke-width="4"
					/>
					<path
						class="opacity-100"
						fill="currentColor"
						d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
					/>
				</svg>
			</div>
			<div
				v-if="isRendering"
				class="mt-3 text-center mt-0 sm:ml-4 text-center"
			>
				<div
					v-if="type === 'video'"
					class="mt-2 pt-[6em]"
				>
					<p class="text-sm font-semibold">
						Processing your output ({{ prcnt }}%)
					</p>
					<p class="text-xs font-semibold">
						We will email the result when its done, you can close this window
					</p>
				</div>
				<div
					v-if="type === 'image' || type === 'sequence'"
					class="mt-2 pt-[6em]"
				>
					<p class="text-sm font-semibold">
						Please wait while processing requested frame
					</p>
				</div>
			</div>
			<img
				v-if="!isRendering"
				class="w-[84px] mx-auto pt-5"
				src="@/assets/images/svg/donuts.svg"
				alt=""
			>
			<div
				v-if="!isRendering"
				class="mt-3 text-center mt-0 sm:ml-4 text-center"
			>
				<div class="mt-2">
					<p class="text-sm font-semibold">
						Horray! your file is ready
					</p>
				</div>
			</div>
		</div>
		<div
			v-if="!isRendering"
			class="mt-5 text-center justify-center"
		>
			<t-button
				:color="`primary-solid`"
				@click="download"
			>
				Download
			</t-button>
		</div>
	</t-modal>
</template>

<script>
/* eslint-disable vue/html-closing-bracket-spacing */
import TModal from '@/components/global/Modal.vue';
import TButton from '@/components/global/Button.vue';
import { XIcon } from '@heroicons/vue/outline';

export default {
	components: {
		TModal,
		TButton,
		XIcon,
	},
	props: {
		isShow: {
			type: Boolean,
			default: () => false,
		},
		isRendering: {
			type: Boolean,
			default: () => false,
		},
		type: {
			type: String,
			default: () => 'image',
		},
		currentProgressRender: {
			type: Number,
			default: () => 0,
		},
		totalProgressRender: {
			type: Number,
			default: () => 0,
		},
	},
	data() {
		return {
			customClass: 'max-w-[317px] min-h-[233px] mx-auto',
			progressResult: '',
			prcnt: 0,
		};
	},
	computed: {
		progressAnimation() {
			return this.progressResult;
		}
	},
	watch: {
		currentProgressRender() {
			this.prcnt = Math.round((this.currentProgressRender / this.totalProgressRender) * 100);
			const prcnt = (this.currentProgressRender / this.totalProgressRender) * 100;
			this.progressResult = `width: ${prcnt}%`;
		},
	},
	created() {},
	mounted() {},
	beforeUnmount() {},
	methods: {
		onClose() {
			this.$emit('onClose');
		},
		download() {
			this.$emit('download');
		},
	},
};
</script>