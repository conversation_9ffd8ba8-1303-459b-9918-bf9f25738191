<template>
  <!-- Feed Post Modal -->
  <Modal
    :isShow="showFeedPostModal"
    @onClose="$emit('close-feedpost-modal')"
    customClass="sm:max-w-2xl"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 border-b border-gray-200">
        <div class="flex items-center justify-between mb-3 ">
          <div class="text-lg sm:text-xl font-medium text-gray-900">
            {{ modalMode === 'add' ? 'Add Feed Post' : 'Edit Feed Post' }}
          </div>
          <button @click="$emit('close-feedpost-modal')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Scrollable Content -->
      <div class="flex-1 overflow-y-auto py-4">

      <form @submit.prevent="submitFeedPost">
        <div class="space-y-4 px-1">
          <!-- Messages -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Messages</label>
            <TInput
              v-model="feedPostForm.messages"
              :value="feedPostForm.messages"
              type="text"
              placeholder="Enter message title"
              required
            />
          </div>

          <!-- Content -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Content</label>
            <TInput
              v-model="feedPostForm.content"
              :value="feedPostForm.content"
              type="area"
              placeholder="Enter feed post content"
              required
              :rows="6"
            />
          </div>

          <!-- Is Pinned -->
          <div class="flex items-center">
            <input
              id="is_pinned"
              v-model="feedPostForm.is_pinned"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="is_pinned" class="ml-2 block text-sm text-gray-900">
              Pin this post
            </label>
          </div>

          <!-- Author Info (Read-only for edit mode) -->
          <div v-if="modalMode === 'edit' && feedPostForm.author" class="bg-gray-50 p-3 rounded-md">
            <label class="block text-sm font-medium text-gray-700 mb-1">Author</label>
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-primary-600">
                  {{ getInitials(feedPostForm.author.first_name, feedPostForm.author.last_name) }}
                </span>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">
                  {{ feedPostForm.author.first_name }} {{ feedPostForm.author.last_name }}
                </p>
                <p class="text-xs text-gray-500">{{ feedPostForm.author.email }}</p>
              </div>
            </div>
          </div>

          <!-- Created/Updated Info (for edit mode) -->
          <div v-if="modalMode === 'edit'" class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Created At</label>
              <p class="text-sm text-gray-600">{{ formatDate(feedPostForm.created_at) }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Updated At</label>
              <p class="text-sm text-gray-600">{{ formatDate(feedPostForm.updated_at) }}</p>
            </div>
          </div>
        </div>

      </form>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="$emit('close-feedpost-modal')"
            color="secondary-solid"
            class="text-sm sm:text-base"
          >
            Cancel
          </TButton>
          <TButton
            type="submit"
            color="primary-solid"
            @click="submitFeedPost"
            class="text-sm sm:text-base"
          >
            {{ modalMode === 'add' ? 'Create Feed Post' : 'Update Feed Post' }}
          </TButton>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
import Modal from '@/components/global/Modal.vue';
import TInput from '@/components/form/Input.vue';
import TButton from '@/components/global/Button.vue';

export default {
  name: 'FeedPostModals',
  components: {
    Modal,
    TInput,
    TButton,
  },
  props: {
    showFeedPostModal: {
      type: Boolean,
      default: false
    },
    modalMode: {
      type: String,
      default: 'add'
    },
    currentData: {
      type: Object,
      default: null
    }
  },
  emits: [
    'close-feedpost-modal',
    'submit-feedpost'
  ],
  data() {
    return {
      feedPostForm: {
        messages: '',
        content: '',
        is_pinned: false,
        author: null,
        created_at: null,
        updated_at: null
      }
    };
  },
  watch: {
    currentData: {
      handler(data) {
        if (data) {
          this.populateForm(data);
        } else {
          this.resetForm();
        }
      },
      immediate: true
    },
    showFeedPostModal(isShow) {
      if (!isShow) {
        this.resetForm();
      }
    }
  },
  methods: {
    populateForm(data) {
      this.feedPostForm = {
        id: data.id || null,
        messages: data.messages || '',
        content: data.content || '',
        is_pinned: data.is_pinned || false,
        author: data.author || null,
        created_at: data.created_at || null,
        updated_at: data.updated_at || null
      };
    },

    resetForm() {
      this.feedPostForm = {
        messages: '',
        content: '',
        is_pinned: false,
        author: null,
        created_at: null,
        updated_at: null
      };
    },

    getInitials(firstName, lastName) {
      const first = firstName ? firstName.charAt(0).toUpperCase() : '';
      const last = lastName ? lastName.charAt(0).toUpperCase() : '';
      return first + last;
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    submitFeedPost() {
      // Create a clean copy of the form data for submission
      const formData = {
        messages: this.feedPostForm.messages,
        content: this.feedPostForm.content,
        is_pinned: this.feedPostForm.is_pinned
      };

      // Include ID for updates
      if (this.modalMode === 'edit' && this.feedPostForm.id) {
        formData.id = this.feedPostForm.id;
      }

      this.$emit('submit-feedpost', formData);
    }
  }
};
</script>
