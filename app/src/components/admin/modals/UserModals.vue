<template>
  <!-- User Modal -->
  <Modal
    :isShow="showUserModal"
    @onClose="handleUserModalClose"
    customClass="sm:max-w-md"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 border-b border-gray-200 pb-2">
        <div class="flex items-center justify-between">
          <div class="text-lg sm:text-xl font-medium text-gray-900">
            {{ modalMode === 'add' ? 'Add User' : 'Edit User' }}
          </div>
          <button @click="$emit('close-user-modal')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Scrollable Content -->
      <div class="flex-1 overflow-y-auto py-4">

      <!-- Avatar -->
      <div class="flex justify-center mb-2">
        <div class="relative">
          <!-- Avatar Display -->
          <div  @click="triggerFileUpload" class="pointer w-16 h-16 rounded-full overflow-hidden bg-orange-500 flex items-center justify-center text-white text-xl font-bold">
            <img
              v-if="userForm.img_url"
              :src="userForm.img_url"
              :alt="`${userForm.first_name} ${userForm.last_name}`"
              class="w-full h-full object-cover"
            />
            <span v-else>{{ getInitials(`${userForm.first_name} ${userForm.last_name}`) }}</span>
          </div>

          <!-- Upload Button -->
          <button
            v-if="isUploading"
            @click="triggerFileUpload"
            :disabled="isUploading"
            class="absolute bottom-0 right-0 w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white hover:bg-gray-700 transition-colors"
            :class="{ 'opacity-50 cursor-not-allowed': isUploading }"
          >
            <div v-if="isUploading" class="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </button>

          <!-- Remove Avatar Button -->
          <button
            v-if="userForm.img_url && !isUploading"
            @click="removeAvatar"
            class="absolute top-0 right-0 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white hover:bg-red-600 transition-colors"
            title="Remove avatar"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>

          <!-- Upload Progress -->
          <div v-if="isUploading" class="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
            <div class="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
              {{ uploadProgress }}%
            </div>
          </div>

          <!-- Hidden File Input -->
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            @change="handleFileUpload"
            class="hidden"
          />
        </div>
      </div>

      <form @submit.prevent="submitUser">
        <div class="space-y-4  px-1">

          <div class="flex items-center justify-between relative">
            <!-- First Name -->
            <div class="w-full pr-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Form Labels.First Name') }} *</label>
              <TInput
                v-model="userForm.first_name"
                :value="userForm.first_name"
                type="text"
                :placeholder="$t('Form Placeholders.Enter first name')"
              />
              <p v-if="showUserValidation && (!userForm.first_name || userForm.first_name.trim() === '')" class="text-red-500 text-xs absolute">{{ $t('Password Validation.First name is required') }}</p>
            </div>

            <!-- Last Name -->
            <div class="w-full pl-2 relative">
              <label class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
              <TInput
                v-model="userForm.last_name"
                :value="userForm.last_name"
                type="text"
                placeholder="Enter last name"
              />
              <p v-if="showUserValidation && (!userForm.last_name || userForm.last_name.trim() === '')" class="text-red-500 text-xs absolute">{{ $t('Password Validation.Last name is required') }}</p>
            </div>
          </div>

          <!-- Phone -->
          <div class="flex items-center justify-between">
            <div class="w-full pr-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <TInput
                v-model="userForm.phone"
                :value="userForm.phone"
                type="tel"
                placeholder="Enter phone number"
              />
            </div>

            <!-- Email -->
            <div class="w-full pl-2 relative">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Form Labels.Email') }} *</label>
              <TInput
                v-model="userForm.email"
                :value="userForm.email"
                type="email"
                :placeholder="$t('Form Placeholders.Enter email address')"
              />
              <p v-if="showUserValidation && (!userForm.email || userForm.email.trim() === '')" class="text-red-500 text-xs absolute">{{ $t('Password Validation.Email is required') }}</p>
              <p v-else-if="showUserValidation && !isEmailValid" class="text-red-500 text-xs absolute">{{ $t('Password Validation.Please enter a valid email address') }}</p>
            </div>
          </div>

          <!-- Language -->
          <div class="flex items-center justify-between">
            <div class="w-full pr-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Language</label>
              <VueMultiselect
                :selectLabel="''"
                :selectedLabel="''"
              :deselectLabel="''"
                v-model="userForm.language"
                :options="languageOptions"
                :multiple="false"
                :close-on-select="true"
                :searchable="true"
                placeholder="Select Language"
                label="name"
                track-by="value"
              />
            </div>

            <!-- Unit -->
            <div class="w-full pl-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Unit</label>
              <TInput
                v-model="userForm.unit"
                :value="userForm.unit"
                type="text"
                placeholder="Enter unit/department"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <!-- Password (for new users) -->
            <div class="w-full pr-2" v-if="modalMode === 'add'">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Form Labels.Password') }} *</label>
              <TInput
                v-model="userForm.password"
                :value="userForm.password"
                type="password"
                :placeholder="$t('Form Placeholders.Enter password')"
                :required="true"
                :showValidation="showUserValidation"
                :preventSpaces="true"
                :minLength="6"
              />
            </div>

            <!-- Password Confirmation (for new users) -->
            <div class="w-full pl-2" v-if="modalMode === 'add'">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Form Labels.Confirm Password') }} *</label>
              <TInput
                v-model="userForm.password_confirmation"
                :value="userForm.password_confirmation"
                type="password"
                :placeholder="$t('Form Placeholders.Confirm password')"
                :required="true"
                :showValidation="showUserValidation"
                :preventSpaces="true"
                :confirmPassword="userForm.password"
              />
            </div>
          </div>

          <!-- Role IDs -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Roles</label>
            <VueMultiselect
              :selectLabel="''"
              :selectedLabel="''"
              :deselectLabel="''"
              v-model="userForm.role_ids"
              :options="roleOptions"
              :multiple="true"
              :close-on-select="false"
              :searchable="true"
              :loading="isLoadingRoles"
              :placeholder="isLoadingRoles ? 'Loading roles...' : 'Select Roles'"
              label="name"
              track-by="id"
              :disabled="isLoadingRoles"
            />
          </div>

          <!-- Group IDs -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Groups</label>
            <VueMultiselect
              :selectLabel="''"
              :selectedLabel="''"
              :deselectLabel="''"
              v-model="userForm.group_ids"
              :options="groupOptions"
              :multiple="true"
              :close-on-select="false"
              :searchable="true"
              :loading="isLoadingGroups"
              :placeholder="isLoadingGroups ? 'Loading groups...' : 'Select Groups'"
              label="name"
              track-by="id"
              :disabled="isLoadingGroups"
            />
          </div>

          <!-- Address -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <TInput
              v-model="userForm.address"
              :value="userForm.address"
              type="area"
              placeholder="Enter address"
            />
          </div>

          <!-- Display Contact -->
          <div>
            <label class="flex items-center space-x-2">
              <input
                v-model="userForm.is_display_contact"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span class="text-sm font-medium text-gray-700">Display Contact Information</span>
            </label>
          </div>

          
        </div>

      </form>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 pt-4 border-t border-gray-200">
        <!-- Validation Summary -->
        <div v-if="showUserValidation && (!isUserFormValid || !isEmailValid)" class="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-600 text-sm font-medium">{{ $t('Password Validation.Please complete all required fields') }}:</p>
          <ul class="text-red-600 text-xs mt-1 list-disc list-inside">
            <li v-if="!userForm.first_name || userForm.first_name.trim() === ''">{{ $t('Password Validation.First name is required') }}</li>
            <li v-if="!userForm.last_name || userForm.last_name.trim() === ''">{{ $t('Password Validation.Last name is required') }}</li>
            <li v-if="!userForm.email || userForm.email.trim() === ''">{{ $t('Password Validation.Email is required') }}</li>
            <li v-if="!isEmailValid && userForm.email">{{ $t('Password Validation.Please enter a valid email address') }}</li>
            <li v-if="modalMode === 'add' && (!userForm.password || userForm.password.trim() === '')">{{ $t('Password Validation.Password is required') }}</li>
            <li v-if="modalMode === 'add' && (!userForm.password_confirmation || userForm.password_confirmation.trim() === '')">{{ $t('Password Validation.Password confirmation is required') }}</li>
            <li v-if="modalMode === 'add' && userForm.password && userForm.password !== userForm.password_confirmation">{{ $t('Password Validation.Passwords must match') }}</li>
          </ul>
        </div>

        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="$emit('close-user-modal')"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="submit"
            color="primary-solid"
            @click="submitUser"
            :isDisabled="!isUserFormValid || !isEmailValid || isSubmittingUser"
            :isLoading="isSubmittingUser"
            class="text-xs sm:text-sm"
          >
            {{ $t('Buttons.Save Changes') }}
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Role Modal -->
  <Modal
    :isShow="showRoleModal"
    @onClose="handleRoleModalClose"
    customClass="sm:max-w-lg"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 border-b border-gray-200 pb-2">
        <div class="flex items-center justify-between">
          <div class="text-lg sm:text-xl font-medium text-gray-900">
            {{ modalMode === 'add' ? 'Add Role' : 'Edit Role' }}
          </div>
          <button @click="$emit('close-role-modal')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Scrollable Content -->
      <div class="flex-1 overflow-y-auto py-4">

      <form @submit.prevent="submitRole">
        <div class="space-y-4  px-1">
          <!-- Role Name -->
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-1">Role Name *</label>
            <TInput
              v-model="roleForm.name"
              :value="roleForm.name"
              type="text"
              placeholder="Type in role name"
            />
            <p v-if="showRoleValidation && (!roleForm.name || roleForm.name.trim() === '')" class="text-red-500 text-xs absolute">Role name is required</p>
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <TInput
              v-model="roleForm.description"
              :value="roleForm.description"
              type="area"
              placeholder="Type in short description about this role"
            />
          </div>

          <!-- Access Permissions -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">Access</label>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Add, Edit, Delete Item</span>
                <label class="relative inline-flex items-center cursor-pointer">
                  <TSwitch :value="roleForm.access_rights.crud" @input="(val) => roleForm.access_rights.crud = val" />
                </label>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Item View</span>
                <TSwitch :value="roleForm.access_rights.view" @input="(val) => roleForm.access_rights.view = val" />
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Item Comment</span>
                <TSwitch :value="roleForm.access_rights.comment" @input="(val) => roleForm.access_rights.comment = val" />
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Assign Resource</span>
                <TSwitch :value="roleForm.access_rights.assign" @input="(val) => roleForm.access_rights.assign = val" />
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Answer Flow</span>
                <TSwitch :value="roleForm.access_rights.answerFlow" @input="(val) => roleForm.access_rights.answerFlow = val" />
              </div>
            </div>
          </div>
        </div>

      </form>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="$emit('close-role-modal')"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="submit"
            color="primary-solid"
            @click="submitRole"
            :isDisabled="!isRoleFormValid || isSubmittingRole"
            :isLoading="isSubmittingRole"
            class="text-xs sm:text-sm"
          >
            Save Changes
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Group Modal -->
  <Modal
    :isShow="showGroupModal"
    @onClose="handleGroupModalClose"
    customClass="sm:max-w-md"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 border-b border-gray-200 pb-2">
        <div class="flex items-center justify-between">
          <div class="text-lg sm:text-xl font-medium text-gray-900">
            {{ modalMode === 'add' ? 'Add Group' : 'Edit Group' }}
          </div>
          <button @click="$emit('close-group-modal')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Scrollable Content -->
      <div class="flex-1 overflow-y-auto py-4">

      <form @submit.prevent="submitGroup">
        <div class="space-y-4  px-1">
          <!-- Group Name -->
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-1">Group Name *</label>
            <TInput
              v-model="groupForm.name"
              :value="groupForm.name"
              type="text"
              placeholder="Type in group name"
            />
            <p v-if="showGroupValidation && (!groupForm.name || groupForm.name.trim() === '')" class="text-red-500 text-xs absolute">Group name is required</p>
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <TInput
              v-model="groupForm.description"
              :value="groupForm.description"
              type="area"
              placeholder="Type in short description about this group"
            />
          </div>
        </div>

      </form>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="$emit('close-group-modal')"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="submit"
            color="primary-solid"
            @click="submitGroup"
            :isDisabled="!isGroupFormValid || isSubmittingGroup"
            :isLoading="isSubmittingGroup"
            class="text-xs sm:text-sm"
          >
            Save Changes
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Category Modal -->
  <Modal
    :isShow="showCategoryModal"
    @onClose="handleCategoryModalClose"
    customClass="sm:max-w-md"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 border-b border-gray-200 pb-2">
        <div class="flex items-center justify-between">
          <div class="text-lg sm:text-xl font-medium text-gray-900">
            {{ modalMode === 'add' ? 'Add User Category' : 'Edit User Category' }}
          </div>
          <button @click="$emit('close-category-modal')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Scrollable Content -->
      <div class="flex-1 overflow-y-auto py-4">

      <form @submit.prevent="submitCategory">
        <div class="space-y-4  px-1">
          <!-- Category Name -->
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-1">Category Name *</label>
            <TInput
              v-model="categoryForm.name"
              :value="categoryForm.name"
              type="text"
              placeholder="Type in category name"
            />
            <p v-if="showCategoryValidation && (!categoryForm.name || categoryForm.name.trim() === '')" class="text-red-500 text-xs absolute">Category name is required</p>
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <TInput
              v-model="categoryForm.description"
              :value="categoryForm.description"
              type="area"
              placeholder="Type in short description about this category"
            />
          </div>
        </div>

      </form>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="$emit('close-category-modal')"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="submit"
            color="primary-solid"
            @click="submitCategory"
            :isDisabled="!isCategoryFormValid || isSubmittingCategory"
            :isLoading="isSubmittingCategory"
            class="text-xs sm:text-sm"
          >
            Save Changes
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import Modal from '@/components/global/Modal.vue';
import TInput from '@/components/form/Input.vue';
import TButton from '@/components/global/Button.vue';
import TSwitch from '@/components/form/Switch.vue';
import VueMultiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.css';
import rolesApi from '@/api/roles.js';
import groupApi from '@/api/group.js';
import filesApi from '@/api/files.js';
import userApi from '@/api/user.js';
import categoryApi from '@/api/category.js';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'UserModals',
  components: {
    Modal,
    TInput,
    TButton,
    VueMultiselect,
    TSwitch,
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  props: {
    showUserModal: {
      type: Boolean,
      default: false
    },
    showRoleModal: {
      type: Boolean,
      default: false
    },
    showGroupModal: {
      type: Boolean,
      default: false
    },
    showCategoryModal: {
      type: Boolean,
      default: false
    },
    modalMode: {
      type: String,
      default: 'add'
    },
    currentData: {
      type: Object,
      default: null
    }
  },
  emits: [
    'close-user-modal',
    'close-role-modal',
    'close-group-modal',
    'close-category-modal',
    'submit-user',
    'submit-role',
    'submit-group',
    'submit-category'
  ],
  data() {
    return {
      userForm: {
        first_name: '',
        last_name: '',
        phone: '',
        email: '',
        language: '',
        img_url: '',
        unit: '',
        password: '',
        password_confirmation: '',
        address: '',
        role_ids: [],
        is_display_contact: false,
        group_ids: []
      },
      roleForm: {
        name: '',
        description: '',
        access_rights: {
          crud: false,
          view: false,
          comment: false,
          assign: false,
          answerFlow: false
        }
      },
      groupForm: {
        name: '',
        description: ''
      },
      categoryForm: {
        name: '',
        description: ''
      },
      // Options for select fields
      languageOptions: [
        { value: 'en', name: 'English' },
        { value: 'no', name: 'Norwegian' },
        { value: 'da', name: 'Danish' },
        { value: 'sv', name: 'Swedish' }
      ],
      roleOptions: [],
      groupOptions: [],
      isLoadingRoles: false,
      isLoadingGroups: false,
      categoryOptions: [
        { id: 'management', name: 'Management' },
        { id: 'technical', name: 'Technical' },
        { id: 'support', name: 'Support' }
      ],

      // File upload state
      isUploading: false,
      uploadProgress: 0,

      // Validation control flags
      showUserValidation: false,
      showRoleValidation: false,
      showGroupValidation: false,
      showCategoryValidation: false,

      // Loading states
      isSubmittingUser: false,
      isSubmittingRole: false,
      isSubmittingGroup: false,
      isSubmittingCategory: false
    };
  },
  computed: {
    // Validation for user form
    isUserFormValid() {
      const requiredFields = ['first_name', 'last_name', 'email'];

      // Check basic required fields
      const basicFieldsValid = requiredFields.every(field =>
        this.userForm[field] && this.userForm[field].trim() !== ''
      );

      // For new users (add mode), also check password fields
      if (this.modalMode === 'add') {
        const passwordValid = this.userForm.password &&
                             this.userForm.password.trim() !== '' &&
                             this.userForm.password_confirmation &&
                             this.userForm.password_confirmation.trim() !== '';

        const passwordsMatch = this.userForm.password === this.userForm.password_confirmation;

        return basicFieldsValid && passwordValid && passwordsMatch;
      }

      // For edit mode, only check basic fields
      return basicFieldsValid;
    },

    // Email validation
    isEmailValid() {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return !this.userForm.email || emailRegex.test(this.userForm.email);
    },

    // Role form validation
    isRoleFormValid() {
      return this.roleForm.name && this.roleForm.name.trim() !== '';
    },

    // Group form validation
    isGroupFormValid() {
      return this.groupForm.name && this.groupForm.name.trim() !== '';
    },

    // Category form validation
    isCategoryFormValid() {
      return this.categoryForm.name && this.categoryForm.name.trim() !== '';
    },


  },
  watch: {
    currentData: {
      handler(newData) {
        if (newData) {
          this.populateForm(newData);
        } else {
          this.resetForms();
        }
      },
      immediate: true
    },
    showRoleModal: {
      handler(newVal) {
        if (newVal && this.currentData) {
          // Force reactivity update when modal opens
          this.$nextTick(() => {
            this.populateForm(this.currentData);
          });
        }
      }
    }
  },
  mounted() {
    this.loadRoles();
    this.loadGroups();
  },
  methods: {
    // File upload methods
    triggerFileUpload() {
      if (!this.isUploading) {
        this.$refs.fileInput.click();
      }
    },

    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.__showNotif('error', 'Error', this.$t('File Upload.Please select an image file'));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.__showNotif('error', 'Error', this.$t('File Upload.File size must be less than 5MB'));
        return;
      }

      this.uploadFile(file);
    },

    uploadFile(file) {
      this.isUploading = true;
      this.uploadProgress = 0;

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'users'); // Organize uploads in folders

      filesApi.upload(
        formData,
        // Success callback
        (response) => {
          console.log('✅ File uploaded successfully:', response);
          if (response.data && response.data) {
            this.userForm.img_url = response.data;
            console.log('✅ Avatar URL updated:', this.userForm.img_url);
            this.__showNotif('success', 'Success', this.$t('File Upload.Avatar uploaded successfully'));
          }
          this.isUploading = false;
          this.uploadProgress = 0;

          // Clear the file input
          this.$refs.fileInput.value = '';
        },
        // Error callback
        (error) => {
          console.error('❌ File upload failed:', error);
          this.__showNotif('error', 'Error', this.$t('File Upload.Failed to upload image. Please try again.'));
          this.isUploading = false;
          this.uploadProgress = 0;

          // Clear the file input
          this.$refs.fileInput.value = '';
        },
        // Progress callback
        (progress) => {
          this.uploadProgress = progress;
          console.log('📊 Upload progress:', progress + '%');
        },
        // Cancel callback
        (cancelToken) => {
          // Store cancel token if needed for cancellation feature
          this.uploadCancelToken = cancelToken;
        }
      );
    },

    // Modal close handlers - clear forms when modals are closed
    handleUserModalClose() {
      this.resetUserForm();
      this.showUserValidation = false; // Reset validation flag
      this.$emit('close-user-modal');
    },

    handleRoleModalClose() {
      this.resetRoleForm();
      this.showRoleValidation = false; // Reset validation flag
      this.$emit('close-role-modal');
    },

    handleGroupModalClose() {
      this.resetGroupForm();
      this.showGroupValidation = false; // Reset validation flag
      this.$emit('close-group-modal');
    },

    handleCategoryModalClose() {
      this.resetCategoryForm();
      this.showCategoryValidation = false; // Reset validation flag
      this.$emit('close-category-modal');
    },

    removeAvatar() {
      this.$confirmRemove({
        itemName: 'avatar',
        message: this.$t('File Upload.Are you sure you want to remove the avatar?'),
        onConfirm: () => {
          this.userForm.img_url = '';
          console.log('✅ Avatar removed');
          this.__showNotif('success', 'Success', this.$t('File Upload.Avatar removed successfully'));
        }
      });
    },
    getInitials(name) {
      if (!name) return 'DS';
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    },

    populateForm(data) {
      if (this.showUserModal) {
        this.userForm = {
          id: data.id,
          first_name: data.first_name || '',
          last_name: data.last_name || '',
          phone: data.phone || '',
          email: data.email || '',
          language: data.language || 'en',
          img_url: data.img_url || '',
          unit: data.unit || '',
          address: data.address || '',
          role_ids: data.roles ? data.roles.map(role => role.id) : [],
          is_display_contact: data.is_display_contact || false,
          group_ids: data.groups ? data.groups.map(group => group.id) : []
        };

        // Convert language string to object for VueMultiselect
        if (data.language) {
          this.userForm.language = this.languageOptions.find(option => option.value === data.language) || data.language;
        }

        // Convert role IDs to objects for VueMultiselect
        if (data.roles && Array.isArray(data.roles)) {
          // Wait for roles to be loaded before mapping
          this.$nextTick(() => {
            this.userForm.role_ids = data.roles.map(role =>
              this.roleOptions.find(option => option.id === role.id) || role
            ).filter(Boolean); // Remove any undefined values
          });
        }

        // Convert group IDs to objects for VueMultiselect
        if (data.groups && Array.isArray(data.groups)) {
          // Wait for groups to be loaded before mapping
          this.$nextTick(() => {
            this.userForm.group_ids = data.groups.map(group =>
              this.groupOptions.find(option => option.id === group.id) || group
            ).filter(Boolean); // Remove any undefined values
          });
        }
      } else if (this.showRoleModal) {
        const incomingRoleData = { ...data };
        if (typeof incomingRoleData.access_rights === 'string') {
          try {
            incomingRoleData.access_rights = JSON.parse(incomingRoleData.access_rights);
          } catch (e) {
            console.error("Error parsing access_rights string during role edit:", e);
            incomingRoleData.access_rights = {
              crud: false,
              view: false,
              comment: false,
              assign: false,
              answerFlow: false
            };
          }
        } else if (typeof incomingRoleData.access_rights !== 'object' || incomingRoleData.access_rights === null) {
          incomingRoleData.access_rights = {
            crud: false,
            view: false,
            comment: false,
            assign: false,
            answerFlow: false
          };
        }
        // Properly merge the nested access_rights object to ensure reactivity
        this.roleForm = {
          ...this.roleForm,
          ...incomingRoleData,
          access_rights: {
            ...this.roleForm.access_rights,
            ...incomingRoleData.access_rights
          }
        };
      } else if (this.showGroupModal) {
        this.groupForm = { ...this.groupForm, ...data };
      } else if (this.showCategoryModal) {
        this.categoryForm = { ...this.categoryForm, ...data };
      }
    },

    resetForms() {
      this.resetUserForm();
      this.resetRoleForm();
      this.resetGroupForm();
      this.resetCategoryForm();

      // Reset all validation flags
      this.showUserValidation = false;
      this.showRoleValidation = false;
      this.showGroupValidation = false;
      this.showCategoryValidation = false;
    },

    resetUserForm() {
      this.userForm = {
        first_name: '',
        last_name: '',
        phone: '',
        email: '',
        language: '',
        img_url: '',
        unit: '',
        password: '',
        password_confirmation: '',
        address: '',
        role_ids: [],
        is_display_contact: false,
        group_ids: []
      };

      // Clear file input if it exists
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = '';
      }

      // Reset upload state
      this.isUploading = false;
      this.uploadProgress = 0;
    },

    resetRoleForm() {
      this.roleForm = {
        name: '',
        description: '',
        access_rights: {
          crud: false,
          view: false,
          comment: false,
          assign: false,
          answerFlow: false
        }
      };
    },

    resetGroupForm() {
      this.groupForm = {
        name: '',
        description: ''
      };
    },

    resetCategoryForm() {
      this.categoryForm = {
        name: '',
        description: ''
      };
    },

    submitUser() {
      // Enable validation when user tries to submit
      this.showUserValidation = true;

      // Check if form is valid before submitting
      if (!this.isUserFormValid || !this.isEmailValid) {
        return; // Don't submit if validation fails
      }

      // Prevent multiple submissions
      if (this.isSubmittingUser) return;

      // Convert VueMultiselect objects back to proper values for API
      const formData = {
        ...this.userForm,
        language: this.userForm.language?.value || this.userForm.language,
        role_ids: Array.isArray(this.userForm.role_ids)
          ? JSON.stringify(this.userForm.role_ids.map(role => role.id || role))
          : '[]', // Returning '[]' as a string for consistency if the array is empty or not an array
        group_ids: Array.isArray(this.userForm.group_ids)
          ? JSON.stringify(this.userForm.group_ids.map(group => group.id || group))
          : '[]', // Returning '[]' as a string for consistency if the array is empty or not an array
        is_display_contact: this.userForm.is_display_contact ? 1 : 0
      };

      // Remove password fields if editing (not creating new user)
      if (this.modalMode === 'edit') {
        delete formData.password;
        delete formData.password_confirmation;
      }

      // Emit the form data - parent will handle API call and modal closing
      this.$emit('submit-user', formData);
    },

    submitRole() {
      // Enable validation when user tries to submit
      this.showRoleValidation = true;

      // Check if form is valid before submitting
      if (!this.isRoleFormValid) {
        return; // Don't submit if validation fails
      }

      // Prevent multiple submissions
      if (this.isSubmittingRole) return;

      // Create a copy of roleForm to avoid directly mutating the original data
      const formToSubmit = { ...this.roleForm };

      // Stringify the access_rights object
      formToSubmit.access_rights = JSON.stringify(formToSubmit.access_rights);

      // Emit the modified form - parent will handle API call and modal closing
      this.$emit('submit-role', formToSubmit);
    },


    submitGroup() {
      // Enable validation when user tries to submit
      this.showGroupValidation = true;

      // Check if form is valid before submitting
      if (!this.isGroupFormValid) {
        return; // Don't submit if validation fails
      }

      // Prevent multiple submissions
      if (this.isSubmittingGroup) return;

      // Emit the form - parent will handle API call and modal closing
      this.$emit('submit-group', this.groupForm);
    },

    submitCategory() {
      // Enable validation when user tries to submit
      this.showCategoryValidation = true;

      // Check if form is valid before submitting
      if (!this.isCategoryFormValid) {
        return; // Don't submit if validation fails
      }

      // Prevent multiple submissions
      if (this.isSubmittingCategory) return;

      // Emit the form - parent will handle API call and modal closing
      this.$emit('submit-category', this.categoryForm);
    },

    loadRoles() {
      this.isLoadingRoles = true;

      const callback = (response) => {
        this.roleOptions = response.data || [];
        this.isLoadingRoles = false;
      };

      const errorCallback = (error) => {
        console.error('Error loading roles:', error);
        // Fallback to empty array if API fails
        this.roleOptions = [];
        this.isLoadingRoles = false;
      };

      const params = {
        order_by: 'name',
        sort_by: 'asc',
        limit: 999 // Get all roles
      };

      rolesApi.getList(params, callback, errorCallback);
    },

    loadGroups() {
      this.isLoadingGroups = true;

      const callback = (response) => {
        this.groupOptions = response.data || [];
        this.isLoadingGroups = false;
      };

      const errorCallback = (error) => {
        console.error('Error loading groups:', error);
        // Fallback to empty array if API fails
        this.groupOptions = [];
        this.isLoadingGroups = false;
      };

      const params = {
        order_by: 'name',
        sort_by: 'asc',
        limit: 999 // Get all groups
      };

      groupApi.getList(params, callback, errorCallback);
    },

    refreshRolesAndGroups() {
      // Refresh both roles and groups data
      this.loadRoles();
      this.loadGroups();
    },

    // Methods to handle API responses from parent component
    handleUserSubmitStart() {
      this.isSubmittingUser = true;
    },

    handleUserSubmitSuccess() {
      this.isSubmittingUser = false;
      this.__showNotif('success', 'Success', 'User saved successfully');
      // Modal will be closed by parent component
    },

    handleUserSubmitError(errorMessage = 'Failed to save user') {
      this.isSubmittingUser = false;
      this.__showNotif('error', 'Error', errorMessage);
      // Modal stays open for user to fix issues
    },

    handleRoleSubmitStart() {
      this.isSubmittingRole = true;
    },

    handleRoleSubmitSuccess() {
      this.isSubmittingRole = false;
      this.__showNotif('success', 'Success', 'Role saved successfully');
    },

    handleRoleSubmitError(errorMessage = 'Failed to save role') {
      this.isSubmittingRole = false;
      this.__showNotif('error', 'Error', errorMessage);
    },

    handleGroupSubmitStart() {
      this.isSubmittingGroup = true;
    },

    handleGroupSubmitSuccess() {
      this.isSubmittingGroup = false;
      this.__showNotif('success', 'Success', 'Group saved successfully');
    },

    handleGroupSubmitError(errorMessage = 'Failed to save group') {
      this.isSubmittingGroup = false;
      this.__showNotif('error', 'Error', errorMessage);
    },

    handleCategorySubmitStart() {
      this.isSubmittingCategory = true;
    },

    handleCategorySubmitSuccess() {
      this.isSubmittingCategory = false;
      this.__showNotif('success', 'Success', 'Category saved successfully');
    },

    handleCategorySubmitError(errorMessage = 'Failed to save category') {
      this.isSubmittingCategory = false;
      this.__showNotif('error', 'Error', errorMessage);
    }
  }
};
</script>
