<template>
  <div class="space-y-6">
    <!-- Current Collaborators Section -->
    <div class="space-y-4">
      <div class="space-y-3">
        <!-- Creator (always first) -->
        <div
          v-if="inquiryData?.creator"
          class="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center gap-3">
            <img
              :src="inquiryData.creator.avatar || defaultAvatar"
              :alt="inquiryData.creator.first_name"
              class="w-8 h-8 rounded-full"
            >
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ inquiryData.creator.first_name }} {{ inquiryData.creator.last_name }}
              </p>
            </div>
          </div>

          <div class="flex items-center gap-2">
            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
              Creator
            </span>
          </div>
        </div>



        <!-- All Collaborators (including <PERSON><PERSON> and others) -->
        <div
          v-for="collaborator in allCollaborators"
          :key="`${collaborator.id}-${reactiveCollaborators}`"
          class="flex items-center justify-between px-4 py-1 group"
        >
          <div class="flex items-center gap-3">
            <!-- Special icon for System Admin -->
            <!-- Regular user avatar -->
            <img v-if="collaborator.imgUrl" class="h-8 w-8 rounded-full object-cover" :src="collaborator.imgUrl" :alt="collaborator.first_name">
            <div v-else class="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs font-medium">
              {{ generateInitials(collaborator.first_name) }}
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ collaborator.role === 'System Admin' ? 'Desidia' : `${collaborator.first_name} ${collaborator.last_name}` }}
              </p>
            </div>
          </div>

          <div class="flex items-center gap-2">
            <span :class="getRoleBadgeClass(collaborator.role)" class="px-2 py-1 text-xs font-medium rounded-full">
              {{ collaborator.role }}
            </span>

            <button
              @click="removeCollaborator(collaborator)"
              class="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200"
              title="Remove collaborator"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Resources Section -->
    <div class="">
      <!-- Title and Search -->
      <div class="flex items-center justify-between px-3">
        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">Resources</h4>

        <div class="w-64 relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <input
            v-model="searchQuery"
            @input="searchUsers"
            type="text"
            placeholder="Search"
            class="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100 text-sm"
          >
        </div>
      </div>

      <!-- Table Header -->
      <div class="mt-4 grid grid-cols-3 gap-4 px-4 py-2">
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Name</div>
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Category</div>
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Assign as</div>
      </div>

      <!-- User List -->
      <div class="border-b border-gray-200 dark:border-gray-700 rounded-b-lg">
        <div
          v-for="user in availableUsers"
          :key="user.id"
          class="grid grid-cols-3 gap-4 px-4 flex-items-center py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
        >
          <!-- Name -->
          <div class="flex items-center gap-3 ">
            <img v-if="user.imgUrl" class="h-8 w-8 rounded-full object-cover" :src="user.imgUrl" :alt="user.first_name">
            <div v-else class="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs font-medium">
              {{ generateInitials(user.first_name) }}
            </div>
            <div class="ml-3 flex-1 max-w-[200px]">
              <div class="text-sm font-medium text-gray-900 truncate">{{ user.first_name }} {{ user.last_name }}</div>
            </div>
          </div>

          <!-- Category -->
          <div class="flex items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ user.category || '' }}
            </span>
          </div>

          <!-- Assign as Dropdown -->
          <div class="flex items-center pt-1">
            <VueMultiselect
              v-if="assigningUserId !== user.id"
              :value="null"
              :options="getAvailableRoles()"
              :searchable="false"
              :close-on-select="true"
              :show-labels="false"
              :allow-empty="false"
              placeholder="Select Role"
              label="name"
              track-by="value"
              @select="(role) => assignUser(user, role.value)"
              :disabled="isAssigning"
              class="role-multiselect"
            >
              <template #singleLabel="{ option }">
                <span class="text-sm">{{ option.name }}</span>
              </template>
              <template #option="{ option }">
                <div class="flex items-center justify-between w-full">
                  <span class="text-sm">{{ option.name }}</span>
                </div>
              </template>
            </VueMultiselect>

            <!-- Loading state for user being assigned -->
            <div v-else class="flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
              <svg class="animate-spin h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-sm text-blue-600 dark:text-blue-400">Assigning...</span>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div
          v-if="availableUsers.length === 0"
          class="px-4 py-8 text-center text-gray-500 dark:text-gray-400"
        >
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
          <p class="mt-2 text-sm">No users available</p>
          <p class="text-xs text-gray-400">All users are already collaborators or try adjusting your search</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="'danger'"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<style scoped>
/* VueMultiselect styling for role selection */
.role-multiselect {
  min-width: 140px;
}

.role-multiselect :deep(.multiselect) {
  min-height: 32px;
  font-size: 0.875rem;
}

.role-multiselect :deep(.multiselect__tags) {
  min-height: 32px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
}

.role-multiselect :deep(.multiselect__single) {
  font-size: 0.875rem;
  line-height: 1.5;
  padding: 0;
  margin: 0;
}

.role-multiselect :deep(.multiselect__select) {
  height: 30px;
  width: 30px;
}

.role-multiselect :deep(.multiselect__select:before) {
  border-color: #6b7280 transparent transparent;
  border-width: 5px 5px 0;
}

.role-multiselect :deep(.multiselect__content-wrapper) {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.role-multiselect :deep(.multiselect__option) {
  font-size: 0.875rem;
  padding: 8px 12px;
}

.role-multiselect :deep(.multiselect__option--highlight) {
  background: #3b82f6;
  color: white;
}

.role-multiselect :deep(.multiselect__option--selected) {
  background: #e5e7eb;
  color: #374151;
  font-weight: 500;
}

.role-multiselect :deep(.multiselect__placeholder) {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  padding: 0;
}

/* Dark mode support */
.dark .role-multiselect :deep(.multiselect__tags) {
  background: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

.dark .role-multiselect :deep(.multiselect__single) {
  color: #f3f4f6;
}

.dark .role-multiselect :deep(.multiselect__content-wrapper) {
  background: #374151;
  border-color: #4b5563;
}

.dark .role-multiselect :deep(.multiselect__option) {
  color: #f3f4f6;
}

.dark .role-multiselect :deep(.multiselect__option--highlight) {
  background: #3b82f6;
}

.dark .role-multiselect :deep(.multiselect__placeholder) {
  color: #9ca3af;
}
</style>

<script>
import userApi from '@/api/user';
import inquiriesApi from '@/api/inquiries';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';
import VueMultiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.css';

export default {
  name: 'InquiryCollaboratorsTab',
  components: {
    ConfirmationModal,
    VueMultiselect
  },
  mixins: [confirmationMixin],
  props: {
    inquiryData: {
      type: Object,
      required: true
    }
  },
  emits: ['assignment-updated'],
  data() {
    return {
      searchQuery: '',
      allUsers: [],
      currentCollaborators: [],
      isLoading: false,
      isAssigning: false, // Track assignment state to prevent blinking
      assigningUserId: null, // Track which user is being assigned
      defaultAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      roleOptions: [
        { name: 'System Admin', value: 'System Admin' },
        { name: 'Creator', value: 'Creator' },
        { name: 'Executor', value: 'Executor' },
        { name: 'Supervisor', value: 'Supervisor' },
        { name: 'Billing', value: 'Billing' }
      ]
    };
  },
  computed: {
    allCollaborators() {
      const collaborators = [];

      // Add creator first if exists
      if (this.inquiryData?.creator) {
        collaborators.push({
          ...this.inquiryData.creator,
          role: 'Creator',
          isCreator: true
        });
      }

      // Add other collaborators from database
      if (this.currentCollaborators && this.currentCollaborators.length > 0) {
        this.currentCollaborators.forEach(collab => {
          console.log('Individual collaborator structure:', collab);

          // Use pivot_assign_as from database structure - check all possible locations
          let role = null;

          // Check for pivot table data (Laravel relationship)
          if (collab.meta && collab.meta.pivot_assign_as) {
            role = collab.meta.pivot_assign_as;
          }
          console.log('Extracted role for', collab.first_name, ':', role);

          // Add collaborator with role (even if role is null, we'll show it)
          collaborators.push({
            ...collab,
            role: role || 'No Role Assigned',
            isCreator: false
          });
        });
      }

      return collaborators;
    },

    // Force reactivity for collaborators display
    reactiveCollaborators() {
      return this.allCollaborators.length;
    },

    availableUsers() {
      // Get all assigned user IDs including creator
      const assignedIds = [
        ...this.currentCollaborators.map(c => c.id),
        this.inquiryData?.creator?.id
      ].filter(Boolean);

      let filtered = this.allUsers.filter(user => {
        // Keep the user visible if they're currently being assigned
        if (this.assigningUserId === user.id) {
          return true;
        }
        return !assignedIds.includes(user.id);
      });

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(user =>
          user.first_name?.toLowerCase().includes(query) ||
          user.last_name?.toLowerCase().includes(query) ||
          user.email?.toLowerCase().includes(query)
        );
      }

      return filtered;
    }
  },
  watch: {
    inquiryData: {
      handler(newData) {
        if (newData?.collaborators && Array.isArray(newData.collaborators)) {
          console.log('Collaborators data structure:', JSON.parse(JSON.stringify(newData.collaborators)));
          // Convert proxy objects to plain objects and ensure reactivity
          this.currentCollaborators = newData.collaborators.map(collab => ({
            ...JSON.parse(JSON.stringify(collab))
          }));
          console.log('Processed collaborators:', this.currentCollaborators);
        } else {
          this.currentCollaborators = [];
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.loadUsers();
    this.loadCollaborators();
  },
  methods: {
    isRoleAssigned(role) {
      // Check if role is already assigned to any collaborator (including creator)
      return this.allCollaborators.some(collab => collab.role === role);
    },

    getRoleBadgeClass(role) {
      const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';
      switch (role) {
        case 'Creator':
          return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200`;
        case 'System Admin':
          return `${baseClasses} bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200`;
        case 'Executor':
          return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200`;
        case 'Supervisor':
          return `${baseClasses} bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200`;
        case 'Billing':
          return `${baseClasses} bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200`;
        default:
          return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200`;
      }
    },

    getAvailableRoles() {
      // Return all roles - allow multiple users with same role
      return this.roleOptions;
    },
    loadUsers() {
      this.isLoading = true;

      const callback = (response) => {
        this.allUsers = response.data || [];
        this.isLoading = false;
      };

      const errorCallback = (error) => {
        this.__showNotif('error', 'Error', 'Failed to load users');
        this.isLoading = false;
      };

      userApi.getList({ limit: 100 }, callback, errorCallback);
    },

    loadCollaborators() {
      if (this.inquiryData?.collaborators && Array.isArray(this.inquiryData.collaborators)) {
        // Convert proxy objects to plain objects
        this.currentCollaborators = this.inquiryData.collaborators.map(collab => ({
          ...JSON.parse(JSON.stringify(collab))
        }));
        console.log('Loaded collaborators:', this.currentCollaborators);
      } else {
        this.currentCollaborators = [];
      }
    },

    searchUsers() {
      // Search is handled by computed property
    },

    async assignUser(user, role) {
      if (!role) return;

      // Prevent multiple simultaneous assignments
      if (this.isAssigning) return;

      this.isAssigning = true;
      this.assigningUserId = user.id; // Track which user is being assigned

      try {
        const params = {
          user_id: user.id,
          assign_as: role
        };

        const callback = (response) => {
          // Create new collaborator object after successful API call
          const newCollaborator = {
            ...user,
            meta: {
              pivot_assign_as: role
            },
            pivot_assign_as: role,
            assign_as: role,
            addedAt: new Date().toISOString()
          };

          // Add to collaborators after API success
          this.currentCollaborators.push(newCollaborator);

          this.__showNotif('success', 'Success', `${user.first_name} assigned as ${role}`);
          this.$emit('assignment-updated', this.currentCollaborators);

          // Delay clearing the assignment state to prevent blinking
          setTimeout(() => {
            this.isAssigning = false;
            this.assigningUserId = null;
          }, 300);
        };

        const errorCallback = (error) => {
          this.__showNotif('error', 'Error', 'Failed to assign user');
          this.isAssigning = false;
          this.assigningUserId = null;
        };

        inquiriesApi.addCollaborator(this.inquiryData.id, params, callback, errorCallback);
      } catch (error) {
        this.__showNotif('error', 'Error', 'Failed to assign user');
        this.isAssigning = false;
        this.assigningUserId = null;
      }
    },

    removeCollaborator(collaborator) {
      // Don't allow removing the creator
      if (collaborator.isCreator) {
        this.__showNotif('warning', 'Warning', 'Cannot remove the inquiry creator');
        return;
      }

      const displayName = collaborator.role === 'System Admin' ? 'Desidia' : `${collaborator.first_name} ${collaborator.last_name}`;

      this.$confirmRemove({
        itemName: displayName,
        message: this.$t('Confirmation.Are you sure you want to remove this collaborator?'),
        onConfirm: async () => {
          // For non-creator collaborators, remove from currentCollaborators
          if (!collaborator.isCreator) {
            // Optimistic update
            const originalCollaborators = [...this.currentCollaborators];
            this.currentCollaborators = this.currentCollaborators.filter(c => c.id !== collaborator.id);

            try {
              const params = {
                user_id: collaborator.id,
                assign_as: collaborator.pivot_assign_as || collaborator.assign_as || collaborator.role
              };

              const callback = (response) => {
                this.__showNotif('success', 'Success', 'Collaborator removed successfully');
                this.$emit('assignment-updated', this.currentCollaborators);
              };

              const errorCallback = (error) => {
                // Revert optimistic update
                this.currentCollaborators = originalCollaborators;
                this.__showNotif('error', 'Error', 'Failed to remove collaborator');
              };

              inquiriesApi.removeCollaborator(this.inquiryData.id, params, callback, errorCallback);
            } catch (error) {
              // Revert optimistic update
              this.currentCollaborators = originalCollaborators;
              this.__showNotif('error', 'Error', 'Failed to remove collaborator');
            }
          }
        }
      });
    },
    // User management
    generateInitials(first_name) {
      if (!first_name) return 'DS';
      return first_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    },
  }
};
</script>
