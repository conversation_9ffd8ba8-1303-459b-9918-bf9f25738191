<template>
  <div class="space-y-6">
    <!-- Basic Information -->
    <div class="space-y-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Basic Information</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Inquiry Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Inquiry Name
          </label>
          <input
            v-model="localInquiry.name"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
            placeholder="Enter inquiry name"
          >
        </div>

        <!-- Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status
          </label>
          <select
            v-model="localInquiry.status"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
          >
            <option value="pending">Pending</option>
            <option value="in-progress">In Progress</option>
            <option value="review">Under Review</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <!-- Priority -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Priority
          </label>
          <select
            v-model="localInquiry.priority"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>

        <!-- Assigned To -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Assigned To
          </label>
          <select
            v-model="localInquiry.assignedTo"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
          >
            <option value="">Unassigned</option>
            <option 
              v-for="user in availableUsers" 
              :key="user.id" 
              :value="user.id"
            >
              {{ user.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Description -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Description
      </label>
      <textarea
        v-model="localInquiry.description"
        rows="4"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
        placeholder="Enter inquiry description..."
      ></textarea>
    </div>

    <!-- Dates -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Start Date -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Start Date
        </label>
        <input
          v-model="localInquiry.startDate"
          type="date"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
        >
      </div>

      <!-- Due Date -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Due Date
        </label>
        <input
          v-model="localInquiry.due_date"
          type="date"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
        >
      </div>
    </div>

    <!-- Tags -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Tags
      </label>
      <div class="flex flex-wrap gap-2 mb-2">
        <span
          v-for="tag in localInquiry.tags"
          :key="tag"
          class="inline-flex items-center gap-1 px-2 py-1 bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 text-xs font-medium rounded-full"
        >
          {{ tag }}
          <button
            @click="removeTag(tag)"
            class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </span>
      </div>
      <div class="flex gap-2">
        <input
          v-model="newTag"
          @keyup.enter="addTag"
          type="text"
          placeholder="Add a tag..."
          class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
        >
        <button
          @click="addTag"
          class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
        >
          Add
        </button>
      </div>
    </div>

    <!-- Progress -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Progress: {{ localInquiry.progress }}%
      </label>
      <input
        v-model.number="localInquiry.progress"
        type="range"
        min="0"
        max="100"
        step="5"
        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
      >
      <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>0%</span>
        <span>25%</span>
        <span>50%</span>
        <span>75%</span>
        <span>100%</span>
      </div>
    </div>

    <!-- Save Button -->
    <div class="flex justify-end">
      <button
        @click="saveInquiry"
        :disabled="isSaving"
        class="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
      >
        {{ isSaving ? 'Saving...' : 'Save Changes' }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InquiryDetailsTab',
  props: {
    inquiry: {
      type: Object,
      required: true
    },
    availableUsers: {
      type: Array,
      default: () => []
    }
  },
  emits: ['inquiry-updated'],
  data() {
    return {
      localInquiry: { ...this.inquiry },
      newTag: '',
      isSaving: false
    };
  },
  watch: {
    inquiry: {
      handler(newInquiry) {
        this.localInquiry = { ...newInquiry };
      },
      deep: true
    }
  },
  methods: {
    addTag() {
      if (this.newTag.trim() && !this.localInquiry.tags.includes(this.newTag.trim())) {
        this.localInquiry.tags.push(this.newTag.trim());
        this.newTag = '';
      }
    },

    removeTag(tag) {
      const index = this.localInquiry.tags.indexOf(tag);
      if (index > -1) {
        this.localInquiry.tags.splice(index, 1);
      }
    },

    async saveInquiry() {
      this.isSaving = true;
      
      try {
        this.$emit('inquiry-updated', this.localInquiry);
        this.__showNotif('success', 'Success', 'Inquiry updated successfully');
      } catch (error) {
        this.__showNotif('error', 'Error', 'Failed to update inquiry');
      } finally {
        this.isSaving = false;
      }
    }
  }
};
</script>
