<template>
  <div class="relative h-full flex flex-col">
    <loader-circle v-if="isFetching" />
    <!-- Attachments List -->
    <div class="flex-1 overflow-y-auto">
      <!-- Empty State -->
      <div v-if="displayAttachments.length === 0 && !isLoading" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <p class="mt-4 text-sm text-gray-500">No attachments yet</p>
      </div>

      <!-- Attachments List -->
      <div v-if="displayAttachments.length > 0" class="space-y-1">
        <div
          v-for="(attachment, index) in displayAttachments"
          :key="attachment.id || attachment.tempId"
          class="group flex items-center justify-between py-1 px-4 hover:bg-gray-50 transition-colors duration-200 border-b border-gray-200"
          :class="{ 'opacity-50': attachment.isOptimistic }"
        >
          <!-- File Info -->
          <div class="flex items-center gap-4 flex-1 min-w-0">
            <!-- Index Number -->
            <span class="text-sm font-medium text-gray-500 w-6 flex-shrink-0">
              {{ index + 1 }}
            </span>

            <!-- File Name -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ attachment.name || getFileNameFromUrl(attachment.file_url) }}
              </p>
              <p v-if="attachment.file_url" class="text-xs text-gray-500 truncate">
                {{ getFileNameFromUrl(attachment.file_url) }}
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <!-- Edit Button -->
            <button
              @click="editAttachment(attachment)"
              class="p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50 transition-colors duration-200"
              title="Edit"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
              </svg>
            </button>

            <!-- Delete Button -->
            <button
              @click="deleteAttachment(attachment)"
              class="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50 transition-colors duration-200"
              title="Delete"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Progress -->
    <div v-if="uploadProgress.length > 0" class="border-t border-gray-200 p-4 bg-gray-50">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Uploading Files</h4>
      <div class="space-y-2">
        <div
          v-for="progress in uploadProgress"
          :key="progress.name"
          class="flex items-center gap-3"
        >
          <span class="text-sm text-gray-600 flex-1 truncate">{{ progress.name }}</span>
          <div class="w-24 bg-gray-200 rounded-full h-2">
            <div
              class="bg-primary-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: progress.progress + '%' }"
            ></div>
          </div>
          <span class="text-xs text-gray-500 w-10 text-right">{{ progress.progress }}%</span>
        </div>
      </div>
    </div>

    <!-- Add File Button -->
    <div class="flex justify-end py-4">
      <button
        @click="openFileDialog"
        class="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm transition-colors duration-200 flex items-center gap-2"
        :disabled="isUploading"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
        </svg>
        Add File(s)
      </button>

      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        class="hidden"
        multiple
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar"
        @change="handleFileUpload"
      >
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Attachment</h3>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <input
              v-model="editForm.name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter attachment name"
            >
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">File</label>
            <p class="text-sm text-gray-500">{{ getFileNameFromUrl(editForm.file_url) }}</p>
          </div>
        </div>

        <div class="flex justify-end gap-3 mt-6">
          <button
            @click="closeEditModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            @click="saveAttachment"
            :disabled="isSaving"
            class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {{ isSaving ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import attachmentApi from '@/api/attachment';
import fileApi from '@/api/files';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'InquiryAttachmentTab',
  components: {
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  props: {
    inquiryData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // Internal state - no longer dependent on parent
      attachments: [],
      uploadProgress: [],
      isUploading: false,
      isLoading: false,
      isFetching: false,
      showEditModal: false,
      isSaving: false,
      editForm: {
        id: null,
        name: '',
        file_url: '',
        inquiry_id: null
      },
      // For optimistic updates
      optimisticUpdates: {
        creating: [],
        updating: [],
        deleting: []
      }
    };
  },
  computed: {
    // Get inquiry ID from inquiryData
    inquiryId() {
      return this.inquiryData?.id;
    },

    // Computed attachments with optimistic updates applied
    displayAttachments() {
      let result = [...this.attachments];

      // Remove items being deleted (optimistic)
      result = result.filter(item => !this.optimisticUpdates.deleting.includes(item.id));

      // Add items being created (optimistic)
      result = [...result, ...this.optimisticUpdates.creating];

      // Apply updates (optimistic)
      result = result.map(item => {
        const update = this.optimisticUpdates.updating.find(u => u.id === item.id);
        return update ? { ...item, ...update } : item;
      });

      return result;
    }
  },
  mounted() {
    this.loadAttachments();
  },
  watch: {
    // Reload attachments when inquiry changes
    inquiryId(newId, oldId) {
      if (newId && newId !== oldId) {
        this.loadAttachments();
      }
    }
  },
  methods: {
    // Load attachments from API
    loadAttachments() {
      if (!this.inquiryId) return;

      this.isLoading = true;
      this.isFetching = true;

      const params = {
        inquiry_id: this.inquiryId,
        page: 1,
        limit: 100
      };

      const callback = (response) => {
        this.isLoading = false;
        this.isFetching = false;
        this.attachments = response.data || [];
        // Clear optimistic updates after successful load
        this.clearOptimisticUpdates();
      };

      const errorCallback = () => {
        this.isLoading = false;
        this.isFetching = false;
        this.__showNotif('error', 'Error', 'Failed to load attachments');
      };

      attachmentApi.getList(params, callback, errorCallback);
    },

    // Clear all optimistic updates
    clearOptimisticUpdates() {
      this.optimisticUpdates = {
        creating: [],
        updating: [],
        deleting: []
      };
    },

    // Open file dialog
    openFileDialog() {
      this.$refs.fileInput.click();
    },

    // Handle file upload
    handleFileUpload(event) {
      const files = Array.from(event.target.files);
      if (files.length === 0) return;

      this.isUploading = true;
      files.forEach(file => this.uploadFile(file));

      // Reset file input
      event.target.value = '';
    },

    // Upload file to server
    async uploadFile(file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        this.__showNotif('error', 'Error', 'File size must be less than 10MB');
        this.isUploading = false;
        return;
      }

      const progressItem = {
        name: file.name,
        progress: 0
      };
      this.uploadProgress.push(progressItem);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', `inquiry${this.inquiryData?.id}`);

      try {
        const callback = (response) => {
          // Remove from progress
          const index = this.uploadProgress.findIndex(p => p.name === file.name);
          if (index > -1) {
            this.uploadProgress.splice(index, 1);
          }

          // Create attachment record
          this.createAttachment(response.data, file.name);
        };

        const errorCallback = () => {
          // Remove from progress
          const index = this.uploadProgress.findIndex(p => p.name === file.name);
          if (index > -1) {
            this.uploadProgress.splice(index, 1);
          }

          this.__showNotif('error', 'Error', 'Failed to upload file');
          this.checkUploadComplete();
        };

        const progressCallback = (progress) => {
          progressItem.progress = progress;
        };

        fileApi.upload(formData, callback, errorCallback, progressCallback);
      } catch (error) {
        this.__showNotif('error', 'Error', 'Failed to upload file');

        // Remove from progress
        const index = this.uploadProgress.findIndex(p => p.name === file.name);
        if (index > -1) {
          this.uploadProgress.splice(index, 1);
        }
        this.checkUploadComplete();
      }
    },

    // Create attachment record after file upload
    createAttachment(fileUrl, fileName) {
      const tempId = `temp_${Date.now()}_${Math.random()}`;

      // Optimistic update - add to creating list
      const optimisticAttachment = {
        tempId,
        id: tempId,
        file_url: fileUrl,
        inquiry_id: this.inquiryId,
        name: fileName,
        isOptimistic: true,
        created_at: new Date().toISOString()
      };

      this.optimisticUpdates.creating.push(optimisticAttachment);

      const attachmentData = {
        file_url: fileUrl,
        inquiry_id: this.inquiryId,
        name: fileName
      };

      const callback = (response) => {
        // Remove from optimistic creating list
        this.optimisticUpdates.creating = this.optimisticUpdates.creating.filter(
          item => item.tempId !== tempId
        );

        // Add to actual attachments list
        this.attachments.push(response.data);

        this.__showNotif('success', 'Success', 'File uploaded successfully');
        this.checkUploadComplete();
      };

      const errorCallback = () => {
        // Remove from optimistic creating list on error
        this.optimisticUpdates.creating = this.optimisticUpdates.creating.filter(
          item => item.tempId !== tempId
        );

        this.__showNotif('error', 'Error', 'Failed to create attachment record');
        this.checkUploadComplete();
      };

      attachmentApi.create(attachmentData, callback, errorCallback);
    },

    // Check if all uploads are complete
    checkUploadComplete() {
      if (this.uploadProgress.length === 0) {
        this.isUploading = false;
      }
    },

    // Edit attachment
    editAttachment(attachment) {
      this.editForm = {
        id: attachment.id,
        name: attachment.name || '',
        file_url: attachment.file_url || '',
        inquiry_id: attachment.inquiry_id
      };
      this.showEditModal = true;
    },

    // Close edit modal
    closeEditModal() {
      this.showEditModal = false;
      this.editForm = {
        id: null,
        name: '',
        file_url: '',
        inquiry_id: null
      };
    },

    // Save attachment changes
    saveAttachment() {
      if (!this.editForm.name.trim()) {
        this.__showNotif('error', 'Error', 'Description is required');
        return;
      }

      this.isSaving = true;

      // Optimistic update - add to updating list
      const optimisticUpdate = {
        id: this.editForm.id,
        name: this.editForm.name,
        file_url: this.editForm.file_url,
        inquiry_id: this.editForm.inquiry_id
      };

      this.optimisticUpdates.updating.push(optimisticUpdate);

      const updateData = {
        name: this.editForm.name,
        file_url: this.editForm.file_url,
        inquiry_id: this.editForm.inquiry_id
      };

      const callback = (response) => {
        this.isSaving = false;

        // Remove from optimistic updating list
        this.optimisticUpdates.updating = this.optimisticUpdates.updating.filter(
          item => item.id !== this.editForm.id
        );

        // Update the actual attachment in the list
        const index = this.attachments.findIndex(item => item.id === this.editForm.id);
        if (index > -1) {
          this.attachments.splice(index, 1, response.data);
        }

        this.__showNotif('success', 'Success', 'Attachment updated successfully');
        this.closeEditModal();
      };

      const errorCallback = () => {
        this.isSaving = false;

        // Remove from optimistic updating list on error
        this.optimisticUpdates.updating = this.optimisticUpdates.updating.filter(
          item => item.id !== this.editForm.id
        );

        this.__showNotif('error', 'Error', 'Failed to update attachment');
      };

      attachmentApi.update(this.editForm.id, updateData, callback, errorCallback);
    },

    // Delete attachment
    deleteAttachment(attachment) {
      this.$confirmDelete({
        itemName: attachment.name || 'this attachment',
        message: this.$t('Confirmation.Are you sure you want to delete this attachment?'),
        onConfirm: () => {
          // Optimistic update - add to deleting list
          this.optimisticUpdates.deleting.push(attachment.id);
        }
      });

      const callback = () => {
        // Remove from optimistic deleting list
        this.optimisticUpdates.deleting = this.optimisticUpdates.deleting.filter(
          id => id !== attachment.id
        );

        // Remove from actual attachments list
        this.attachments = this.attachments.filter(item => item.id !== attachment.id);

        this.__showNotif('success', 'Success', 'Attachment deleted successfully');
      };

      const errorCallback = () => {
        // Remove from optimistic deleting list on error (restore item)
        this.optimisticUpdates.deleting = this.optimisticUpdates.deleting.filter(
          id => id !== attachment.id
        );

        this.__showNotif('error', 'Error', 'Failed to delete attachment');
      };

      attachmentApi.delete(attachment.id, callback, errorCallback);
    },

    // Get file name from URL
    getFileNameFromUrl(url) {
      if (!url) return 'Unknown file';
      const parts = url.split('/');
      return parts[parts.length - 1] || 'Unknown file';
    }
  }
};
</script>
