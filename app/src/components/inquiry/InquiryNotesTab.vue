<template>
  <div class="relative h-full flex flex-col">
    <!-- Notes Content -->
    <div class="flex-1 overflow-y-auto">
      <!-- Empty State -->
      <div v-if="!notes && !isLoading" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900">No notes yet</h3>
        <p class="mt-2 text-sm text-gray-500">Get started by adding your first note.</p>
      </div>

      <!-- Notes Editor -->
      <div v-else class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Add a Note
          </label>

          <Textarea
            class="mt-3"
            v-model="localNotes"
            :value="localNotes"
            :rows="10"
            placeholder="Write your note here..."
            :isDisabled="isSaving"
          />
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
          <button
            @click="saveNotes"
            :disabled="isSaving || !hasChanges"
            class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
          >
            {{ isSaving ? 'Saving Changes...' : 'Save Changes' }}
          </button>
        </div>

        <!-- Last Updated Info -->
        <div v-if="lastUpdated" class="text-xs text-gray-500 text-right">
          Last updated: {{ formatDate(lastUpdated) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Textarea from '@/components/form/Textarea.vue';
import inquiriesApi from '@/api/inquiries';

export default {
  name: 'InquiryNotesTab',
  components: {
    Textarea
  },
  props: {
    inquiryData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localNotes: '',
      originalNotes: '',
      isSaving: false,
      isLoading: false,
      lastUpdated: null
    };
  },
  computed: {
    // Get inquiry ID from inquiryData
    inquiryId() {
      return this.inquiryData?.id;
    },

    // Get notes from inquiryData
    notes() {
      return this.inquiryData?.notes || '';
    },

    // Check if there are unsaved changes
    hasChanges() {
      return this.localNotes !== this.originalNotes;
    }
  },
  watch: {
    // Watch for changes in inquiry data
    notes: {
      handler(newNotes) {
        if (newNotes !== undefined && !this.isSaving) {
          this.localNotes = newNotes || '';
          this.originalNotes = newNotes || '';
          this.lastUpdated = this.inquiryData?.updated_at;
        }
      },
      immediate: true
    },

    // Watch for inquiry ID changes
    inquiryId(newId, oldId) {
      if (newId && newId !== oldId) {
        this.localNotes = this.notes || '';
        this.originalNotes = this.notes || '';
        this.lastUpdated = this.inquiryData?.updated_at;
      }
    }
  },
  methods: {
    // Save notes using inquiriesApi.updateInquiry
    async saveNotes() {
      if (!this.hasChanges || !this.inquiryId) return;

      this.isSaving = true;

      const params = {
        notes: this.localNotes
      };

      const callback = (response) => {
        this.isSaving = false;
        this.originalNotes = this.localNotes;
        this.lastUpdated = new Date().toISOString();

        // Update the parent component's data
        this.$emit('notes-updated', {
          ...this.inquiryData,
          notes: this.localNotes,
          updated_at: this.lastUpdated
        });

        this.__showNotif('success', 'Success', 'Notes saved successfully');
      };

      const errorCallback = (error) => {
        this.isSaving = false;
        this.__showNotif('error', 'Error', 'Failed to save notes');
        console.error('Failed to save notes:', error);
      };

      try {
        inquiriesApi.updateInquiry(this.inquiryId, params, callback, errorCallback);
      } catch (error) {
        this.isSaving = false;
        this.__showNotif('error', 'Error', 'Failed to save notes');
        console.error('Failed to save notes:', error);
      }
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now - date) / (1000 * 60 * 60);

      if (diffInHours < 1) {
        return 'Just now';
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)} hours ago`;
      } else if (diffInHours < 48) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    },

    // Handle keyboard shortcuts
    handleKeydown(event) {
      // Ctrl/Cmd + S to save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        if (this.hasChanges && !this.isSaving) {
          this.saveNotes();
        }
      }
    }
  },

  mounted() {
    // Add keyboard shortcut listener
    document.addEventListener('keydown', this.handleKeydown);
  },

  beforeUnmount() {
    // Remove keyboard shortcut listener
    document.removeEventListener('keydown', this.handleKeydown);
  }
};
</script>
