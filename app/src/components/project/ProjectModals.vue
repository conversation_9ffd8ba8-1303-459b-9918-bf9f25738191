<template>
  <!-- Add/Edit Project Modal -->
  <Modal
    :isShow="showModal"
    @onClose="closeModal"
    customClass="sm:max-w-4xl"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 px-4 sm:px-6 pb-4 border-b border-gray-200">
        <h3 class="text-lg sm:text-xl font-medium text-gray-900">
          {{ modalMode === 'add' ? 'Add Project' : 'Edit Project' }}
        </h3>

        <!-- Tabs -->
        <div class="mt-4">
          <nav class="-mb-px flex space-x-6 sm:space-x-8">
            <button
              @click="activeTab = 'detail'"
              :class="[
                activeTab === 'detail'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-xs sm:text-sm'
              ]"
            >
              Detail
            </button>
            <button
              @click="activeTab = 'user-access'"
              :class="[
                activeTab === 'user-access'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-xs sm:text-sm'
              ]"
            >
              User Access
            </button>
          </nav>
        </div>
      </div>

      <!-- Scrollable Content -->
      <div v-if="activeTab === 'detail'" class="flex-1 overflow-y-auto px-4 sm:px-6 py-4">
        <loader-circle v-if="isFetching" />
        <!-- Detail Tab -->
        <div v-if="activeTab === 'detail'">
          <form @submit.prevent="submitForm">
            <div class="space-y-4">
              <!-- Project Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Project Name
                </label>
                <TInput
                  v-model="form.name"
                  :value="form.name"
                  type="text"
                  placeholder="Type in your project name"
                />
              </div>

              <!-- Description -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <TInput
                  v-model="form.description"
                  :value="form.description"
                  type="area"
                  placeholder="Type in short description about this project"
                />
              </div>

              <!-- Brief -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Brief
                </label>
                <TInput
                  v-model="form.brief"
                  :value="form.brief"
                  type="area"
                  placeholder="Type in brief about this project"
                />
              </div>

              <!-- Status -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <TInput
                  v-model="form.status"
                  :value="form.status"
                  type="text"
                  placeholder="Project status"
                />
              </div>

              <!-- Is Archived -->
              <!-- <div>
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    v-model="form.is_archived"
                    class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  />
                  <span class="ml-2 text-sm text-gray-700">Archive this project</span>
                </label>
              </div> -->

              <!-- Answer Flow -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Answer Flow
                </label>
                <VueMultiselect
                  v-model="form.answer_flow_ids"
                  :options="answerFlowOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :searchable="true"
                  placeholder="Select Answer Flow"
                  label="name"
                  track-by="id"
                  @input="onAnswerFlowChange"
                />
              </div>

              <!-- Access (Roles) -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Access (Roles)
                </label>
                <VueMultiselect
                  v-model="form.role_ids"
                  :options="roleOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :searchable="true"
                  placeholder="Select Roles"
                  label="name"
                  track-by="id"
                />
              </div>

              <!-- Group -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Group
                </label>
                <VueMultiselect
                  v-model="form.group_ids"
                  :options="groupOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :searchable="true"
                  placeholder="Select Group"
                  label="name"
                  track-by="id"
                />
              </div>

              <!-- Category -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <VueMultiselect
                  v-model="form.category_ids"
                  :options="categoryOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :searchable="true"
                  placeholder="Select Project Category"
                  label="name"
                  track-by="id"
                />
              </div>
            </div>

          </form>
        </div>
      </div>

      <!-- Fixed Footer -->
      <div v-if="activeTab === 'detail'" class="flex-shrink-0 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="closeModal"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="submit"
            color="primary-solid"
            :isLoading="isSubmitting"
            @click="submitForm"
            class="text-xs sm:text-sm"
          >
            Save Changes
          </TButton>
        </div>
      </div>

      <!-- User Access Tab -->
      <div v-if="activeTab === 'user-access'" class="py-4">
        <!-- Header with Add User and Search -->
        <div class="flex justify-between items-center mb-4">
          <TButton
            @click="showAddUserModal = true"
            color="primary-solid"
            class="text-sm text-nowrap mr-4"
          >
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add User
          </TButton>

          <div class="flex items-center gap-3">
            <!-- Search -->
            <div class="relative">
              <TInput
                v-model="userSearchKeyword"
                :value="userSearchKeyword"
                type="text"
                :placeholder="$t('Search')"
                @input="onUserSearch"
                customStyle="padding-left: 2.5rem; padding-right: 2rem;"
              />
              <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
            </div>

            <!-- Filter -->
            <!-- <TButton
              @click="toggleUserFilter"
              color="secondary-solid"
              class="text-sm"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
              </svg>
              Filter
            </TButton> -->
          </div>
        </div>

        <!-- Users Table -->
        <div class="max-h-96 overflow-y-auto">
          <div class="">
            <div
              v-for="(user, index) in projectUsers"
              :key="user.id"
              class="border-b border-gray-200 flex items-center p-3 hover:bg-gray-50 transition-colors group"
            >
              <div class="flex-shrink-0 w-8 text-center">
                <span class="text-sm text-gray-500">{{ (userMeta.current_page - 1) * userMeta.per_page + (index + 1) }}</span>
              </div>
              <div class="flex-shrink-0 h-8 w-8 ml-3">
                <img v-if="user.imgUrl" class="h-8 w-8 rounded-full object-cover" :src="user.imgUrl" :alt="user.first_name">
                <div v-else class="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs font-medium">
                  {{ generateInitials(user.first_name) }}
                </div>
              </div>
              <div class="ml-3 flex-1">
                <div class="text-sm font-medium text-gray-900">{{ user.first_name }} {{ user.last_name }}</div>
                <div class="text-xs text-gray-500">{{ user.email }}</div>
              </div>
              <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  @click="removeUserFromProject(user)"
                  class="text-gray-400 hover:text-red-600 transition-colors p-1"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modern Pagination -->
        <ModernPagination
          v-if="projectUsers.length"
          :total="userMeta.total"
          :current-page="userMeta.current_page"
          :last-page="userMeta.last_page"
          :per-page="userMeta.per_page"
          :is-fetching="false"
          @page-changed="onUserPageChanged"
          @per-page-changed="onUserPerPageChanged"
        />

      </div>

      <!-- Fixed Footer for User Access Tab -->
      <div v-if="activeTab === 'user-access'" class="flex-shrink-0 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="closeModal"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="button"
            @click="activeTab = 'detail'"
            color="primary-solid"
            :isLoading="isSubmitting"
            class="text-xs sm:text-sm"
          >
            Save Changes
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Add User Modal -->
  <Modal
    :isShow="showAddUserModal"
    @onClose="closeAddUserModal"
    customClass="sm:max-w-2xl"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh] ">

      <!-- Fixed Header -->
      <div class="flex-shrink-0 px-4 sm:px-6 pb-4 border-b border-gray-200">
        <h3 class="text-lg sm:text-xl font-medium text-gray-900">Add User</h3>
      </div>

      <!-- Scrollable Content -->
      <div class="flex-1 overflow-y-auto px-4 sm:px-6 py-4">

      <!-- Search Users -->
      <div class="mb-4">
        <div class="flex gap-2">
          <div class="relative flex-1">
            <TInput
              v-model="addUserSearchKeyword"
              :value="addUserSearchKeyword"
              type="text"
              :placeholder="$t('Search')"
              @input="onAddUserSearch"
              customStyle="padding-left: 2.5rem; padding-right: 2rem;"
            />
            <div class="absolute inset-y-0 left-0 flex items-center pl-3">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
          </div>

          <!-- Filter Button -->
          <div class="relative">
            <button
              @click="toggleUserFilter"
              class="inline-flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              :class="{ 'bg-primary-50 border-primary-300 text-primary-700': showUserFilter }"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
              </svg>
              Filter
            </button>

            <!-- Sort Filter Component -->
            <SortFilter
              :showFilter="showUserFilter"
              :orderBy="userOrderBy"
              :sortBy="userSortBy"
              :sortOptions="userSortOptions"
              @update:orderBy="userOrderBy = $event"
              @update:sortBy="userSortBy = $event"
              @update:showFilter="showUserFilter = $event"
              @filter-changed="onUserFilterChanged"
            />
          </div>
        </div>
      </div>

      <!-- Available Users List -->
      <div class="max-h-96 overflow-y-auto">
        <div class="border-b border-gray-200">
          <div
            v-for="user in availableUsers"
            :key="user.id"
            @click="toggleUserSelection(user)"
            class="border-b border-gray-200 group"
            :class="[
              'flex items-center p-3 cursor-pointer transition-colors',
              selectedUsersToAdd.some(u => u.id === user.id)
                ? 'bg-orange-50 border-orange-200'
                : 'hover:bg-gray-50'
            ]"
          >
            <div class="flex-shrink-0 h-8 w-8">
              <img v-if="user.imgUrl" class="h-8 w-8 rounded-full object-cover" :src="user.imgUrl" :alt="user.first_name">
              <div v-else class="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs font-medium">
                {{ generateInitials(user.first_name) }}
              </div>
            </div>
            <div class="ml-3 flex-1">
              <div class="text-sm font-medium text-gray-900">{{ user.first_name }}</div>
            </div>
            <div class="flex items-center">
              <!-- Show checkmark if selected -->
              <div v-if="selectedUsersToAdd.some(u => u.id === user.id)" class="text-orange-500">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <!-- Show + button on hover if not selected -->
              <div v-else class="opacity-0 group-hover:opacity-100 transition-opacity">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Add User Modern Pagination -->
      <ModernPagination
        v-if="availableUsers.length"
        :total="addUserMeta.total"
        :current-page="addUserMeta.current_page"
        :last-page="addUserMeta.last_page"
        :per-page="addUserMeta.per_page"
        :is-fetching="false"
        @page-changed="onAddUserPageChanged"
        @per-page-changed="onAddUserPerPageChanged"
      />

      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 px-4 sm:px-6 pt-4 border-t border-gray-200">
        <div class="flex justify-end gap-3">
          <TButton
            type="button"
            @click="closeAddUserModal"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="button"
            @click="addSelectedUsers"
            color="primary-solid"
            :isLoading="isAddingUsers"
            :disabled="selectedUsersToAdd.length === 0"
            class="text-xs sm:text-sm"
          >
            Add Selected Users
            <span v-if="selectedUsersToAdd.length > 0" class="ml-2 px-2 py-0.5 bg-primary-700 text-white text-xs rounded-full">
              {{ selectedUsersToAdd.length }}
            </span>
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Delete Confirmation Modal -->
  <Modal
    :isShow="showDeleteModal"
    @onClose="closeDeleteModal"
    customClass="sm:max-w-md"
  >
    <!-- Modal Container with Fixed Header/Footer -->
    <div class="flex flex-col h-full max-h-[90vh]">

      <!-- Content (for delete modal, we'll keep it simple without separate header) -->
      <div class="flex-1 overflow-y-auto px-4 sm:px-6 py-2">
        <div class="flex items-center mb-4">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        </div>

        <div class="text-center">
          <h3 class="text-lg sm:text-xl font-medium text-gray-900 mb-2">
            Delete Project
          </h3>
          <p class="text-xs sm:text-sm text-gray-500">
            Are you sure you want to delete "{{ projectToDelete?.name }}"? This action cannot be undone.
          </p>
        </div>
      </div>

      <!-- Fixed Footer -->
      <div class="flex-shrink-0 px-4 pt-4 border-t border-gray-200">
        <div class="flex justify-center gap-3">
          <TButton
            type="button"
            @click="closeDeleteModal"
            color="secondary-solid"
            class="text-xs sm:text-sm"
          >
            Cancel
          </TButton>
          <TButton
            type="button"
            @click="confirmDelete"
            color="red-solid"
            :isLoading="isDeleting"
            class="text-xs sm:text-sm"
          >
            Delete
          </TButton>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import Modal from '@/components/global/Modal.vue';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import VueMultiselect from 'vue-multiselect';
import ModernPagination from '@/components/global/ModernPagination.vue';
import SortFilter from '@/components/global/SortFilter.vue';
import 'vue-multiselect/dist/vue-multiselect.css';
import userApi from '@/api/user.js';
import rolesApi from '@/api/roles.js';
import groupApi from '@/api/group.js';
import categoryApi from '@/api/category.js';
import { computed } from 'vue';
import { useNotebooks } from '@/pages/assistant/hooks/useNotebooks.js';
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'ProjectModals',
  components: {
    Modal,
    TButton,
    TInput,
    VueMultiselect,
    ModernPagination,
    SortFilter,
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    showDeleteModal: {
      type: Boolean,
      default: false
    },
    modalMode: {
      type: String,
      default: 'add'
    },
    form: {
      type: Object,
      default: () => ({
        name: '',
        description: '',
        brief: '',
        status: '',
        is_archived: false,
        role_ids: [],
        group_ids: [],
        answer_flow_ids: [],
        category_ids: [],
        user_access_ids: [],
      })
    },
    projectToDelete: {
      type: Object,
      default: null
    },
    isSubmitting: {
      type: Boolean,
      default: false
    },
    isDeleting: {
      type: Boolean,
      default: false
    }
  },

  watch: {
  },

  watch: {
    'form.answer_flow_ids': {
      handler(newValue) {
        // Prevent infinite loops by checking if conversion is needed
        if (this.isConvertingIds) return;

        if (Array.isArray(newValue) && newValue.length > 0) {
          const needsConversion = newValue.some(item =>
            (typeof item === 'string' || typeof item === 'number') &&
            !this.isJsonString(item)
          );

          if (needsConversion && this.answerFlowOptions && this.answerFlowOptions.length > 0) {
            this.convertIdsToObjects(newValue);
          }
        }
      },
      immediate: true,
      deep: true
    },

    answerFlowOptions: {
      handler(newOptions) {
        // When answerFlowOptions become available, check if we need to convert IDs
        if (this.isConvertingIds) return;

        if (newOptions && newOptions.length > 0 && this.form.answer_flow_ids && this.form.answer_flow_ids.length > 0) {
          const needsConversion = this.form.answer_flow_ids.some(item =>
            (typeof item === 'string' || typeof item === 'number') &&
            !this.isJsonString(item)
          );

          if (needsConversion) {
            this.convertIdsToObjects(this.form.answer_flow_ids);
          }
        }
      },
      immediate: true
    }
  },

  emits: ['close-modal', 'close-delete-modal', 'submit-form', 'confirm-delete', 'save-user-access'],
  setup() {
    const { notebooks } = useNotebooks();
    const answerFlowOptions = computed(() => {
      const options = notebooks.value.map(notebook => ({ id: notebook.id, name: notebook.title || notebook.name || `Notebook ${notebook.id}` }));
      return options;
    });
    return { answerFlowOptions };
  },
  data() {
    return {
      activeTab: 'detail',
      isFetching: false,
      isConvertingIds: false, // Flag to prevent infinite loops
      // Options for multiselect
      roleOptions: [
      ],
      groupOptions: [
      ],
      categoryOptions: [
      ],

      // User Access Tab
      showAddUserModal: false,
      projectUsers: [],
      availableUsers: [],
      selectedUsersToAdd: [],
      userSearchKeyword: '',
      addUserSearchKeyword: '',
      isAddingUsers: false,

      // User pagination
      userMeta: {
        total: 0,
        per_page: 10,
        current_page: 1,
        last_page: 1,
        first_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        previous_page_url: null
      },

      // Add user pagination
      addUserMeta: {
        total: 0,
        per_page: 10,
        current_page: 1,
        last_page: 1,
        first_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        previous_page_url: null
      },

      // User filter data
      showUserFilter: false,
      userOrderBy: 'first_name',
      userSortBy: 'desc',
      userSortOptions: [
        
        { value: 'first_name', label: 'First Name' },
        { value: 'last_name', label: 'Last Name' },
        { value: 'email', label: 'Email' },
        { value: 'created_at', label: 'Created Date' },
        { value: 'updated_at', label: 'Updated Date' }
      ]
    };
  },
  computed: {
    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.userMeta.current_page - 2);
      const end = Math.min(this.userMeta.last_page, this.userMeta.current_page + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    },

    visibleAddUserPages() {
      const pages = [];
      const start = Math.max(1, this.addUserMeta.current_page - 2);
      const end = Math.min(this.addUserMeta.last_page, this.addUserMeta.current_page + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  watch: {
    showModal(newVal) {
      if (newVal) {
        this.activeTab = 'detail';
        this.fetchAvailableUsers();
        if (this.modalMode === 'edit') {
          this.isFetching = true;
          // Initialize project users from form data
          this.projectUsers = this.form.user_access_ids || [];
          this.userMeta.total = this.projectUsers.length;
          this.userMeta.last_page = Math.ceil(this.projectUsers.length / this.userMeta.per_page);
         
        }
      }
    },

    'form.id': {
      handler(newId) {
        if (newId) {
          this.isFetching = false;
          this.fetchAvailableUsers();
          // Check if the first item is just an ID (string/number) rather than an object
          const firstItem = this.form.answer_flow_ids[0];
          if (typeof firstItem === 'string' || typeof firstItem === 'number') {
            console.log('Converting IDs to objects for multiselect');
            this.$nextTick(() => {
              this.convertIdsToObjects(this.form.answer_flow_ids);
            });
          }
        }
      }
    },

    // Watch for changes in user_access_ids to update the display
    'form.user_access_ids': {
      handler(newUserData) {
        if (newUserData && newUserData.length > 0) {
          // If it's already an array of user objects, use directly
          if (Array.isArray(newUserData) && typeof newUserData[0] === 'object') {
            this.projectUsers = newUserData;
            this.userMeta.total = this.projectUsers.length;
            this.userMeta.last_page = Math.ceil(this.projectUsers.length / this.userMeta.per_page);
          } else {
            this.loadUsersFromIds(newUserData);
          }
        } else {
          this.projectUsers = [];
          this.userMeta.total = 0;
          this.userMeta.last_page = 1;
        }
      },
      deep: true
    }
  },
  mounted() {
    this.fetchRoles();
    this.fetchGroups();
    this.fetchCategories();
    // answerFlowOptions populated via useNotebooks hook
  },
  methods: {
    closeModal() {
      this.resetData();
      this.$emit('close-modal');
    },

    closeDeleteModal() {
      this.$emit('close-delete-modal');
    },

    submitForm() {
      this.$emit('submit-form');
    },

    confirmDelete() {
      this.$emit('confirm-delete');
    },

    onAnswerFlowChange() {
      // Handle answer flow selection changes
    },

    isJsonString(str) {
      if (typeof str !== 'string') return false;
      try {
        JSON.parse(str);
        return true;
      } catch {
        return false;
      }
    },

    normalizeAnswerFlowIds(rawIds) {
      if (!rawIds) return [];

      // If it's already an array of objects, return as is
      if (Array.isArray(rawIds) && rawIds.length > 0 && typeof rawIds[0] === 'object' && rawIds[0].id) {
        return rawIds;
      }

      // If it's not an array, try to parse it
      if (!Array.isArray(rawIds)) {
        if (typeof rawIds === 'string') {
          try {
            rawIds = JSON.parse(rawIds);
          } catch {
            return [];
          }
        } else {
          return [];
        }
      }

      // Clean up the array - handle nested JSON strings
      const cleanIds = [];
      for (let item of rawIds) {
        if (typeof item === 'string') {
          // Check if it's a JSON string
          if (this.isJsonString(item)) {
            try {
              const parsed = JSON.parse(item);
              if (Array.isArray(parsed)) {
                cleanIds.push(...parsed);
              } else {
                cleanIds.push(parsed);
              }
            } catch {
              cleanIds.push(item);
            }
          } else {
            cleanIds.push(item);
          }
        } else {
          cleanIds.push(item);
        }
      }

      return cleanIds;
    },

    convertIdsToObjects(rawIds) {
      if (!Array.isArray(this.answerFlowOptions) || this.answerFlowOptions.length === 0) {
        return;
      }

      this.isConvertingIds = true;

      // Normalize the IDs first
      const cleanIds = this.normalizeAnswerFlowIds(rawIds);

      if (!Array.isArray(cleanIds) || cleanIds.length === 0) {
        this.form.answer_flow_ids = [];
        this.isConvertingIds = false;
        return;
      }

      const convertedObjects = cleanIds.map(id => {
        // If it's already an object with id, return it
        if (typeof id === 'object' && id.id) {
          return id;
        }

        // Try exact match first
        let found = this.answerFlowOptions.find(option => option.id === id);

        // If not found, try string comparison
        if (!found) {
          found = this.answerFlowOptions.find(option => String(option.id) === String(id));
        }

        if (found) {
          return found;
        } else {
          // Create placeholder for unknown IDs
          return { id: id, name: `Unknown (${id})` };
        }
      });

      // Update the form with the converted objects
      this.form.answer_flow_ids = convertedObjects;

      this.$nextTick(() => {
        this.isConvertingIds = false;
      });
    },

    saveUserAccess() {
      // Update the form's user_access_ids with current project users (as objects)
      this.form.user_access_ids = this.projectUsers;

      // Emit the save event with the updated user access data
      this.$emit('save-user-access', {
        projectUsers: this.projectUsers,
        userAccessIds: this.form.user_access_ids
      });
    },

    resetData() {
      this.activeTab = 'detail';
      this.projectUsers = [];
      this.selectedUsersToAdd = [];
      this.userSearchKeyword = '';
      this.addUserSearchKeyword = '';
    },

    // Fetch roles from API
    fetchRoles() {
      const params = {
        order_by: 'id',
        sort_by: 'asc',
        page: 1,
        limit: 100
      };

      const callback = (response) => {
        this.roleOptions = response.data || [];
      };

      const errorCallback = (error) => {
        console.error('Fetch roles error:', error);
      };

      rolesApi.getList(params, callback, errorCallback);
    },

    // Fetch groups from API
    fetchGroups() {
      const params = {
        order_by: 'id',
        sort_by: 'asc',
        page: 1,
        limit: 100
      };

      const callback = (response) => {
        this.groupOptions = response.data || [];
      };

      const errorCallback = (error) => {
        console.error('Fetch groups error:', error);
      };

      groupApi.getList(params, callback, errorCallback);
    },

    // Fetch categories from API
    fetchCategories() {
      const params = {
        order_by: 'id',
        sort_by: 'asc',
        page: 1,
        limit: 100
      };

      const callback = (response) => {
        this.categoryOptions = response.data || [];
      };

      const errorCallback = (error) => {
        console.error('Fetch categories error:', error);
      };

      categoryApi.getList(params, callback, errorCallback);
    },

    // Fetch answer flows from API
    fetchAnswerFlows() {
      const callback = (response) => {
        this.answerFlowOptions = response.data || [];
      };

      const errorCallback = (error) => {
        console.error('Error fetching answer flows:', error);
        // Fallback to empty array
        this.answerFlowOptions = [];
      };

      answerFlowsApi.getList({}, callback, errorCallback);
    },

    // User management
    generateInitials(first_name) {
      if (!first_name) return 'DS';
      return first_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    },

    // Load project users when editing existing project
    loadProjectUsers() {
      // For edit mode, users should already be loaded from the project data
      // This method is now mainly for search/pagination functionality
      if (!this.form.id) {
        // For new projects, load users from user_access_ids if any
        if (this.form.user_access_ids && this.form.user_access_ids.length > 0) {
          this.loadUsersFromIds(this.form.user_access_ids);
        }
        return;
      }

      // If we have search keyword, filter the existing users
      if (this.userSearchKeyword) {
        const filtered = this.form.user_access_ids.filter(user =>
          user.first_name?.toLowerCase().includes(this.userSearchKeyword.toLowerCase()) ||
          user.last_name?.toLowerCase().includes(this.userSearchKeyword.toLowerCase()) ||
          user.email?.toLowerCase().includes(this.userSearchKeyword.toLowerCase())
        );
        this.projectUsers = filtered;
        this.userMeta.total = filtered.length;
        this.userMeta.last_page = Math.ceil(filtered.length / this.userMeta.per_page);
      } else {
        // Show all users from form.user_access_ids
        this.projectUsers = this.form.user_access_ids || [];
        this.userMeta.total = this.projectUsers.length;
        this.userMeta.last_page = Math.ceil(this.projectUsers.length / this.userMeta.per_page);
      }
    },

    // Load users from user data (for new projects or fallback)
    loadUsersFromIds(userData) {
      console.log('Loading users from data:', userData);

      if (!userData || userData.length === 0) {
        this.projectUsers = [];
        this.userMeta.total = 0;
        this.userMeta.last_page = 1;
        return;
      }

      // If userData is already an array of user objects, use it directly
      if (Array.isArray(userData) && userData.length > 0 && typeof userData[0] === 'object') {
        this.projectUsers = userData;
      } else {
        // If it's an array of IDs, try to match with available users
        const normalizedUserIds = userData.map(id => parseInt(id)).filter(id => !isNaN(id));
        this.projectUsers = this.availableUsers.filter(user => normalizedUserIds.includes(parseInt(user.id)));

        const missingUserIds = normalizedUserIds.filter(id => !this.availableUsers.some(user => parseInt(user.id) === id));
        if (missingUserIds.length > 0) {
          console.log('Some users not found in available users:', missingUserIds);
        }
      }

      console.log('Final project users:', this.projectUsers);

      // Update meta
      this.userMeta.total = this.projectUsers.length;
      this.userMeta.last_page = Math.ceil(this.projectUsers.length / this.userMeta.per_page);
    },

    fetchAvailableUsers() {
      const params = {
        order_by: this.userOrderBy,
        sort_by: this.userSortBy,
        page: this.addUserMeta.current_page,
        limit: this.addUserMeta.per_page,
      };

      if (this.addUserSearchKeyword) {
        params.keyword = this.addUserSearchKeyword;
      }

      // For edit mode, exclude users already in the project
      if (this.form.id) {
        params.exclude_project_id = this.form.id;
      }

      const callback = (response) => {
        this.availableUsers = response.data || [];
        this.addUserMeta = response.meta || this.addUserMeta;
      };

      const errorCallback = (error) => {
        console.error('Fetch users error:', error);
      };

      userApi.getList(params, callback, errorCallback);
    },

    onUserSearch() {
      this.debounce(this.loadProjectUsers, 300)();
    },

    onAddUserSearch() {
      this.debounce(this.fetchAvailableUsers, 300)();
    },

    toggleUserFilter() {
      this.showUserFilter = !this.showUserFilter;
    },

    onUserFilterChanged(filterData) {
      this.userOrderBy = filterData.order_by;
      this.userSortBy = filterData.sort_by;
      this.addUserMeta.current_page = 1; // Reset to first page when filtering
      this.fetchAvailableUsers();
    },

    // User Access Modal
    closeAddUserModal() {
      this.showAddUserModal = false;
      this.selectedUsersToAdd = [];
      this.addUserSearchKeyword = '';
    },

    toggleUserSelection(user) {
      const index = this.selectedUsersToAdd.findIndex(u => u.id === user.id);
      if (index > -1) {
        this.selectedUsersToAdd.splice(index, 1);
      } else {
        this.selectedUsersToAdd.push(user);
      }
    },

    addSelectedUsers() {
      if (this.selectedUsersToAdd.length === 0) return;

      this.isAddingUsers = true;

      // Add selected users to the form's user_access_ids (store the user objects)
      this.selectedUsersToAdd.forEach(user => {
        if (!this.form.user_access_ids.some(u => u.id === user.id)) {
          this.form.user_access_ids.push(user);
        }
      });

      // Switch to Detail tab
      this.activeTab = 'detail';

      this.isAddingUsers = false;
      this.closeAddUserModal();
    },

    removeUserFromProject(user) {
      this.$confirmRemove({
        itemName: user.first_name,
        message: this.$t('Confirmation.Are you sure you want to remove this user from the project?'),
        onConfirm: () => {
          // Remove from form data
          this.form.user_access_ids = this.form.user_access_ids.filter(u => u.id !== user.id);

          // Remove from display list
          this.projectUsers = this.projectUsers.filter(pu => pu.id !== user.id);

          // Update meta
          this.userMeta.total = this.projectUsers.length;
          this.userMeta.last_page = Math.ceil(this.projectUsers.length / this.userMeta.per_page);

          // For existing projects, the user removal will be saved when the form is submitted
        }
      });
    },

    // Pagination methods for project users
    prevUserPage() {
      if (this.userMeta.current_page > 1) {
        this.userMeta.current_page--;
        this.loadProjectUsers();
      }
    },

    nextUserPage() {
      if (this.userMeta.current_page < this.userMeta.last_page) {
        this.userMeta.current_page++;
        this.loadProjectUsers();
      }
    },

    goToUserPage(page) {
      this.userMeta.current_page = page;
      this.loadProjectUsers();
    },

    changeUserPerPage() {
      this.userMeta.current_page = 1;
      this.loadProjectUsers();
    },

    // Pagination methods for add user modal
    prevAddUserPage() {
      if (this.addUserMeta.current_page > 1) {
        this.addUserMeta.current_page--;
        this.fetchAvailableUsers();
      }
    },

    nextAddUserPage() {
      if (this.addUserMeta.current_page < this.addUserMeta.last_page) {
        this.addUserMeta.current_page++;
        this.fetchAvailableUsers();
      }
    },

    goToAddUserPage(page) {
      this.addUserMeta.current_page = page;
      this.fetchAvailableUsers();
    },

    changeAddUserPerPage() {
      this.addUserMeta.current_page = 1;
      this.fetchAvailableUsers();
    },

    // New pagination methods for ModernPagination component
    onUserPageChanged(page) {
      this.userMeta.current_page = page;
      this.loadProjectUsers();
    },

    onUserPerPageChanged(per_page) {
      this.userMeta.per_page = per_page;
      this.userMeta.current_page = 1;
      this.loadProjectUsers();
    },

    onAddUserPageChanged(page) {
      this.addUserMeta.current_page = page;
      this.fetchAvailableUsers();
    },

    onAddUserPerPageChanged(per_page) {
      this.addUserMeta.per_page = per_page;
      this.addUserMeta.current_page = 1;
      this.fetchAvailableUsers();
    },

    // Utility methods
    debounce(func, wait) {
      let timeout;
      return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
      };
    }
  }
};
</script>
