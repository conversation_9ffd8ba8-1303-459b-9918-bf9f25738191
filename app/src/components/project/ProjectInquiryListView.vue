<template>
  <div class="h-full flex flex-col">
    <!-- Table Header -->
    <div class="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-12 gap-4 px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
        <div class="col-span-4">Inquiry</div>
        <div class="col-span-2">Assignee</div>
        <div class="col-span-2">Status</div>
        <div class="col-span-2">Due Date</div>
        <div class="col-span-1">Priority</div>
        <div class="col-span-1">Actions</div>
      </div>
    </div>

    <!-- Table Content -->
    <div class="flex-1 overflow-y-auto">
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>

      <!-- Empty State -->
      <div v-else-if="inquiries.length === 0" class="flex flex-col items-center justify-center h-64">
        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">No inquiries found</h3>
        <p class="text-gray-500 dark:text-gray-400">Get started by creating your first inquiry.</p>
      </div>

      <!-- Inquiry List -->
      <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
        <div
          v-for="inquiry in inquiries"
          :key="inquiry.id"
          @click="$emit('inquiry-click', inquiry)"
          class="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200"
        >
          <!-- Inquiry Info -->
          <div class="col-span-4">
            <div class="flex items-start space-x-3">
              <!-- Priority Indicator -->
              <div 
                class="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                :class="getPriorityColor(inquiry.priority)"
              ></div>
              
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {{ inquiry.title }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
                  {{ inquiry.description }}
                </p>
                
                <!-- Subinquiries indicator -->
                <div v-if="inquiry.subinquiries && inquiry.subinquiries.length > 0" class="flex items-center mt-2">
                  <svg class="w-3 h-3 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ inquiry.subinquiries.filter(st => st.completed).length }}/{{ inquiry.subinquiries.length }} subinquiries
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Assignee -->
          <div class="col-span-2 flex items-center">
            <div v-if="inquiry.assignee" class="flex items-center space-x-2">
              <img 
                :src="inquiry.assignee.avatar || '/api/placeholder/24/24'" 
                :alt="inquiry.assignee.name"
                class="w-6 h-6 rounded-full"
              />
              <span class="text-sm text-gray-900 dark:text-gray-100 truncate">
                {{ inquiry.assignee.name }}
              </span>
            </div>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">Unassigned</span>
          </div>

          <!-- Status -->
          <div class="col-span-2 flex items-center">
            <span 
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="getStatusColor(inquiry.status)"
            >
              {{ getStatusLabel(inquiry.status) }}
            </span>
          </div>

          <!-- Due Date -->
          <div class="col-span-2 flex items-center">
            <div v-if="inquiry.due_date" class="flex items-center space-x-1">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
              <span 
                class="text-sm"
                :class="getDueDateColor(inquiry.due_date)"
              >
                {{ formatDate(inquiry.due_date) }}
              </span>
            </div>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">No due date</span>
          </div>

          <!-- Priority -->
          <div class="col-span-1 flex items-center">
            <span 
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
              :class="getPriorityBadgeColor(inquiry.priority)"
            >
              {{ getPriorityLabel(inquiry.priority) }}
            </span>
          </div>

          <!-- Actions -->
          <div class="col-span-1 flex items-center justify-end">
            <div class="relative">
              <button
                @click.stop="toggleInquiryMenu(inquiry.id)"
                class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div
                v-if="activeInquiryMenu === inquiry.id"
                @click.stop
                class="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10"
              >
                <div class="py-1">
                  <button
                    @click="editInquiry(inquiry)"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Edit Inquiry
                  </button>
                  <button
                    @click="duplicateInquiry(inquiry)"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Duplicate
                  </button>
                  <button
                    @click="deleteInquiry(inquiry)"
                    class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Global Confirmation Modal -->
  <ConfirmationModal
    :isShow="confirmationModal.isShow"
    :title="confirmationModal.title"
    :message="confirmationModal.message"
    :type="confirmationModal.type"
    :confirmText="confirmationModal.confirmText"
    :cancelText="confirmationModal.cancelText"
    :confirmButtonColor="confirmationModal.confirmButtonColor"
    :isLoading="confirmationModal.isLoading"
    @confirm="handleConfirmationConfirm"
    @cancel="handleConfirmationCancel"
  />
</template>

<script>
import ConfirmationModal from '@/components/global/ConfirmationModal.vue';
import confirmationMixin from '@/mixins/confirmationMixin.js';

export default {
  name: 'ProjectInquiryListView',
  components: {
    ConfirmationModal
  },
  mixins: [confirmationMixin],
  props: {
    inquiries: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['inquiry-click', 'inquiry-updated', 'inquiry-deleted'],
  data() {
    return {
      activeInquiryMenu: null
    };
  },
  mounted() {
    // Close menu when clicking outside
    document.addEventListener('click', this.closeInquiryMenu);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.closeInquiryMenu);
  },
  methods: {
    toggleInquiryMenu(inquiryId) {
      this.activeInquiryMenu = this.activeInquiryMenu === inquiryId ? null : inquiryId;
    },

    closeInquiryMenu() {
      this.activeInquiryMenu = null;
    },

    editInquiry(inquiry) {
      this.closeInquiryMenu();
      this.$emit('inquiry-updated', inquiry);
    },

    duplicateInquiry(inquiry) {
      this.closeInquiryMenu();
      // Emit duplicate event or handle duplication logic
      console.log('Duplicate inquiry:', inquiry);
    },

    deleteInquiry(inquiry) {
      this.closeInquiryMenu();
      this.$confirmDelete({
        itemName: inquiry.title,
        message: this.$t('Confirmation.Are you sure you want to delete this inquiry?'),
        onConfirm: () => {
          this.$emit('inquiry-deleted', inquiry);
        }
      });
    },

    getPriorityColor(priority) {
      const colors = {
        high: 'bg-red-500',
        medium: 'bg-yellow-500',
        low: 'bg-green-500'
      };
      return colors[priority] || 'bg-gray-400';
    },

    getPriorityBadgeColor(priority) {
      const colors = {
        high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      };
      return colors[priority] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    },

    getPriorityLabel(priority) {
      const labels = {
        high: 'High',
        medium: 'Med',
        low: 'Low'
      };
      return labels[priority] || 'None';
    },

    getStatusColor(status) {
      const colors = {
        'todo': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        'in-progress': 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200',
        'review': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        'done': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      };
      return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    },

    getStatusLabel(status) {
      const labels = {
        'todo': 'To Do',
        'in-progress': 'In Progress',
        'review': 'Review',
        'done': 'Done'
      };
      return labels[status] || status;
    },

    getDueDateColor(due_date) {
      const today = new Date();
      const due = new Date(due_date);
      const diffTime = due - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return 'text-red-600 dark:text-red-400'; // Overdue
      } else if (diffDays <= 3) {
        return 'text-yellow-600 dark:text-yellow-400'; // Due soon
      } else {
        return 'text-gray-600 dark:text-gray-400'; // Normal
      }
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
  }
};
</script>
