<template>
  <div class="h-full flex flex-col">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center h-64">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
    </div>

    <!-- Kanban Board -->
    <div v-else class="flex-1 overflow-x-auto">
      <div class="flex h-full gap-6 p-6 min-w-max">
        <!-- Column -->
        <div
          v-for="column in columns"
          :key="column.id"
          class="flex flex-col w-80 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <!-- Column Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-2">
              <div 
                class="w-3 h-3 rounded-full"
                :style="{ backgroundColor: column.color }"
              ></div>
              <h3 class="font-medium text-gray-900 dark:text-gray-100">{{ column.title }}</h3>
              <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">
                {{ getColumnInquiryCount(column.id) }}
              </span>
            </div>
            
            <!-- Add Inquiry Button -->
            <button
              @click="addInquiryToColumn(column.id)"
              class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded transition-colors duration-200"
              title="Add Inquiry"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
            </button>
          </div>

          <!-- inquiries Container -->
          <div 
            class="flex-1 p-4 space-y-3 overflow-y-auto"
            @drop="onDrop($event, column.id)"
            @dragover.prevent
            @dragenter.prevent
          >
            <!-- Inquiry Card -->
            <div
              v-for="inquiry in getColumnInquiries(column.id)"
              :key="inquiry.id"
              @click="$emit('inquiry-click', inquiry)"
              @dragstart="onDragStart($event, inquiry)"
              draggable="true"
              class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow duration-200"
            >
              <!-- Inquiry Header -->
              <div class="flex items-start justify-between mb-3">
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2">
                  {{ inquiry.name }}
                </h4>
                
                <!-- Priority Indicator -->
                <div 
                  class="w-2 h-2 rounded-full flex-shrink-0 ml-2 mt-1"
                  :class="getPriorityColor(inquiry.priority)"
                ></div>
              </div>

              <!-- Inquiry Description -->
              <p v-if="inquiry.description" class="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {{ inquiry.description }}
              </p>

              <!-- Inquiry Meta -->
              <div class="space-y-2">
                <!-- Due Date -->
                <div v-if="inquiry.due_date" class="flex items-center space-x-1">
                  <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                  </svg>
                  <span 
                    class="text-xs"
                    :class="getDueDateColor(inquiry.due_date)"
                  >
                    {{ formatDate(inquiry.due_date) }}
                  </span>
                </div>

                <!-- Subinquiries Progress -->
                <div v-if="inquiry.subinquiries && inquiry.subinquiries.length > 0" class="flex items-center space-x-2">
                  <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  <div class="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-1">
                    <div 
                      class="bg-primary-600 h-1 rounded-full transition-all duration-300"
                      :style="{ width: getSubinquiryProgress(inquiry.subinquiries) + '%' }"
                    ></div>
                  </div>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ inquiry.subinquiries.filter(st => st.completed).length }}/{{ inquiry.subinquiries.length }}
                  </span>
                </div>

                <!-- Assignee and Actions -->
                <div class="flex items-center justify-between">
                  <!-- Assignee -->
                  <div v-if="inquiry.assignee" class="flex items-center space-x-1">
                    <img 
                      :src="inquiry.assignee.avatar || '/api/placeholder/20/20'" 
                      :alt="inquiry.assignee.name"
                      class="w-5 h-5 rounded-full"
                    />
                    <span class="text-xs text-gray-600 dark:text-gray-400 truncate max-w-20">
                      {{ inquiry.assignee.name }}
                    </span>
                  </div>
                  <div v-else class="text-xs text-gray-500 dark:text-gray-400">Unassigned</div>

                  <!-- Inquiry Actions -->
                  <div class="flex items-center space-x-1">
                    <!-- Attachments -->
                    <div v-if="inquiry.attachments && inquiry.attachments.length > 0" class="flex items-center space-x-1">
                      <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 002.828-2.828l-2.828-2.828a2 2 0 00-2.828 0L7 14v3h3l6.586-6.586a2 2 0 000-2.828L15.172 7z"/>
                      </svg>
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ inquiry.attachments.length }}</span>
                    </div>

                    <!-- Comments -->
                    <div v-if="inquiry.comments && inquiry.comments.length > 0" class="flex items-center space-x-1">
                      <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 21l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"/>
                      </svg>
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ inquiry.comments.length }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty Column State -->
            <div v-if="getColumnInquiries(column.id).length === 0" class="text-center py-8">
              <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
              </svg>
              <p class="text-sm text-gray-500 dark:text-gray-400">No inquiries</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProjectInquiryKanbanView',
  props: {
    inquiries: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['inquiry-click', 'inquiry-moved', 'inquiry-updated', 'inquiry-deleted'],
  data() {
    return {
      draggedInquiry: null
    };
  },
  methods: {
    getColumnInquiries(columnId) {
      return this.inquiries.filter(inquiry => inquiry.status === columnId);
    },

    getColumnInquiryCount(columnId) {
      return this.getColumnInquiries(columnId).length;
    },

    onDragStart(event, inquiry) {
      this.draggedInquiry = inquiry;
      event.dataTransfer.effectAllowed = 'move';
    },

    onDrop(event, columnId) {
      event.preventDefault();
      
      if (this.draggedInquiry && this.draggedinquiry.status !== columnId) {
        const updatedInquiry = {
          ...this.draggedInquiry,
          status: columnId
        };
        
        this.$emit('inquiry-moved', {
          inquiry: updatedInquiry,
          fromColumn: this.draggedinquiry.status,
          toColumn: columnId
        });
      }
      
      this.draggedInquiry = null;
    },

    addInquiryToColumn(columnId) {
      this.$emit('add-inquiry', { columnId });
    },

    getPriorityColor(priority) {
      const colors = {
        high: 'bg-red-500',
        medium: 'bg-yellow-500',
        low: 'bg-green-500'
      };
      return colors[priority] || 'bg-gray-400';
    },

    getDueDateColor(due_date) {
      const today = new Date();
      const due = new Date(due_date);
      const diffTime = due - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return 'text-red-600 dark:text-red-400'; // Overdue
      } else if (diffDays <= 3) {
        return 'text-yellow-600 dark:text-yellow-400'; // Due soon
      } else {
        return 'text-gray-600 dark:text-gray-400'; // Normal
      }
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    },

    getSubinquiryProgress(subinquiries) {
      if (!subinquiries || subinquiries.length === 0) return 0;
      const completed = subinquiries.filter(st => st.completed).length;
      return Math.round((completed / subinquiries.length) * 100);
    }
  }
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
