<template>
  <div class="h-full flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ project.name }}</h1>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ project.description }}</p>
      </div>
      
      <!-- View Toggle -->
      <div class="flex items-center gap-4">
        <!-- Search -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search inquiries..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        <!-- View Toggle Buttons -->
        <div class="flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden">
          <button
            @click="activeView = 'list'"
            :class="[
              activeView === 'list'
                ? 'bg-primary-600 text-white'
                : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600',
              'px-4 py-2 text-sm font-medium transition-colors duration-200'
            ]"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
            </svg>
          </button>
          <button
            @click="activeView = 'kanban'"
            :class="[
              activeView === 'kanban'
                ? 'bg-primary-600 text-white'
                : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600',
              'px-4 py-2 text-sm font-medium transition-colors duration-200'
            ]"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"/>
            </svg>
          </button>
        </div>

        <!-- Add Inquiry Button -->
        <button
          @click="$emit('add-inquiry')"
          class="inline-flex items-center gap-2 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Inquiry
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex-1 overflow-hidden">
      <!-- List View -->
      <div v-if="activeView === 'list'" class="h-full">
        <ProjectInquiryListView
          :inquiries="filteredInquiries"
          :loading="loading"
          @inquiry-click="$emit('inquiry-click', $event)"
          @inquiry-updated="$emit('inquiry-updated', $event)"
          @inquiry-deleted="$emit('inquiry-deleted', $event)"
        />
      </div>

      <!-- Kanban View -->
      <div v-else class="h-full">
        <ProjectInquiryKanbanView
          :inquiries="filteredInquiries"
          :columns="kanbanColumns"
          :loading="loading"
          @inquiry-click="$emit('inquiry-click', $event)"
          @inquiry-moved="$emit('inquiry-moved', $event)"
          @inquiry-updated="$emit('inquiry-updated', $event)"
          @inquiry-deleted="$emit('inquiry-deleted', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ProjectInquiryListView from './ProjectInquiryListView.vue';
import ProjectInquiryKanbanView from './ProjectInquiryKanbanView.vue';

export default {
  name: 'ProjectInquiryList',
  components: {
    ProjectInquiryListView,
    ProjectInquiryKanbanView,
  },
  props: {
    project: {
      type: Object,
      required: true
    },
    inquiries: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    kanbanColumns: {
      type: Array,
      default: () => [
        { id: 'todo', name: 'To Do', color: '#6B7280' },
        { id: 'in-progress', name: 'In Progress', color: '#F59E0B' },
        { id: 'review', name: 'Review', color: '#8B5CF6' },
        { id: 'done', name: 'Done', color: '#10B981' }
      ]
    }
  },
  emits: [
    'add-inquiry',
    'inquiry-click',
    'inquiry-updated',
    'inquiry-deleted',
    'inquiry-moved'
  ],
  data() {
    return {
      activeView: 'list', // 'list' or 'kanban'
      searchQuery: ''
    };
  },
  computed: {
    filteredInquiries() {
      if (!this.searchQuery) {
        return this.inquiries;
      }
      
      const query = this.searchQuery.toLowerCase();
      return this.inquiries.filter(inquiry => 
        inquiry.title?.toLowerCase().includes(query) ||
        inquiry.description?.toLowerCase().includes(query) ||
        inquiry.assignee?.name?.toLowerCase().includes(query) ||
        inquiry.status?.toLowerCase().includes(query)
      );
    }
  },
  methods: {
    setView(view) {
      this.activeView = view;
      this.$emit('view-changed', view);
    }
  }
};
</script>
