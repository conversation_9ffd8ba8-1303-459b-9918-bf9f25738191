:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #F24822;
  text-decoration: inherit;
}
a:hover {
  color: #d73502;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #F24822;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #d73502;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Global Responsive Typography System */
:root {
  /* Mobile-first base sizes */
  --base-font-size: 14px;
  --heading-1-size: calc(var(--base-font-size) * 2);
  --heading-2-size: calc(var(--base-font-size) * 1.75);
  --heading-3-size: calc(var(--base-font-size) * 1.5);
  --heading-4-size: calc(var(--base-font-size) * 1.25);
  --paragraph-size: var(--base-font-size);
  --small-text-size: calc(var(--base-font-size) * 0.875);
  --tiny-text-size: calc(var(--base-font-size) * 0.75);

  /* Spacing variables */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

/* Tablet breakpoint */
@media (min-width: 640px) {
  :root {
    --base-font-size: 15px;
    --heading-1-size: calc(var(--base-font-size) * 2.25);
    --heading-2-size: calc(var(--base-font-size) * 1.875);
    --heading-3-size: calc(var(--base-font-size) * 1.625);
    --heading-4-size: calc(var(--base-font-size) * 1.375);
  }
}

/* Desktop breakpoint */
@media (min-width: 1024px) {
  :root {
    --base-font-size: 16px;
    --heading-1-size: calc(var(--base-font-size) * 2.5);
    --heading-2-size: calc(var(--base-font-size) * 2);
    --heading-3-size: calc(var(--base-font-size) * 1.75);
    --heading-4-size: calc(var(--base-font-size) * 1.5);
  }
}

/* Apply responsive typography */
body {
  font-size: var(--base-font-size);
  line-height: 1.6;
}

h1 {
  font-size: var(--heading-1-size);
  line-height: 1.2;
}

h2 {
  font-size: var(--heading-2-size);
  line-height: 1.3;
}

h3 {
  font-size: var(--heading-3-size);
  line-height: 1.4;
}

h4 {
  font-size: var(--heading-4-size);
  line-height: 1.4;
}

p {
  font-size: var(--paragraph-size);
  line-height: 1.6;
}

small {
  font-size: var(--small-text-size);
  line-height: 1.5;
}

/* Utility classes for responsive text */
.text-responsive-xs {
  font-size: var(--tiny-text-size);
}

.text-responsive-sm {
  font-size: var(--small-text-size);
}

.text-responsive-base {
  font-size: var(--base-font-size);
}

.text-responsive-lg {
  font-size: var(--heading-4-size);
}

.text-responsive-xl {
  font-size: var(--heading-3-size);
}

.text-responsive-2xl {
  font-size: var(--heading-2-size);
}

.text-responsive-3xl {
  font-size: var(--heading-1-size);
}

/* Compact mode for dense interfaces */
.compact-mode {
  --base-font-size: 13px;
  --spacing-xs: 0.125rem;
  --spacing-sm: 0.25rem;
  --spacing-md: 0.5rem;
  --spacing-lg: 0.75rem;
  --spacing-xl: 1rem;
}

/* Extended mode for better readability */
.extended-mode {
  --base-font-size: 17px;
  --spacing-xs: 0.375rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1.25rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;
}

/* Assistant-specific responsive classes */
.assistant-compact {
  --base-font-size: 13px;
}

@media (min-width: 640px) {
  .assistant-compact {
    --base-font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .assistant-compact {
    --base-font-size: 15px;
  }
}

/* You can override locally */
.parent-container {
  --base-font-size: 18px; /* Override for this specific branch */
}