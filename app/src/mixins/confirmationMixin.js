/**
 * Global Confirmation Modal Mixin
 * 
 * This mixin provides a standardized way to show confirmation dialogs
 * across all components, replacing browser confirm() calls.
 * 
 * Usage:
 * 1. Import and use the mixin in your component
 * 2. Add ConfirmationModal to your template
 * 3. Use this.$confirm() instead of confirm()
 * 
 * Example:
 * this.$confirm({
 *   title: 'Delete Item',
 *   message: 'Are you sure you want to delete this item?',
 *   type: 'danger',
 *   confirmText: 'Delete',
 *   onConfirm: () => { this.deleteItem(); }
 * });
 */

export default {
  data() {
    return {
      // Confirmation modal state
      confirmationModal: {
        isShow: false,
        title: '',
        message: '',
        type: 'warning', // warning, danger, info, question
        confirmText: '',
        cancelText: '',
        confirmButtonColor: 'red-solid',
        isLoading: false,
        onConfirm: null,
        onCancel: null
      }
    };
  },
  
  methods: {
    /**
     * Show confirmation modal
     * @param {Object} options - Configuration options
     * @param {string} options.title - Modal title
     * @param {string} options.message - Modal message
     * @param {string} options.type - Modal type (warning, danger, info, question)
     * @param {string} options.confirmText - Confirm button text
     * @param {string} options.cancelText - Cancel button text
     * @param {string} options.confirmButtonColor - Confirm button color
     * @param {Function} options.onConfirm - Callback when confirmed
     * @param {Function} options.onCancel - Callback when cancelled
     * @returns {Promise} Promise that resolves with true/false
     */
    $confirm(options = {}) {
      return new Promise((resolve) => {
        // Set modal configuration
        this.confirmationModal = {
          isShow: true,
          title: options.title || this.$t('Confirmation.Confirm Action'),
          message: options.message || this.$t('Confirmation.Are you sure you want to proceed?'),
          type: options.type || 'warning',
          confirmText: options.confirmText || this.$t('Buttons.Confirm'),
          cancelText: options.cancelText || this.$t('Buttons.Cancel'),
          confirmButtonColor: options.confirmButtonColor || (options.type === 'danger' ? 'red-solid' : 'primary-solid'),
          isLoading: false,
          onConfirm: () => {
            if (options.onConfirm) {
              // If onConfirm returns a promise, handle loading state
              const result = options.onConfirm();
              if (result && typeof result.then === 'function') {
                this.confirmationModal.isLoading = true;
                result
                  .then(() => {
                    this.confirmationModal.isLoading = false;
                    this.closeConfirmationModal();
                    resolve(true);
                  })
                  .catch(() => {
                    this.confirmationModal.isLoading = false;
                    // Don't close modal on error, let user try again
                  });
              } else {
                this.closeConfirmationModal();
                resolve(true);
              }
            } else {
              this.closeConfirmationModal();
              resolve(true);
            }
          },
          onCancel: () => {
            if (options.onCancel) {
              options.onCancel();
            }
            this.closeConfirmationModal();
            resolve(false);
          }
        };
      });
    },
    
    /**
     * Shorthand for delete confirmation
     * @param {Object} options - Configuration options
     * @param {string} options.itemName - Name of item being deleted
     * @param {Function} options.onConfirm - Delete callback
     */
    $confirmDelete(options = {}) {
      const itemName = options.itemName || 'this item';
      return this.$confirm({
        title: options.title || this.$t('Confirmation.Delete Confirmation'),
        message: options.message || `Are you sure you want to delete "${itemName}"? ${this.$t('Confirmation.This action cannot be undone')}.`,
        type: 'danger',
        confirmText: options.confirmText || this.$t('Buttons.Delete'),
        confirmButtonColor: 'red-solid',
        onConfirm: options.onConfirm,
        onCancel: options.onCancel
      });
    },
    
    /**
     * Shorthand for remove confirmation
     * @param {Object} options - Configuration options
     * @param {string} options.itemName - Name of item being removed
     * @param {Function} options.onConfirm - Remove callback
     */
    $confirmRemove(options = {}) {
      const itemName = options.itemName || 'this item';
      return this.$confirm({
        title: options.title || this.$t('Confirmation.Remove Confirmation'),
        message: options.message || `Are you sure you want to remove "${itemName}"?`,
        type: 'warning',
        confirmText: options.confirmText || this.$t('Buttons.Remove'),
        confirmButtonColor: 'yellow-solid',
        onConfirm: options.onConfirm,
        onCancel: options.onCancel
      });
    },
    
    /**
     * Handle confirmation modal confirm
     */
    handleConfirmationConfirm() {
      if (this.confirmationModal.onConfirm) {
        this.confirmationModal.onConfirm();
      }
    },
    
    /**
     * Handle confirmation modal cancel
     */
    handleConfirmationCancel() {
      if (this.confirmationModal.onCancel) {
        this.confirmationModal.onCancel();
      }
    },
    
    /**
     * Close confirmation modal
     */
    closeConfirmationModal() {
      this.confirmationModal.isShow = false;
      // Reset after animation
      setTimeout(() => {
        this.confirmationModal = {
          isShow: false,
          title: '',
          message: '',
          type: 'warning',
          confirmText: '',
          cancelText: '',
          confirmButtonColor: 'red-solid',
          isLoading: false,
          onConfirm: null,
          onCancel: null
        };
      }, 300);
    }
  }
};
