// src/mixins/useRegisterSW.js
const isBrowser = typeof window !== 'undefined';
const isDev = false; // Set to true if you want to enable development mode features

export default {
    name: 'useRegisterSW',

    data() {
        return {
            __offlineReady: false,
            __needRefresh: false,
            __registration: null,
            __refreshing: false,
            __updateInterval: null,
        };
    },

    async mounted() {
        if (!isBrowser) return;
        if (isDev && !this.__testMode) {
            console.log('PWA: dev test mode disabled');
            return;
        }

        try {
            const {
                registerSW
            } = await import('virtual:pwa-register');
            const vm = this;

            registerSW({
                immediate: true,

                onRegistered(registration) {
                    vm.__registration = registration;
                    if (isDev) {
                        // dev-only simulation
                        setTimeout(() => {
                            vm.__needRefresh = true;
                            document.dispatchEvent(new Event('swUpdated'));
                        }, 5_000);
                    } else {
                        // poll every hour in production - but only check, don't auto-update
                        vm.__updateInterval = setInterval(() => {
                            registration.update();
                        }, 60 * 60 * 1_000);
                    }
                },

                onNeedRefresh() {
                    vm.__needRefresh = true;
                    document.dispatchEvent(new Event('swUpdated'));
                },

                onOfflineReady() {
                    vm.__offlineReady = true;
                },

                onRegisterError(err) {
                    console.error('SW registration error:', err);
                    navigator.serviceWorker.getRegistrations()
                        .then(regs => Promise.all(regs.map(r => r.unregister())))
                        .then(() => setTimeout(() => window.location.reload(), 1_000));
                },
            });
        } catch (e) {
            console.warn('PWA disabled:', e);
        }
    },

    methods: {
        __closePromptUpdateSW() {
            this.__needRefresh = false;
        },

        __updateServiceWorker() {
            const waiting = this.__registration?.waiting;
            if (!waiting) return;

            // prevent double clicks
            if (this.__refreshing) return;
            this.__refreshing = true;

            waiting.postMessage({
                type: 'SKIP_WAITING'
            });

            navigator.serviceWorker.addEventListener('controllerchange', () => {
                window.location.reload();
            }, {
                once: true
            });
        },
    },

    beforeUnmount() {
        this.__updateInterval && clearInterval(this.__updateInterval);
    },
};