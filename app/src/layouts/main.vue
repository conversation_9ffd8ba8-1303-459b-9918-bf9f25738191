<template>
	<!-- <div v-show="!isChrome"
		class="top-0 px-6 w-full py-2 [h-30px] lg:flex lg:items-center bg-red-600 text-white text-sm justify-center">
		{{ $t('Bannerbite performs better with Chrome based browser') }},
		<a href="https://www.google.co.id/chrome/?brand=CHBD&gclid=CjwKCAjwiOv7BRBREiwAXHbv3AWBmeej6mPeNXp8FwCu2rVTeVyKHmOTY8PZwdTt57cJPs2tReOVzBoCC8YQAvD_BwE&gclsrc=aw.ds"
			target="_blank" class="underline pointer text-white pl-1">{{ $t('Download here') }}</a>
	</div> -->
	<div>
		<!-- dont make sense teuing aku ge gatau -->
		<!-- <div class="hidden">
			<nav class="-mb-0.5 flex gap-x-6">
				<a ref="triggerHS"
					class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
					href="#">
					Overview
				</a>
			</nav>
		</div> -->
	</div>
	<div id="main-view" class="h-screen overflow-hidden bg-gray-100">
		<Sidebar
			v-if="!hideSidebar"
			ref="sidebar"
			@show-project-modal="showProjectModal"
			@show-delete-modal="showDeleteModal"
		/>
		<Header v-if="!hideSidebar" />
		<main id="content" :class="hideSidebar ? 'pt-0' : 'lg:ps-[260px] pt-[50px]'">
			<div :class="hideSidebar ? '' : 'main-padding sm:py-0 space-y-5'">
				<!-- Card -->
				<div
					:class="hideSidebar ? 'w-full h-screen' : 'min-w-full flex flex-col justify-center h-72 md:h-96 min-h-[calc(100vh-50px)] bg-white shadow-sm'">
					<div class="relative h-full overflow-hidden overflow-y-auto" :class="{ 'p-4': activeRouteName !== 'AnswerFlow' && !hideSidebar, 'rounded-xl': !hideSidebar }">
						<router-view v-slot="{ Component }">
							<!-- <transition name="slide"> -->
							<component :is="Component" />
							<!-- </transition> -->
						</router-view>
					</div>
				</div>
				<!-- End Card -->
			</div>
		</main>
		<!-- <Footer /> -->

		<!-- Project Modals -->
		<ProjectModals
			:showModal="projectModal.show"
			:showDeleteModal="deleteModal.show"
			:modalMode="projectModal.mode"
			:form="projectModal.form"
			:projectToDelete="deleteModal.project"
			:isSubmitting="projectModal.isSubmitting"
			:isDeleting="deleteModal.isDeleting"
			:availableColors="availableColors"
			@close-modal="closeProjectModal"
			@close-delete-modal="closeDeleteModal"
			@submit-form="submitProjectForm"
			@confirm-delete="confirmDeleteProject"
			@save-user-access="saveUserAccess"
		/>
	</div>
</template>

<script>
import {
		mapActions
	} from "vuex";
	import Sidebar from '@/layouts/partials/Sidebar.vue';
	import Header from '@/layouts/partials/Header.vue';
	import Footer from '@/layouts/partials/Footer.vue';
	import ProjectModals from '@/components/project/ProjectModals.vue';
	import projectApi from '@/api/project.js';
	import userApi from '@/api/user.js';

	export default {
		components: {
			Sidebar,
			Header,
			Footer,
			ProjectModals,
		},
		setup() {},
		data() {
			return {
				isChrome: false,

				// Project modal state
				projectModal: {
					show: false,
					mode: 'add', // 'add' or 'edit'
					isSubmitting: false,
					form: {
						name: '',
						description: '',
						color: '#F97316',
					},
					currentProject: null,
				},

				// Delete modal state
				deleteModal: {
					show: false,
					isDeleting: false,
					project: null,
				},

				// Available colors for projects
				availableColors: [
					'#F97316', '#EF4444', '#10B981', '#3B82F6', '#8B5CF6',
					'#F59E0B', '#EF4444', '#06B6D4', '#84CC16', '#EC4899'
				],
			};
		},
		mounted() {
			// Add the click event listener to the document
			// document.addEventListener('click', this.clickThis);
      this.fetchUser()
		},
		beforeDestroy() {
			// Remove the click event listener to prevent memory leaks
			// document.removeEventListener('click', this.clickThis);
		},
		computed: {
			activeRouteId() {
				return this.$route.path !== "/embed";
			},
			activeRouteName() {
				return this.$route.name;
			},
			hideSidebar() {
				// Hide sidebar for AssistantNotebook route
				return this.$route.name === 'AssistantNotebook' || this.$route.name === 'AssistantProject';
        
			},
		},
		watch: {
			$route(to, from) {
				this.scrollDown();
			},
		},
		created() {
			this.browserName();
		},
		methods: {
      ...mapActions({
				fetchUser: 'auth/fetchUser',
			}),

			// Project modal methods
			showProjectModal(data) {
				this.projectModal.mode = data.mode;
				this.projectModal.currentProject = data.project;

				// Helper function to ensure data is in array format for multiselect
				const ensureArray = (data) => {
					if (!data) return [];
					if (Array.isArray(data)) return data;
					// Handle single object or primitive value
					if (typeof data === 'object' || typeof data === 'string' || typeof data === 'number') {
						return [data];
					}
					return [];
				};

				if (data.mode === 'add') {
					this.resetProjectForm();
				} else {
					// For edit mode, always fetch the full project details to get all related data
					this.fetchProjectDetails(data.project.id);
				}

				this.projectModal.show = true;
			},

			// Fetch full project details for editing
			fetchProjectDetails(projectId) {
				// Helper function to ensure data is in array format for multiselect
				const ensureArray = (data) => {
					if (!data) {
						return [];
					}
					if (Array.isArray(data)) {
						return data;
					}
					// Handle single object or primitive value
					if (typeof data === 'object' || typeof data === 'string' || typeof data === 'number') {
						return [data];
					}
					return [];
				};

				const callback = (response) => {
					const project = response.data;

					this.projectModal.form = {
						id: project.id,
						name: project.name,
						description: project.description || '',
						brief: project.brief || '',
						status: project.status || '',
						is_archived: project.is_archived || false,
						role_ids: ensureArray(project.roles),
						group_ids: ensureArray(project.groups),
						// flow_ids: ensureArray(project.flows),
						answer_flow_ids: ensureArray(project.answer_flow_ids),
						category_ids: ensureArray(project.categories),
						user_access_ids: [] // Will be populated by fetchProjectUsers
					};



					// Fetch project users separately using the project users API
					this.fetchProjectUsers(project.id);
				};

				const errorCallback = (error) => {
					console.error('Error fetching project details:', error);
					// Fallback to basic form
					this.resetProjectForm();
					this.projectModal.form.id = projectId;
				};

				projectApi.get(projectId, callback, errorCallback);
			},

			// Fetch project users for edit mode
			fetchProjectUsers(projectId) {
				const callback = (response) => {
					const users = response.data;

					// Update the form's user_access_ids with the fetched users
					this.projectModal.form.user_access_ids = Array.isArray(users) ? users : [];
				};

				const errorCallback = (error) => {
					console.error('Error fetching project users:', error);
					// Keep user_access_ids as empty array if fetch fails
					this.projectModal.form.user_access_ids = [];
				};
        const params = {
          project_id: projectId,
        };
				// Use the project users API to get users for this project
				userApi.getList(params, callback, errorCallback);
			},



			closeProjectModal() {
				this.projectModal.show = false;
				this.resetProjectForm();
				this.projectModal.currentProject = null;
			},

			showDeleteModal(project) {
				this.deleteModal.project = project;
				this.deleteModal.show = true;
			},

			closeDeleteModal() {
				this.deleteModal.show = false;
				this.deleteModal.project = null;
			},

			resetProjectForm() {
				this.projectModal.form = {
					id: null,
					name: '',
					description: '',
					brief: '',
					status: '',
					is_archived: false,
					role_ids: [],
					group_ids: [],
					flow_ids: [],
          answer_flow_ids: [],
					category_ids: [],
					user_access_ids: []
				};
			},

			async submitProjectForm() {
				if (!this.validateProjectForm()) return;

				this.projectModal.isSubmitting = true;

				// Helper function to convert array to JSON string format
				const arrayToString = (arr) => {
					if (!Array.isArray(arr)) {
						return '[]';
					}

					// Handle empty array
					if (arr.length === 0) {
						return '[]';
					}

					const ids = [];

					for (let item of arr) {
						if (typeof item === 'object' && item !== null && item.id !== undefined && item.id !== null) {
							// Handle objects with id property
							ids.push(String(item.id));
						} else if (typeof item === 'string') {
							// Check if it's already a JSON string
							try {
								const parsed = JSON.parse(item);
								if (Array.isArray(parsed)) {
									// If it's a JSON array, extract the IDs
									ids.push(...parsed.map(id => String(id)));
								} else {
									// If it's a single value, use it
									ids.push(String(parsed));
								}
							} catch {
								// If it's not JSON, treat as regular string ID
								if (item.trim() !== '') {
									ids.push(item);
								}
							}
						} else if (item !== undefined && item !== null) {
							// Handle direct values (numeric IDs, etc.)
							ids.push(String(item));
						}
					}

					// Remove duplicates and empty strings
					const uniqueIds = [...new Set(ids)].filter(id => id !== null && id !== undefined && id.trim() !== '');

					return JSON.stringify(uniqueIds);
				};



				const params = {
					name: this.projectModal.form.name,
					description: this.projectModal.form.description,
					brief: this.projectModal.form.brief,
					status: this.projectModal.form.status,
					is_archived: this.projectModal.form.is_archived,
					role_ids: arrayToString(this.projectModal.form.role_ids),
					group_ids: arrayToString(this.projectModal.form.group_ids),
					// flow_ids: arrayToString(this.projectModal.form.flow_ids), need to enabled when all answerflow is correct
          answer_flow_ids: arrayToString(this.projectModal.form.answer_flow_ids),
					category_ids: arrayToString(this.projectModal.form.category_ids),
					user_access_ids: arrayToString(this.projectModal.form.user_access_ids)
				};



				// Optimistic update
				let tempId = null;
				const projectSidebar = this.$refs.sidebar.$refs.projectSidebar;

				if (this.projectModal.mode === 'add') {
					// Add optimistically
					tempId = projectSidebar.addProjectOptimistically({
						name: this.projectModal.form.name,
						description: this.projectModal.form.description,
						color: this.projectModal.form.color
					});
				} else {
					// Update optimistically
					projectSidebar.updateProjectOptimistically(this.projectModal.currentProject.id, {
						name: this.projectModal.form.name,
						description: this.projectModal.form.description,
						color: this.projectModal.form.color
					});
				}

				const callback = (response) => {
					console.log(`Project ${this.projectModal.mode === 'add' ? 'created' : 'updated'} successfully`);

					// Confirm optimistic update
					if (this.projectModal.mode === 'add') {
						projectSidebar.confirmOptimisticAdd(tempId, response.data);
					} else {
						projectSidebar.confirmOptimisticUpdate(this.projectModal.currentProject.id, response.data);
					}

					this.closeProjectModal();
					this.projectModal.isSubmitting = false;
				};

				const errorCallback = (error) => {
					console.error('Error saving project:', error);

					// Revert optimistic update
					if (this.projectModal.mode === 'add') {
						projectSidebar.revertOptimisticAdd(tempId);
					} else {
						projectSidebar.revertOptimisticUpdate(this.projectModal.currentProject.id);
					}

					this.projectModal.isSubmitting = false;
					const message = error?.response?.data?.message || `Failed to ${this.projectModal.mode === 'add' ? 'create' : 'update'} project. Please try again.`;
					this.__showNotif('error', 'Error', message);
				};

				try {
					if (this.projectModal.mode === 'add') {
						projectApi.create(params, callback, errorCallback);
					} else {
						projectApi.update(this.projectModal.currentProject.id, params, callback, errorCallback);
					}
				} catch (error) {
					errorCallback(error);
				}
			},

			async confirmDeleteProject() {
				if (!this.deleteModal.project) return;

				this.deleteModal.isDeleting = true;
				const projectSidebar = this.$refs.sidebar.$refs.projectSidebar;

				// Optimistically mark project as being deleted
				projectSidebar.deleteProjectOptimistically(this.deleteModal.project.id);

				const callback = () => {
					console.log('Project deleted successfully');

					// Confirm optimistic delete (remove from list)
					projectSidebar.confirmOptimisticDelete(this.deleteModal.project.id);

					this.closeDeleteModal();
					this.deleteModal.isDeleting = false;
				};

				const errorCallback = (error) => {
					console.error('Error deleting project:', error);

					// Revert optimistic delete
					projectSidebar.revertOptimisticDelete(this.deleteModal.project.id);

					this.deleteModal.isDeleting = false;
					const message = error?.response?.data?.message || 'Failed to delete project. Please try again.';
					this.__showNotif('error', 'Error', message);
				};

				try {
					projectApi.delete(this.deleteModal.project.id, callback, errorCallback);
				} catch (error) {
					errorCallback(error);
				}
			},

			saveUserAccess(data) {
				// Update the form with the new user access data
				this.projectModal.form.user_access_ids = data.userAccessIds;
				console.log('User access updated:', data);
			},

			validateProjectForm() {
				if (!this.projectModal.form.name.trim()) {
					this.__showNotif('error', 'Error', 'Project name is required');
					return false;
				}
				if (this.projectModal.form.name.trim().length < 2) {
					this.__showNotif('error', 'Error', 'Project name must be at least 2 characters long');
					return false;
				}
				return true;
			},
			// clickThis() {
			// 	this.$refs.triggerHS ? .click();
			// 	// Your logic here
			// },
			scrollDown() {
				const objDiv = this.$refs.mainView;
				// Scroll top div
				// objDiv.scrollTop = 0;

				// Scroll bottom of div
				// objDiv.scrollTop = objDiv.scrollHeight;
			},
			browserName(agent) {
				let userAgent = navigator.userAgent;
				let browserName;
				if (userAgent.match(/chrome|chromium|crios/i)) {
					browserName = "chrome";
				} else if (userAgent.match(/firefox|fxios/i)) {
					browserName = "firefox";
				} else if (userAgent.match(/safari/i)) {
					browserName = "safari";
				} else if (userAgent.match(/opr\//i)) {
					browserName = "opera";
				} else if (userAgent.match(/edg/i)) {
					browserName = "edge";
				} else {
					browserName = "No browser detection";
				}
				if (browserName != 'chrome') {
					this.isChrome = false;
				} else {
					this.isChrome = true;
				}
			},
		},
	};
</script>
<style>
	@media (min-width: 1024px) {
		.main-padding {
		}

		.main-width {
			width: calc(100vw - 332px);
		}
	}

	@media (max-width: 1024px) {
		.main-width {
			width: calc(100vw - 34px);
		}
	}
</style>