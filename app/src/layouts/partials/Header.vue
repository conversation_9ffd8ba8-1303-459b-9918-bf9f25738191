<template>
	<!-- ========== HEADER ========== -->
	<header
		class="lg:ms-[260px] fixed top-0 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 bg-white">
		<div class="flex justify-between xl:grid xl:grid-cols-3 basis-full items-center w-full py-2.5 px-2 sm:px-5">
			<div class="xl:col-span-1 flex items-center md:gap-x-3">
				<div class="lg:hidden">
					<!-- Sidebar Toggle -->
					<button type="button"
						class="w-7 h-[38px] inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
						aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-sidebar"
						aria-label="Toggle navigation" data-hs-overlay="#hs-pro-sidebar">
						<svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
							viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
							stroke-linecap="round" stroke-linejoin="round">
							<path d="M17 8L21 12L17 16M3 12H13M3 6H13M3 18H13" /></svg>
					</button>
					<!-- End Sidebar Toggle -->
				</div>

				<!-- navbar -->
				<div class="absolute lg:mt-1 ml-16 lg:ml-0 lg:block" >
					<!-- admin -->
					<nav class="-mb-0.5 flex gap-x-6" v-if="isAdmin">
						<router-link
							ref="overviewNav"
							v-if="$route.path.includes('/admin/role') ||  $route.path.includes('/admin/job')"
							:class="{'border-primary-600 border-b-2': $route.path.includes('/admin/role')}"
							class="py-2 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							to="/admin/role">
							Roles
						</router-link>
						<router-link
							v-if="$route.path.includes('/admin/role') ||  $route.path.includes('/admin/job')"
							:class="{'border-primary-600 border-b-2': $route.path.includes('/admin/job')}"
							class="py-2 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap  hover:text-primary-600"
							to="/admin/job" aria-current="page">
							Job
						</router-link>
						<router-link
							v-if="$route.path.includes('/admin/service') || $route.path.includes('/admin/product')"
							:class="{'border-primary-600 border-b-2': $route.path.includes('/admin/service')}"
							class="py-2 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							to="/admin/service">
							Service
						</router-link>
						<router-link
							v-if="$route.path.includes('/admin/service') || $route.path.includes('/admin/product')"
							:class="{'border-primary-600 border-b-2': $route.path.includes('/admin/product')}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2  hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							to="/admin/product">
							Product
						</router-link>
					</nav>

					<!-- settings -->
					<nav class="-mb-0.5 flex gap-x-6" v-if="$route.path.includes('/settings')">
						<router-link
							ref="overviewNav"
							:class="{'border-primary-600 border-b-2': $route.path.includes('/settings/profile')}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							to="/settings/profile">
							Profile
						</router-link>
						<router-link 
							:class="{'border-primary-600 border-b-2': $route.path.includes('/settings/change-password')}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2  hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							to="/settings/change-password">
							Security
						</router-link>
					</nav>

					<!-- not admin -->
					<!-- <nav class="-mb-0.5 flex gap-x-6 ml-8" v-if="getActiveProject?.name && $route.name !== 'MyTask' && $route.name !== 'InquiryDetailFull' && $route.name !== 'InquiryDetail'">
						<router-link
							:class="{'border-primary-600 border-b-2': $route.path.includes(`/e/overview/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`)}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							:to="`/e/overview/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`">
							Overview
						</router-link>
						<router-link
							:class="{'border-primary-600 border-b-2': $route.path.includes(`/e/kanban/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`)}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap  hover:text-primary-600"
							:to="`/e/kanban/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`">
							Board
						</router-link>
						<router-link
							:class="{'border-primary-600 border-b-2': $route.path.includes(`/e/files/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`)}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							:to="`/e/files/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`">
							Files
						</router-link>
						<router-link
							:class="{'border-primary-600 border-b-2': $route.path.includes(`/e/report/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`)}"
							class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2  hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
							:to="`/e/report/${__encryptProjectData(getActiveProject.id, getActiveProject.slug)}`">
							Analytic
						</router-link>
					</nav> -->
				</div>
			</div>

			<div class="xl:col-span-2 flex justify-end items-center gap-x-2">
				<div class="flex items-center">
					<!-- search mobile -->
					<div class="lg:hidden">
						<!-- Search Button Icon -->
						<!-- <button type="button"
							class="inline-flex shrink-0 justify-center items-center gap-x-2 size-[38px] rounded-full text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
							data-hs-overlay="#hs-pro-dnsm">
							<svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
								viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
								stroke-linecap="round" stroke-linejoin="round">
								<circle cx="11" cy="11" r="8" />
								<path d="m21 21-4.3-4.3" /></svg>
						</button> -->
						<!-- End Search Button Icon -->
					</div>

				</div>

				<!-- Get Answer Button -->
				<t-button
					v-if="user"
					@click="showGetAnswerModal = true"
          :color="'primary-solid'"
				>
					<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
						<path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
					</svg>
					Get Answer
				</t-button>

				<!-- account -->
				<div class="h-[38px] ">
					<!-- Account Dropdown -->
					<div
						class="hs-dropdown inline-flex [--strategy:absolute] [--placement:bottom-right] relative text-start">
						<button id="hs-dnad" type="button"
							class="inline-flex shrink-0 items-center gap-x-3 text-start rounded-full focus:outline-none"
							aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
							<img class="shrink-0 size-[38px] rounded-full object-cover" :src="getUserImage(user)" alt="Avatar" />
						</button>

						<!-- Account Dropdown -->
						<div
							class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
							role="menu" aria-orientation="vertical" aria-labelledby="hs-dnad">
							<div class="p-1 border-b border-gray-200">
								<router-link
									class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
									:to="'/settings/profile'">
									<img class="shrink-0 size-8 rounded-full object-cover" :src="getUserImage(user)  " alt="Avatar">

									<div class="grow">
										<span class="text-sm font-semibold text-gray-800">
											{{ user?.first_name || '-'}}
											{{ user?.last_name}}
										</span>
										<p class="text-xs text-gray-500">
											{{ user?.email || '-'}}
										</p>
									</div>
								</router-link>
							</div>
							<div class="px-1 my-1" @click="resetDataProject()">
								<router-link 
									class="flex items-center gap-x-3 py-1 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
									:to="'/settings/profile'">
									Profile
								</router-link>
							</div>
							<div class="px-1 my-1" v-if="!isClient" @click="resetDataProject()">
								<router-link
									class="flex items-center gap-x-3 py-1 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
									to="/settings/change-password">
									Settings
								</router-link>
							</div>
							<div class="p-1 border-t border-gray-200" @click="resetDataProject()">
								<router-link
									class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100"
									to="/logout">
									Sign out
								</router-link>
							</div>
						</div>
						<!-- End Account Dropdown -->
					</div>
					<!-- End Account Dropdown -->
				</div>
			</div>
		</div>
	</header>

	<!-- Get Answer Modal -->
	<Modal
		:isShow="showGetAnswerModal"
		@onClose="closeGetAnswerModal"
		customClass="sm:max-w-md"
	>
		<!-- Modal Container with Fixed Header/Footer -->
		<div class="flex flex-col h-full max-h-[90vh]">
			<!-- Fixed Header -->
			<div class="flex-shrink-0 border-b border-gray-200 pb-4">
				<div class="flex items-center justify-between">
					<div class="text-lg sm:text-xl font-medium text-gray-900">
						Select Project
					</div>
					<button @click="closeGetAnswerModal" class="text-gray-400 hover:text-gray-600">
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
						</svg>
					</button>
				</div>
				<p class="text-sm text-gray-500 mt-2">
					Select project from the list to get answer from
				</p>
			</div>

			<!-- Scrollable Content -->
			<div class="flex-1 overflow-y-auto py-4">
				<!-- Loading State -->
        <loader-circle v-if="isLoadingProjects" />


				<!-- No Projects -->
				<div v-else-if="activeProjects.length === 0" class="text-center py-8">
					<p class="text-gray-500">No active projects found</p>
				</div>

				<!-- Projects List -->
				<div v-else class="space-y-2">
					<button
						v-for="project in activeProjects"
						:key="project.id"
						@click="selectProject(project)"
						class="w-full text-left px-4 py-2 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
					>
						<div class="flex items-center space-x-3">
							<!-- Project Color Indicator -->
							<div
								class="w-4 h-4 rounded-full flex-shrink-0"
								:style="{ backgroundColor: project.color || '#6B7280' }"
							></div>

							<!-- Project Info -->
							<div class="flex-1 min-w-0">
								<div class="text-sm font-medium text-gray-900 truncate">
									{{ project.name }}
								</div>
							</div>
						</div>
					</button>
				</div>
			</div>
		</div>
	</Modal>
</template>


<script>
	import {
		mapGetters, mapActions
	} from 'vuex';
  import {
      HSStaticMethods
  } from "preline";
	import Modal from '@/components/global/Modal.vue';
	import projectApi from '@/api/project.js';

	export default {
		components: {
			Modal,
		},
		data() {
			return {
				showGetAnswerModal: false,
				activeProjects: [],
				isLoadingProjects: false,
			};
		},
		computed: {
			...mapGetters({
				getActiveProject: 'application/getActiveProject',
				user: 'auth/user',
				isAdmin: 'auth/isAdmin',
				isClient: 'auth/isClient',
				isFreelancer: 'auth/isFreelancer',
				getToken: 'auth/getToken',
			}),
		},
		created() {
			// console.log(this.user)
			// this.$refs.overviewNav?.click();
      setTimeout(() => {
          HSStaticMethods.autoInit();
      }, 500);
		},
		methods: {
      ...mapActions({
				resetDataProject: 'application/resetDataProject',
			}),
			getUserImage(user) {
				if (user?.img_url) {
					return user.img_url;
				} else {
					const defaultName = user && (user.first_name || user.last_name)
						? `${user.first_name || ''} ${user.last_name || ''}`.trim()
						: "desidia";
					return this.__generateInitialCanvas(defaultName);
				}
			},

			// Get Answer Modal Methods
			async fetchActiveProjects() {
				this.isLoadingProjects = true;

				const params = {
					order_by: 'name',
					sort_by: 'asc',
					page: 1,
					limit: 100,
					is_archived: 0 // Only get active projects
				};

				const callback = (response) => {
					this.activeProjects = response.data || [];
					this.assignRandomColors();
					this.isLoadingProjects = false;
				};

				const errorCallback = (error) => {
					console.error('Fetch active projects error:', error);
					this.activeProjects = [];
					this.isLoadingProjects = false;
					this.__showNotif('error', 'Error', 'Failed to load projects');
				};

				projectApi.getList(params, callback, errorCallback);
			},

			assignRandomColors() {
				const availableColors = [
					'#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
					'#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
					'#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
					'#EC4899', '#F43F5E'
				];

				this.activeProjects.forEach((project, index) => {
					if (!project.color) {
						project.color = availableColors[index % availableColors.length];
					}
				});
			},

			closeGetAnswerModal() {
				this.showGetAnswerModal = false;
				this.activeProjects = [];
			},

			selectProject(project) {
				// Navigate to assistant with selected project
				// this.$router.push({
				// 	name: 'AnswerFlow',
				// 	query: { projectId: project.id }
				// });
				this.closeGetAnswerModal();
			},
		},

		watch: {
			showGetAnswerModal(newVal) {
				if (newVal) {
					this.fetchActiveProjects();
				}
			}
		}
	};
</script>