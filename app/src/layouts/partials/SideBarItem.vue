<template>
	<li class="px-5">
		<div :class="{'bg-gray-100': isActive(item)}"
			@click="goTo(item.path)" class="flex items-center gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-none focus:bg-gray-100 cursor-pointer"
			>
			<slot v-if="$slots.default"></slot>
			<!-- Optional fallback content -->
			<div :class="{'text-primary-600': isActive(item)}" class="text-md font-medium">
				{{ $t(item.label) }}
			</div>
			<span v-if="item.children && item.children.length" @click="toggle">
				<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
					stroke="currentColor">
					<path :d="isOpen ? 'M19 9l-7 7-7-7' : 'M9 5l7 7-7 7'" />
				</svg>
			</span>
		</div>

		<!-- Nested Items -->
		<ul v-if="item.children && isOpen" class="pl-[20px] space-y-4 mt-2">
			<div v-for="(child, index) in item.children" :key="index" :class="{'text-primary-600': isActive(child)}" class="text-sm font-normal ml-6 cursor-pointer">
				<a @click="goTo(child.path)"> {{ $t(child.label) }} </a>
			</div>
		</ul>
	</li>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';


	export default {
		props: {
			item: {
				type: Object,
				required: true
			}
		},
		data() {
			return {
				isOpen: false
			};
		},
		computed: {
			...mapGetters({
				user: 'auth/user'
			})
		},
		created() {
			if (this.$route.path.includes('/admin')) {
				this.isOpen = true;
			}
		},
		methods: {
			...mapActions({
				resetDataProject: 'application/resetDataProject',
			}),
			goTo(path) {
				if (path === '/admin') this.isOpen = !this.isOpen;
				else this.$router.push(path);
				this.resetDataProject()
			},
			toggle() {
				this.isOpen = !this.isOpen;
				this.resetDataProject()
			},
			isActive(item) {
				// Check if the current route matches the parent item
				if (this.$route.path === (item.path) || this.$route.path === (item.alternativePath)) {
					return true;
				}

				// Check if the current route matches any child item
				if (item.children && item.children.length) {
					return item.children.some(child => this.isActive(child));
				}

				return false;
			}

		},
		components: {
		}
	};
</script>

<style scoped>
	/* Add any additional custom styles if necessary */
</style>