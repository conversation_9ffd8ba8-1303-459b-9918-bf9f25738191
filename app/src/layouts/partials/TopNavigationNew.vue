<template>
	<header v-if="!fullscreenPage">
		<Popover class="relative bg-white">
			<!-- Desktop -->
			<div
				class="flex justify-between items-center w-full py-4 sm:px-6 md:justify-start md:space-x-10 lg:px-8"
			>
				<!-- Left -->
				<div
					v-if="notAllowedPath"
					class="flex justify-start"
				>
					<!-- Desktop -->
					<router-link
						to="/"
						class="hidden md:block"
					>
						<span class="sr-only">Desidia</span>
						<img
							class="h-8 w-auto sm:h-10"
							src="@/assets/images/svg/donuts.svg"
							alt=""
						>
					</router-link>

					<!-- Mobile -->
					<button
						type="button"
						class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden"
						@click="__showLeftNavigation"
					>
						<span class="sr-only">Open sidebar</span>
						<MenuAlt2Icon
							class="h-6 w-6"
							aria-hidden="true"
						/>
					</button>
				</div>

				<div class="text-left absolute left-0 ml-5 lg:block md:block">
					<div
						v-if="activeRouteName === 'TemplateEditor'"
						class="text-blue pointer"
						@click="backToTemplate()"
					>
						{{ $t('Return to Templates') }}
					</div>
				</div>
				
				<div
					v-if="!checkEnterpriseAndManualUser"
					class="flex items-center absolute right-0"
				>
					<!-- upgarde -->
					<div
						v-if="activeRouteName !== 'ShareableForm' && isPackageMax !== 'Unlimited'"
						class="text-blue pointer mr-20"
						@click="upgradeAccount()"
					>
						{{ $t('Upgrade Account') }}
					</div>
				</div>
				

				<!-- left with juatify between -->
				<PopoverGroup
					v-if="notAllowedPath"
					as="nav"
					class="bg-transparent text-left hidden md:flex space-x-10"
				>
					<template
						v-for="(mainmenu, index) in mainMenuItems"
						:key="index"
					>
						<!-- Single -->
						<router-link
							v-if="mainmenu.childrens.length === 0 && (mainmenu.access.includes(role) || mainmenu.access === 'all')"
							:key="mainmenu.href"
							:to="mainmenu.href"
							class="text-base font-medium hover:text-primary-400"
							:class="[isActive(mainmenu.href) ? 'font-bold text-primary-500' : '']"
						>	
							<div
								v-if="!isAdmin"
								@mouseover="tooltipPosition(true)"
								@mouseleave="tooltipPosition(false)"
							>
								<span v-if="mainmenu.name === 'Galleries' && (getIsShowGallery || (getIsShowGallery && gallery.length > 0)) ">{{ $t(mainmenu.name) }}</span>
								<span v-if="mainmenu.name !== 'Galleries' && mainmenu.name !== 'Projects' && mainmenu.name !== 'Templates'">{{ $t(mainmenu.name) }}</span>
								<span
									v-if="mainmenu.name === 'Projects'"
								>
									<a
										class="master-tooltip"
										title="See all of your projects"
									>{{ $t(mainmenu.name) }}</a>
								</span>
								<span
									v-if="mainmenu.name === 'Templates'"
								>
									<a
										class="master-tooltip"
										title="Browse video template"
									>{{ $t(mainmenu.name) }}</a>
								</span>
							</div>
							<div
								v-if="isAdmin"
								@mouseover="tooltipPosition(true)"
								@mouseleave="tooltipPosition(false)"
							>
								<span v-if="mainmenu.name !== 'Projects' && mainmenu.name !== 'Templates'">{{ $t(mainmenu.name) }}</span>
								<span v-if="mainmenu.name === 'Projects'">
									<a
										class="master-tooltip"
										title="See all of your projects"
									>{{ $t(mainmenu.name) }}</a>
								</span>
								<span v-if="mainmenu.name === 'Templates'">
									<a
										class="master-tooltip"
										title="Browse video template"
									>{{ $t(mainmenu.name) }}</a>
								</span>
							</div>
						</router-link>

						<!-- Has childrens -->
						<Popover
							v-if="mainmenu.childrens.length > 0 && (mainmenu.access.includes(role) || mainmenu.access === 'all')"
							v-slot="{ open }"
							class="relative"
						>
							<PopoverButton
								:class="[open ? 'text-[#374253]-900' : 'text-[#374253]-500', 'group bg-transparent rounded-md inline-flex items-center text-base font-medium hover:text-[#374253]-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500']"
							>
								<span>{{ $t(mainmenu.name) }}</span>
								<ChevronDownIcon 
									v-if="!open"
									:class="[open ? 'text-[#374253]-600' : 'text-[#374253]-400', 'ml-2 h-5 w-5 group-hover:text-[#374253]-500']"
									aria-hidden="true"
								/>
								<ChevronUpIcon
									v-else
									:class="[open ? 'text-[#374253]-600' : 'text-[#374253]-400', 'ml-2 h-5 w-5 group-hover:text-[#374253]-500']"
									aria-hidden="true"
								/>
							</PopoverButton>

							<transition
								enterActiveClass="transition ease-out duration-200"
								enterFromClass="opacity-0 translate-y-1"
								enterToClass="opacity-100 translate-y-0"
								leaveActiveClass="transition ease-in duration-150"
								leaveFromClass="opacity-100 translate-y-0"
								leaveToClass="opacity-0 translate-y-1"
							>
								<PopoverPanel
									v-slot="{ close }"
									class="absolute z-10 -ml-4 mt-3 transform w-screen max-w-md lg:max-w-2xl lg:ml-0 lg:left-1/2 lg:-translate-x-1/2"
								>
									<div class="rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden">
										<div class="relative grid gap-6 bg-white px-5 py-6 sm:gap-8 sm:p-8 lg:grid-cols-2">
											<router-link
												v-for="item in mainmenu.childrens"
												:key="item.name"
												:to="item.href"
												class="-m-3 p-3 flex items-start rounded-lg hover:bg-gray-50"
												:class="[isActive(item.href) ? 'bg-gray-100' : '']"
												@click="closeMenu(close)"
											>
												<div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md bg-gradient-to-r from-primary-400 to-primary-600 text-white sm:h-12 sm:w-12">
													<component
														:is="item.icon"
														class="h-6 w-6"
														aria-hidden="true"
													/>
												</div>
												<div class="ml-4">
													<p class="text-base font-medium text-gray-900">
														{{ $t(item.name) }}
													</p>
													<p class="mt-1 text-sm text-gray-500">
														{{ $t(item.description) }}
													</p>
												</div>
											</router-link>
										</div>
									</div>
								</PopoverPanel>
							</transition>
						</Popover>
					</template>
				</PopoverGroup>
				
				<!-- Right -->
				<div class="flex mr-3 items-center justify-end flex-1 lg:w-0">
					<!-- Profile dropdown -->
					<Menu
						as="div"
						class="ml-3 relative z-10"
					>
						<div>
							<MenuButton
								class="bg-transparent max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
							>
								<span class="sr-only">Open user menu</span>
								<img
									v-if="user && user.avatar !== '' && user && user.avatar !== null"
									class="h-8 w-8 rounded-full object-cover"
									:src="user.avatar"
									alt="avatar-image"
									referrerpolicy="no-referrer"
									@error="handleAvatarError()"
								>
								<div v-if="user && user.avatar === null || user && user.avatar === ''">
									<div class="h-8 w-8 rounded-full font-bold pt-[5.5px] text-center bg-primary-600 text-white uppercase">
										{{ __generateInitial(name) }} DD
									</div>
								</div>
							</MenuButton>
						</div>
						<transition
							enterActiveClass="transition ease-out duration-100"
							enterFromClass="transform opacity-0 scale-95"
							enterToClass="transform opacity-100 scale-100"
							leaveActiveClass="transition ease-in duration-75"
							leaveFromClass="transform opacity-100 scale-100"
							leaveToClass="transform opacity-0 scale-95"
						>
							<MenuItems
								class="origin-top-right absolute right-0 mt-2 w-60 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
							>
								<MenuItem
									v-for="item in userMenus"
									v-slot="{ active }"
									:key="item.name"
									@click="checkExternalLink(item)"
								>
									<label
										@mouseover="checkHover(item.name, 'hover')"
										@mouseleave="checkHover(item.name, 'leave')"
									>
										<span
											v-if="!isFetching && !audioUrl && item.name !== 'Onboarding'"
											:class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
										>{{ $t(item.name) }}</span>
										<span
											v-if="!isFetching && !audioUrl && item.name === 'Onboarding'"
											:class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
										>{{ isShowOnboard === '1' ? $t('Hide Onboarding Window') : $t('Show Onboarding Window') }}</span>
									</label>
								</MenuItem>
							</MenuItems>
						</transition>
					</Menu>
					<div
						v-show="isShowMenuLocalization"
						class="w-[170px] bg-white fixed top-0 right-0 lg:mr-[17em] md:mr-[16.5em]  mr-[15.8em] mt-[6em] z-50 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
						@mouseover="checkHover('Language', 'hover')"
						@mouseleave="checkHover('outside', 'leave')"
					>
						<div
							v-for="child in userMenus[1].child"
							:key="child.key"
							class="text-sm py-2 px-4 hover:bg-gray-100"
						>
							<div
								class="flex justify-between items-center cursor-default"
								@click="changeLocale(child)"
							>
								{{ child.name }}
								<div
									v-if="user && user.language && child.key === user.language"
									class="bg-primary-600 h-3 w-3 rounded-full"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>	
		</Popover>
	</header>
</template>

<script>
import authApi from '@/api/auth';
import { mapGetters, mapActions } from 'vuex';
import { updateLocale } from '@/libraries/http-client';
import mainMenuItems from "@/databags/main-menu.js";
import { USER_MENUS } from "@/databags/user";
import biteApi from '@/api/bite';
import galleryApi from '@/api/gallery';
import { getTooltipPosition} from '@/libraries/helper';

import $ from "jquery";

import { Popover, PopoverButton, PopoverGroup, PopoverPanel } from '@headlessui/vue';
import {
	MenuIcon,
	XIcon,
	MenuAlt2Icon,
	BellIcon,
	ChevronDownIcon,
	ChevronUpIcon,
} from '@heroicons/vue/outline';

import {
	Menu,
	MenuButton,
	MenuItem,
	MenuItems,
} from '@headlessui/vue';

export default {
	components: {
		Popover,
		PopoverGroup,
		PopoverButton,
		PopoverPanel,
		MenuAlt2Icon,
		ChevronDownIcon,
		ChevronUpIcon,
		Menu,
		MenuButton,
		MenuItem,
		MenuItems,
	},
	setup() {
		return {
			closeMenu: async (close) => {
				close();
			},
		};
	},
	data() {
		return {
			mainMenuItems,
			langMenus: [
				{name: 'English', key: 'en'},
				{name: 'Norsk', key: 'no'},
				{name: 'Bahasa', key: 'id'},
			],
			bite: null,
			gallery: [],
			isShowMenuLocalization: false,
			tooltipPosition: getTooltipPosition,
		};
	},
	computed: {
		...mapGetters({
			user: 'auth/user',
			role: 'auth/role',
			isAdmin: 'auth/isAdmin',
			getIsShowGallery: 'auth/getIsShowGallery',
			isShowOnboard: 'auth/getIsShowOnboard'
		}),
		name() {
			return this.user && this.user.name ? this.user.name : '';
		},
		activeRouteName() {
			return this.$route.name;
		},
		activeRouteSlug() {
			return this.$route.params.slug;
		},
		activeRouteId() {
			return this.$route.params.id;
		},
		notAllowedPath() {
			return this.activeRouteName !== 'TemplateEditor';
		},
		fullscreenPage() {
			const allowedFullScreen = [
				'BiteEditor',
				'BiteForm',
				'Embed',
				'Twitter',
				'Templates'
			];
			return allowedFullScreen.includes(this.activeRouteName);
		},
		isPackageMax() {
			return this.user &&  this.user.userCredit &&  this.user.userCredit.credit ? this.user.userCredit.credit : 'Unlimited';
		},
		userMenus() {
			let menus = USER_MENUS;
			const packageName = this.user && this.user.userSubscription && this.user.userSubscription.package && this.user.userSubscription.package.name ? this.user.userSubscription.package.name : '';
			const roleAdmin = this.user && this.user.roles && this.user.roles.includes('super_admin') ? true : false;
			if (!roleAdmin) {
				if ((packageName !== 'Trial' && this.user && this.user.userSubscription && !this.user.userSubscription.stripe_subscription_id) || (packageName !== 'Trial' && this.user && !this.user.userSubscription)) {
					const menus = USER_MENUS.filter(menu => menu.href !== '/subscriptions' && menu.href !== '/billing');
					return menus;
				}
			}
			return menus;
		},
		checkEnterpriseAndManualUser() {
			const packageName = this.user && this.user.userSubscription && this.user.userSubscription.package && this.user.userSubscription.package.name ? this.user.userSubscription.package.name : '';
			const roleAdmin = this.user && this.user.roles && this.user.roles.includes('super_admin') ? true : false;
			if (!roleAdmin) {
				if ((packageName !== 'Trial' && this.user && this.user.userSubscription && !this.user.userSubscription.stripe_subscription_id) || (packageName !== 'Trial' && this.user && !this.user.userSubscription) || (packageName.includes('Enterprise'))) {
					return true;
				}
			}
			return false;
		},
	},
	mounted() {
	},
	created() {
		if (this.activeRouteId) this.fetchBite();
		this.fetchGallery();
		const language = this.user && this.user.language ? this.user.language : 'en';
		this.changeLocale(language);
	},
	methods: {
		...mapActions({
			fetchUser: 'auth/fetchUser',
			toggleMenuGallery: 'auth/toggleMenuGallery',
			setIsShowOnboard: 'auth/setIsShowOnboard'
		}),
		changeLocale(locale) {
			localStorage.setItem(`locale`,  locale.key || locale);
			updateLocale(locale.key || locale);
			this.$i18n.locale = locale.key || locale;
			this.isShowMenuLocalization = false;

			const params = {language: locale.key || locale};
			this.isSaving = true;
			const callback = (response) => {
				this.fetchUser();
			};
			const errorCallback = (err) => {
				const message = err?.response?.data?.message;
				this.__showNotif('error', 'Error', message);
			};
			authApi.update(params, callback, errorCallback);

			const updateHttpClient = () => {
				// Define your new configuration
				const newConfig = {
					headers: {
						'X-New-Header': 'new-value',
					},
					// Other configuration options...
				};

				// Trigger the update by calling the updateConfig function
				updateConfig(newConfig);
			};
		},
		isActive(string) {
			let isActive = false;
			if (string === '/') isActive = this.$route.path === string;
			else if (string !== '/') isActive = window.location.href.indexOf(string) > -1;
			return isActive;
		},
		handleAvatarError() {
			const item = this.user;
			item.avatar = null;
			Object.assign(this.user, item);
		},
		upgradeAccount() {
			this.$router.push(`/subscriptions`);
		},
		backToBite() {
			this.$router.push(`/projects/${this.activeRouteSlug}/bite/${this.activeRouteId}`);
		},
		backToTemplate() {
			this.$router.push(`/templates`);
		},
		fetchBite() {
			this.isFetchingBite = true;
			const callback = (response) => {
				const item = response.data;
				this.bite = item;
				this.isFetchingBite = false;
			};
			const errorCallback = (err) => {
				const message = err?.response?.data?.message;
				this.__showNotif('error', 'Error', message);
				this.isFetchingBite = false;
			};
			biteApi.get(atob(this.activeRouteId), callback, errorCallback);
		},
		fetchGallery() {
			this.gallery = [];
			const params = {
				order_by: 'created_at',
				sort_by: 'asc',
				limit: 1,
				type: 'image'
			};
			if (this.keyword) params.keyword = this.keyword;
			const callback = (response) => {
				const data = response.data;
				this.gallery = data;
				if (this.gallery && this.gallery.length > 0 ) this.toggleMenuGallery(true);
			};
			const errorCallback = (err) => {
				const message = err?.response?.data?.message;
				this.__showNotif('error', 'Error', message);
			};
			galleryApi.getList(params, callback, errorCallback);
		},
		checkExternalLink(item) {
			if (item.name === 'Onboarding') {this.setIsShowOnboard(this.isShowOnboard === '1' ? '0' : '1'); return; };
			if (!item.href) { this.isShowMenuLocalization = false; return; }
			if (item.href !== 'https://support.bannerbite.com/') { this.$router.push(item.href); return; }
			if (item.href === 'https://support.bannerbite.com/') { window.open("https://support.bannerbite.com/"); return; }
		},
		checkHover(item, status) {
			if (status === 'hover' && item === 'Language') {
				this.isShowMenuLocalization = true;
			} else {
				this.isShowMenuLocalization = false;
			}
		},
	},
};
</script>