<template>
  <div class="px-5 mt-6">
    <!-- Projects Section Header -->
    <div class="flex items-center justify-between pl-3 mb-3">
      <div class="text-sm font-medium text-gray-500">
        {{ $t('Projects') }}
      </div>
      <button
        v-if="!isClient"
        @click="openAddModal"
        class="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
        title="Add Project"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M5 12h14"/>
          <path d="M12 5v14"/>
        </svg>
      </button>
    </div>

    <!-- Projects List with <PERSON> and Scroll -->
    <div class="max-h-[500px] overflow-y-auto mb-6">
      <ul class="space-y-1">
        <li v-if="projects.length === 0 && !isLoading" class="px-3 py-2 text-sm text-gray-500 italic">
          No projects yet. Click + to add one.
        </li>
        <li v-if="isLoading && projects.length === 0" class="px-3 py-2 text-sm text-gray-500 italic">
          Loading projects...
        </li>
        <li
          v-for="project in projects"
          :key="project.id || project.tempId"
          class="group relative text-gray-800 "
          :class="{ 'opacity-50': project.isOptimistic && project.isDeleting, 'bg-primary-600 text-white rounded': project.id === projectId }"
          @mouseenter="hoveredProject = project.id || project.tempId"
          @mouseleave="hoveredProject = null"
        >
          <div
            v-if="!(project.isOptimistic || project.isDeleting)"
            class="flex items-center gap-x-3 py-2 px-3 text-sm rounded hover:bg-gray-100 hover:text-gray-800 cursor-pointer"
            :class="{ 'bg-primary-600 text-white rounded': project.id === projectId }"
          >
            <!-- Project Color Indicator -->
            <div
              class="w-3 h-3 rounded-full flex-shrink-0"
              :style="{ backgroundColor: project.color }"
            ></div>

            <!-- Project Name as router-link -->
            <router-link
              :to="getProjectTasksRoute(project)"
              class="flex-1 font-medium truncate w-[140px]"
              @click.stop
            >
              {{ project.name }}
            </router-link>

            <!-- Actions Menu OUTSIDE router-link -->
            <div class="relative">
              <ButtonDropdown
                @click.stop=""
                v-if="hoveredProject === project.id && !isClient"
                :customClassItems="'w-32'"
              >
                <template #button>
                  <button class="text-gray-400 hover:text-gray-600 rounded transition-colors duration-200">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="black" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="1"/>
                      <circle cx="12" cy="5" r="1"/>
                      <circle cx="12" cy="19" r="1"/>
                    </svg>
                  </button>
                </template>

                <template #items>
                  <MenuItem>
                    <button
                      @click="openEditModal(project)"
                      class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                    >
                      Edit
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button
                      @click="openDeleteModal(project)"
                      class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded"
                    >
                      Delete
                    </button>
                  </MenuItem>
                </template>
              </ButtonDropdown>
            </div>
          </div>
          <div
            v-else
            class="flex items-center gap-x-3 py-2 px-3 text-sm rounded cursor-not-allowed opacity-50"
          >
            <!-- Project Color Indicator -->
            <div
              class="w-3 h-3 rounded-full flex-shrink-0"
              :style="{ backgroundColor: project.color }"
            ></div>
            <!-- Project Name -->
            <div class="flex-1 font-medium truncate">
              {{ project.name }}
            </div>
            <!-- Actions Menu OUTSIDE router-link -->
            <div class="relative">
              <ButtonDropdown
                @click.stop=""
                v-if="hoveredProject === project.id && !isClient"
                :customClassItems="'w-32'"
              >
                <template #button>
                  <button class="text-gray-400 hover:text-gray-600 rounded transition-colors duration-200">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="black" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="1"/>
                      <circle cx="12" cy="5" r="1"/>
                      <circle cx="12" cy="19" r="1"/>
                    </svg>
                  </button>
                </template>
                <template #items>
                  <MenuItem>
                    <button
                      @click="openEditModal(project)"
                      class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                    >
                      Edit
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button
                      @click="openDeleteModal(project)"
                      class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded"
                    >
                      Delete
                    </button>
                  </MenuItem>
                </template>
              </ButtonDropdown>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import ButtonDropdown from '@/components/global/ButtonDropdown.vue';
import { MenuItem } from '@headlessui/vue';
import { mapGetters } from 'vuex';
import projectApi from '@/api/project.js';

export default {
  name: 'ProjectSidebar',
  components: {
    ButtonDropdown,
    MenuItem,
  },
  emits: ['show-project-modal', 'show-delete-modal'],
  data() {
    return {
      // Projects data
      projects: [],
      isLoading: false,

      // Available colors for projects
      availableColors: [
        '#F97316', // orange
        '#10B981', // green
        '#3B82F6', // blue
        '#8B5CF6', // purple
        '#EF4444', // red
        '#F59E0B', // amber
        '#06B6D4', // cyan
        '#84CC16', // lime
      ],

      // UI state
      hoveredProject: null,
      projectSlug: null,
      projectId: null,

    };
  },
  computed: {
    ...mapGetters({
      isClient: 'auth/isClient',
    }),
  },
  mounted() {
    this.fetchProjects();
    const encryptedId = this.$route.params.id; // Get the encrypted string from route params
    if (encryptedId) {
      const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data
      if (decryptedData) {
        this.projectId = decryptedData.id;   // Access the original project ID
        this.projectSlug = decryptedData.slug; // Access the original project slug
      }
    }
  },
  watch: {
    '$route.params.id': {
      immediate: true,
      handler(newId) {
        if (newId) {
          const decryptedData = this.__decryptProjectData(newId);
          if (decryptedData) {
            this.projectId = decryptedData.id;
            this.projectSlug = decryptedData.slug;
          }
        } else {
          this.projectId = null;
          this.projectSlug = null;
        }
      }
    }
  },
  methods: {
    // Modal management
    openAddModal() {
      this.$emit('show-project-modal', {
        mode: 'add',
        project: null
      });
    },

    openEditModal(project) {
      this.$emit('show-project-modal', {
        mode: 'edit',
        project: project
      });
      this.hoveredProject = null; // Hide the dropdown
    },

    openDeleteModal(project) {
      this.$emit('show-delete-modal', project);
      this.hoveredProject = null; // Hide the dropdown
    },

    // Fetch projects from API
    fetchProjects() {
      this.isLoading = true;

      const params = {
        order_by: 'id',
        sort_by: 'desc',
        page: 1,
        limit: 100,
        isArchived: 0 // Only get non-archived projects
      };

      const callback = (response) => {
        this.projects = response.data || [];
        this.assignRandomColors();
        this.isLoading = false;
      };

      const errorCallback = (error) => {
        console.error('Fetch projects error:', error);
        this.projects = [];
        this.isLoading = false;
      };

      projectApi.getList(params, callback, errorCallback);
    },

    // Assign random colors to projects that don't have one
    assignRandomColors() {
      this.projects.forEach((project, index) => {
        if (!project.color) {
          project.color = this.availableColors[index % this.availableColors.length];
        }
      });
    },

    getProjectTasksRoute(project) {
      // If user is a client, redirect to assistant page with project ID
      if (this.isClient) {
        return `/project/assistant/${project.id}`;
      }
      // For non-client users, redirect to inquiries page
      return `/inquiries/${this.__encryptProjectData(project.id, project.slug)}`;
    },

    // Optimistic update methods
    addProjectOptimistically(projectData) {
      const tempId = 'temp_' + Date.now();
      const optimisticProject = {
        ...projectData,
        id: null,
        tempId: tempId,
        isOptimistic: true,
        color: projectData.color || this.availableColors[0]
      };

      // Add to the beginning of the list
      this.projects.unshift(optimisticProject);
      return tempId;
    },

    updateProjectOptimistically(projectId, projectData) {
      const index = this.projects.findIndex(p => p.id === projectId);
      if (index !== -1) {
        // Keep the original project data in case we need to revert
        const originalProject = { ...this.projects[index] };

        // Update with new data
        this.projects[index] = {
          ...this.projects[index],
          ...projectData,
          isOptimistic: true,
          originalData: originalProject
        };
      }
    },

    deleteProjectOptimistically(projectId) {
      const index = this.projects.findIndex(p => p.id === projectId);
      if (index !== -1) {
        // Mark as being deleted instead of removing immediately
        this.projects[index].isDeleting = true;
        this.projects[index].isOptimistic = true;
      }
    },

    confirmOptimisticAdd(tempId, realProjectData) {
      const index = this.projects.findIndex(p => p.tempId === tempId);
      if (index !== -1) {
        this.projects[index] = {
          ...realProjectData,
          color: this.projects[index].color,
          isOptimistic: false
        };
      }
    },

    confirmOptimisticUpdate(projectId, realProjectData) {
      const index = this.projects.findIndex(p => p.id === projectId);
      if (index !== -1) {
        this.projects[index] = {
          ...realProjectData,
          isOptimistic: false
        };
        delete this.projects[index].originalData;
      }
    },

    confirmOptimisticDelete(projectId) {
      const index = this.projects.findIndex(p => p.id === projectId);
      if (index !== -1) {
        this.projects.splice(index, 1);
      }
    },

    revertOptimisticAdd(tempId) {
      const index = this.projects.findIndex(p => p.tempId === tempId);
      if (index !== -1) {
        this.projects.splice(index, 1);
      }
    },

    revertOptimisticUpdate(projectId) {
      const index = this.projects.findIndex(p => p.id === projectId);
      if (index !== -1 && this.projects[index].originalData) {
        this.projects[index] = this.projects[index].originalData;
      }
    },

    revertOptimisticDelete(projectId) {
      const index = this.projects.findIndex(p => p.id === projectId);
      if (index !== -1) {
        this.projects[index].isDeleting = false;
        this.projects[index].isOptimistic = false;
      }
    },

    // Refresh projects list (called from parent when needed)
    refreshProjects() {
      this.fetchProjects();
    }
  },
};
</script>