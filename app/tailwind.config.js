const defaultTheme = require('tailwindcss/defaultTheme');
const colors = require('tailwindcss/colors');

/** @type {import("@types/tailwindcss/tailwind-config").TailwindConfig } */
module.exports = {
	content: [
		'./index.html',
		'./src/**/*.{vue,js,ts,jsx,tsx}',
		'node_modules/preline/dist/*.js',
	],
	darkMode: 'class',
	theme: {
		extend: {
			screens: {
				'sm': '480px',
				'md': '768px',
				'lg': '976px',
				'xl': '1440px',
				'2xl': '1536px',
			},
			colors: {
				primary: {
					50: '#fef2f2',
					100: '#fee2e2',
					200: '#fecaca',
					300: '#fca5a5',
					400: '#f87171',
					500: '#F24822',
					600: '#F24822',
					700: '#d73502',
					800: '#b91c1c',
					900: '#991b1b',
				},
				kb_main_purple: "#635FC7",
				kb_main_purple_hover: "#A8A4FF",
				kb_black: "#000112",
				kb_very_dark_grey: "#20212C",
				kb_dark_grey: "#2B2C37",
				kb_medium_grey: "#828FA3",
				kb_light_grey: "#F9fafb",
				kb_red: "#EA5555",
				kb_red_hover: "#FF9898",
				kb_main_purple_o25: "rgba(99,95,199,0.25)",
				kb_main_purple_o10: "rgba(99, 95, 199, 0.1)",
			},
			boxShadow: {
				board_menu: "0px 10px 20px rgba(54, 78, 126, 0.25)",
				card: "0px 4px 6px rgba(54, 78, 126, 0.10)",
			},
			fontSize: {
				f11: ".687rem",
				f12: ".75rem",
				f13: ".812rem",
				f15: ".937rem",
				f18: "1.125rem",
				f24: "1.50rem",
				f32: "2.0rem",
			},
			fontFamily: {
			},
			extend: {
				spacing: {
					'128': '32rem',
					'144': '36rem',
				},
				borderRadius: {
					'4xl': '2rem',
				}
			}
		}
	},
	plugins: [
		require('@tailwindcss/forms'),
		require('@tailwindcss/typography'),
		require('@tailwindcss/aspect-ratio'),
		require('preline/plugin'),
	],
};