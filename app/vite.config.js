import { defineConfig } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import vueI18n from '@intlify/vite-plugin-vue-i18n';
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'

export default defineConfig({
	plugins: [
    Components(),
		AutoImport({ // targets to transform
				include: [
				/\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
				/\.vue$/, /\.vue\?vue/, // .vue
				/\.md$/, // .md
				],
		}),
		vue({
			template: {
				compilerOptions: {
					isCustomElement: 
            // tag => tag.startsWith('t-input') || // vaadin web components
            tag => tag.startsWith('Listbox') // material web components
				}
			}
		}),

		vueI18n({
			// if you want to use Vue I18n Legacy API, you need to set `compositionOnly: false`
			compositionOnly: false,
			// you need to set i18n resource including paths !
			include: resolve(__dirname, './src/locales/**')
		}),

		VitePWA({
			includeAssets: ['favicon.svg', 'favicon.ico', 'robots.txt', 'apple-touch-icon.png'],
			registerType: 'prompt',
			workbox: {
				clientsClaim: false,
				skipWaiting: false,
				cleanupOutdatedCaches: true,
				sourcemap: false,
				navigateFallback: null,
				runtimeCaching: [
					{
						urlPattern: /^https?:\/\/.*\.(png|jpg|jpeg|svg|gif|webp|woff2?|eot|ttf|otf)$/i,
						handler: 'CacheFirst',
						options: {
							cacheName: 'assets-cache',
							expiration: {
								maxEntries: 100,
								maxAgeSeconds: 60 * 60 * 24 * 7 // 7 days
							}
						}
					}
				]
			},
			manifest: {
				name: 'Desidia',
				short_name: 'Desidia',
				description: 'Engage viewers with snappy, accessible and easy to remember content',
				theme_color: '#ffffff',
				background_color: '#ffffff',
				display: 'standalone',
				start_url: ".",
				scope: "./",
				icons: [
					{
						src: 'android-chrome-192x192.png',
						sizes: '192x192',
						type: 'image/png',
						purpose: 'any maskable'
					},
					{
						src: 'android-chrome-512x512.png',
						sizes: '512x512',
						type: 'image/png',
						purpose: 'any maskable'
					}
				]
			}
		}),
	],
	resolve: {
		alias: {
			'@': resolve(__dirname, 'src'),
		},
	},
	server: {
		open: true,
		port: '4444',
	},
	esbuild: {
		// sourcemap: true, // Ensure esbuild generates source maps
	},
	build: {
		// sourcemap: true, // Ensure source maps are generated in production
		experimental: {
			renderBuiltUrl(filename, { hostType }) {
				if (hostType === 'js') {
					return { runtime: `window.__toCdnUrl(${JSON.stringify(filename)})` };
				} else {
					return { relative: true };
				}
			},
		},
		rollupOptions: {
			output: {
			  // Customize the filenames for your assets
				entryFileNames: '[name].js',
				chunkFileNames: '[name].js',
				assetFileNames: '[name].[ext]'
			}
		}
	},
});
