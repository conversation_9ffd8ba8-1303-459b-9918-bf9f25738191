# Supabase Integration Reference

This document summarises every interaction that **InsightsLM** makes with Supabase so that you can re-implement the same behaviour in any JavaScript framework (Vue, Svelte, plain JS, etc.).  The examples below use **Vue 3** with the Composition API, but the code is identical for every front-end – only the place where you call it changes.

---

## Step&nbsp;1 – Install and Configure Environment

```bash
# Install the official JS client
npm i @supabase/supabase-js
```

Create two environment variables in **your front-end build system** (for Vite they must be prefixed with `VITE_`):

| Variable | Description |
|----------|-------------|
| `VITE_SUPABASE_URL` | Your project URL, e.g. `https://abcde.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | The **Anon / Public** API key |

>  Never expose the Service-Role key in client code.

---

## Step&nbsp;2 – Create the Supabase Client

```ts
// supabaseClient.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl  = import.meta.env.VITE_SUPABASE_URL
const supabaseKey  = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseKey)
```

In Vue you can provide/inject the instance globally or simply import it where needed:

```ts
import { supabase } from '@/supabaseClient'
```

---

## Step 3 – Implement Authentication

Supabase Auth methods are plain JS calls. Below are integration patterns for various frameworks to simplify adoption and LLM conversion.

### 3.1 Generic JS Usage

```ts
// Initialize session
const initAuth = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (!session) {
    const { data, error: signinError } = await supabase.auth.signInWithPassword({
      email: form.email ?? '<EMAIL>',
      password: form.password ?? 'asdfasdf',
    });
    if (signinError) throw signinError;
    // Cache the access token
    localStorage.setItem('authToken', data.session?.access_token);
  }
};

// Listen for auth state changes
supabase.auth.onAuthStateChange((event, session) => {
  console.log('Auth event', event, session);
});

// Sign out
await supabase.auth.signOut({ scope: 'local' });
```

### 3.2 Vue 3 (Composition API)

```ts
import { onMounted } from 'vue';

onMounted(async () => {
  await initAuth();
});
```

### 3.3 React Hooks

```tsx
import { useEffect } from 'react';

useEffect(() => {
  initAuth();
}, []);
```

### 3.4 Svelte

```svelte
<script>
  import { onMount } from 'svelte';

  onMount(async () => {
    await initAuth();
  });
</script>
```

### 3.5 Automatic Superadmin Login

To auto-login the `superadmin` user at startup and cache the token:

```ts
const initAuth = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (!session) {
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'asdfasdf',
    });
    if (loginError) throw loginError;
    localStorage.setItem('authToken', loginData.session?.access_token);
  }
};
initAuth();
```

## Step 4 – Query the Database

Below is a **minimal** view of the tables that the UI touches.  All operations follow the standard query builder – you can copy/paste the snippets directly.

| Table | Typical Operations |
|-------|--------------------|
| **`notebooks`** | `select`, `insert`, `update`, `delete`, realtime subscription |
| **`sources`**   | CRUD + storage file metadata |
| **`notes`**     | CRUD |
| **`n8n_chat_histories`** | read & realtime insert stream |
| **`documents`** | vector search via Postgres function `match_documents` |

### Example (Vue 3 + Composition API)

```ts
// Fetch all notebooks for the current user
await supabase.from('notebooks')
  .select('*')
  .eq('user_id', user.id)
  .order('created_at', { ascending: false })
```

```ts
// Insert a new note
await supabase.from('notes').insert({
  id: crypto.randomUUID(),
  notebook_id: notebookId,
  title: 'Untitled',
  content: '',
})
```

---

## Step&nbsp;5 – Work with Storage Buckets

Two buckets are referenced:

| Bucket | Purpose |
|--------|---------|
| `sources` | Files that the user uploads (PDFs, text, web snapshots, etc.) |
| `audio`   | Generated audio overviews for a notebook |

```ts
// Upload
await supabase.storage.from('sources')
  .upload(`${notebookId}/${file.name}`, file)

// Delete
await supabase.storage.from('audio').remove([
  `${notebookId}/overview.mp3`,
])
```

---

## Step&nbsp;6 – Call Edge Functions

The frontend invokes seven Supabase Edge Functions.  Each call sends a JSON body – replicate this structure in your framework.

| Function Name | Payload Shape |
|---------------|--------------|
| `generate-notebook-content` | `{ notebookId: string, filePath?: string, sourceType: string }` |
| `process-document`          | `{ sourceId: string, filePath: string, sourceType: string }` |
| `send-chat-message`         | `{ notebookId: string, message: string }` (simplified) |
| `generate-audio-overview`   | `{ notebookId: string }` |
| `refresh-audio-url`         | `{ notebookId: string }` |
| `generate-note-title`       | `{ noteId: string, content: string }` |
| `process-additional-sources`| `{ notebookId: string, files: string[] }` |

### What each function does
| Function | Purpose |
|----------|---------|
| `generate-notebook-content` | Reads the first source attached to a notebook and returns `title` + `description` suggestions. |
| `process-document` | Kicks off an n8n workflow that splits, chunks & embeds the uploaded file, then stores the chunks in `documents`. |
| `send-chat-message` | Streams an LLM response back to the chat UI and writes the final answer + citations to `n8n_chat_histories`. |
| `generate-audio-overview` | Calls an n8n webhook that sends the notebook summary to an AI TTS provider; result lands in `notebooks.audio_overview_url`. |
| `refresh-audio-url` | Gets the latest signed URL for the MP3 file after expiry and updates the same columns. |
| `generate-note-title` | Reads note content, asks an LLM for a concise title, updates `notes.title`. |
| `process-additional-sources` | Same as `process-document` but for multiple files dropped after notebook creation. |

>  Edge functions are deployed under `supabase/functions`.  After editing any `.ts` file, run `supabase functions deploy <name>`.

```ts
await supabase.functions.invoke('generate-notebook-content', {
  body: {
    notebookId: 'uuid',
    sourceType: 'pdf',
  },
})
```

---

## Step&nbsp;7 – Subscribe to Realtime Updates

The UI listens for Postgres changes to keep the interface live-updated.

### Channels & Payload Shapes
| Channel | Trigger | Vue handler excerpt |
|---------|---------|---------------------|
| `sources-changes` | `INSERT`, `UPDATE` on `public.sources` | ```js
supabase.channel('sources-changes')
  .on('postgres_changes', {event: '*', table: 'sources'}, p => {
    // merge new/updated source into local store
  })
  .subscribe()
``` |
| `notebooks-changes` | `INSERT`, `UPDATE`, `DELETE` on `public.notebooks` | same pattern as above |
| `chat-messages` | `INSERT` on `n8n_chat_histories` filtered by `session_id` | see `useChatMessages` example earlier |
| `notebook-audio-updates` | `UPDATE` on `notebooks.audio_overview_*` columns | auto-refreshes the player once generation finishes |

Always remember to call `supabase.removeChannel(channel)` in `beforeUnmount` (Options API) or the cleanup function of `onMounted` (Composition API) to prevent memory leaks.

```ts
const channel = supabase
  .channel('chat-messages')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'n8n_chat_histories',
    filter: `session_id=eq.${notebookId}`,
  }, payload => {
    console.log('New message', payload.new)
  })
  .subscribe()

// Later
supabase.removeChannel(channel)
```

Channels used:

* `sources-changes` – monitor inserts/updates in `sources`
* `notebooks-changes` – monitor `notebooks`
* `chat-messages` – new rows in `n8n_chat_histories`
* `notebook-audio-updates` – updates to the audio fields in `notebooks`

---

## Step&nbsp;8 – Perform Vector Search (optional)

For semantic search the project calls a Postgres function `match_documents`:

```ts
await supabase.rpc('match_documents', {
  query_embedding: embeddingAsString,
  match_count: 5,
  filter: { notebook_id: notebookId },
})
```

Replicate this RPC exactly – Supabase will type-check the payload.

---

## Step&nbsp;9 – Putting It All Together (Vue Example)

```ts
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { supabase } from '@/supabaseClient'

const notebooks = ref([])

onMounted(async () => {
  const { data } = await supabase
    .from('notebooks')
    .select('*')
    .order('created_at', { ascending: false })
  notebooks.value = data ?? []
})
</script>
```

---

### Additional Vue 3 Examples

Below are copy-paste friendly snippets that cover the rest of the Supabase surface area used by InsightsLM.

#### A. `useAuth` composable
```ts
// composables/useAuth.ts
import { ref, onMounted } from 'vue'
import { supabase } from '@/supabaseClient'

const user      = ref(null)
const session   = ref(null)
const loading   = ref(true)
const error     = ref<string | null>(null)

export function useAuth () {
  const signIn = async (email: string, password: string) => {
    const { data, error: err } = await supabase.auth.signInWithPassword({ email, password })
    if (err) throw err
    session.value = data.session
    user.value     = data.user
  }

  const signOut = async () => {
    await supabase.auth.signOut()
    session.value = null
    user.value    = null
  }

  onMounted(async () => {
    const { data } = await supabase.auth.getSession()
    session.value = data.session
    user.value    = data.session?.user ?? null
    loading.value = false
  })

  // Listen for future changes
  supabase.auth.onAuthStateChange((_, newSession) => {
    session.value = newSession
    user.value    = newSession?.user ?? null
  })

  return { user, session, loading, error, signIn, signOut }
}
```

#### B. `LoginForm.vue`
```vue
<template>
  <form @submit.prevent="onSubmit" class="space-y-4">
    <input v-model="email"  type="email"  placeholder="Email"  />
    <input v-model="password" type="password" placeholder="Password" />
    <button type="submit">Sign in</button>
  </form>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { useAuth } from '@/composables/useAuth'

const email = ref('')
const password = ref('')
const { signIn } = useAuth()

const onSubmit = async () => {
  try {
    await signIn(email.value, password.value)
  } catch (e) {
    alert((e as Error).message)
  }
}
</script>
```

#### C. File upload to `sources` bucket
```vue
<script setup lang="ts">
import { supabase } from '@/supabaseClient'

const upload = async (file: File, notebookId: string) => {
  await supabase.storage
    .from('sources')
    .upload(`${notebookId}/${file.name}`, file)
}
</script>
```

#### D. Realtime chat listener (`useChatMessages.ts`)
```ts
import { ref, onBeforeUnmount } from 'vue'
import { supabase } from '@/supabaseClient'

export function useChatMessages (notebookId: string) {
  const messages = ref<any[]>([])

  const channel = supabase
    .channel('chat-messages')
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'n8n_chat_histories',
      filter: `session_id=eq.${notebookId}`
    }, payload => {
      messages.value.push(payload.new)
    })
    .subscribe()

  onBeforeUnmount(() => supabase.removeChannel(channel))

  return { messages }
}
```

#### E. Edge Function invocation (`generate-notebook-content`)
```ts
await supabase.functions.invoke('generate-notebook-content', {
  body: {
    notebookId,
    sourceType: 'pdf',
  }
})
```

### Options API Equivalents

Prefer the classic Options API? Here are drop-in replacements for the most common pieces.

#### 1. LoginForm.vue (Options API)
```vue
<template>
  <form @submit.prevent="onSubmit" class="space-y-4">
    <input v-model="form.email"     type="email"    placeholder="Email"    />
    <input v-model="form.password"  type="password" placeholder="Password" />
    <button type="submit">Sign in</button>
  </form>
</template>

<script>
import { supabase } from '@/supabaseClient'

export default {
  data() {
    return {
      form: { email: '', password: '' },
      loading: false,
    }
  },
  methods: {
    async onSubmit () {
      this.loading = true
      const { error } = await supabase.auth.signInWithPassword({
        email: this.form.email,
        password: this.form.password,
      })
      this.loading = false
      if (error) alert(error.message)
    },
  },
}
</script>
```

#### 2. Global auth mixin
Create `mixins/auth.js` once and include it in your root component so every page gets `this.$user`.
```js
// mixins/auth.js
import { reactive } from 'vue'
import { supabase } from '@/supabaseClient'

const state = reactive({ user: null, loading: true })

export default {
  created () {
    supabase.auth.getSession().then(({ data }) => {
      state.user   = data.session?.user ?? null
      state.loading = false
    })
    supabase.auth.onAuthStateChange((_, session) => {
      state.user = session?.user ?? null
    })
  },
  computed: {
    $user () { return state.user },
    $authLoading () { return state.loading },
  },
  methods: {
    async $signOut () { await supabase.auth.signOut() },
  },
}
```

Use in any component:
```js
export default {
  mixins: [require('@/mixins/auth').default],
  mounted() {
    console.log('Current user', this.$user)
  },
}
```

#### 3. File upload method (Options API)
```js
methods: {
  async uploadFile (file, notebookId) {
    await supabase.storage
      .from('sources')
      .upload(`${notebookId}/${file.name}`, file)
  },
}
```

#### 4. Edge function call (Options API)
```js
methods: {
  async generateContent (notebookId) {
    await supabase.functions.invoke('generate-notebook-content', {
      body: { notebookId, sourceType: 'pdf' },
    })
  },
}
```

With either Composition or Options API, the Supabase calls themselves stay the same—you only change the Vue syntax.



---

### Questions?

If anything in this guide is unclear, open an issue or reach out in the community chat – happy coding!
