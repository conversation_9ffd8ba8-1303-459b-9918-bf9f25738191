# Supabase Integration Reference

This document summarises every interaction that this application makes with Supabase. The examples below use **Vue 3** with the Composition API, but the patterns are easily adaptable to other frameworks.

---

### Step 1: Install and Configure Environment

First, install the official Supabase JS client:

```bash
# Install the official JS client
npm i @supabase/supabase-js
```

Next, create a `.env` file in your project root and add your Supabase credentials. For Vite, all environment variables exposed to the client **must** be prefixed with `VITE_`.

```sh
# .env
VITE_SUPABASE_URL=your-project-url
VITE_SUPABASE_ANON_KEY=your-anon-key
```

| Variable | Description |
|---|---|
| `VITE_SUPABASE_URL` | Your project URL, e.g. `https://abcde.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | The **Anon / Public** API key |

> **Security Note:** Never expose the `service_role` key or other private keys in client-side code. The public/anon key is designed for this purpose.

---

### Step 2: Create the Supabase Client

It's best practice to create a single Supabase client instance and export it for use throughout your application. Create a file `src/supabaseClient.ts`:

```ts
// src/supabaseClient.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseKey)
```

You can now import this instance in any component or composable:

```ts
import { supabase } from '@/supabaseClient' // Assuming '@' is an alias for '/src'
```

---

### Step 3: Implement Authentication

Authentication is managed through a `useAuth` composable for clean, reusable logic. This composable handles sign-in, sign-out, and listens for authentication state changes.

#### 3.1 `useAuth.ts` Composable

```ts
// src/composables/useAuth.ts
import { ref, onMounted } from 'vue'
import { supabase } from '@/supabaseClient'
import type { Session, User } from '@supabase/supabase-js'

const user = ref<User | null>(null)
const session = ref<Session | null>(null)
const loading = ref(true)

export function useAuth() {

  async function signInWithPassword(email: string, pass: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: pass,
    })
    if (error) throw error
    return data
  }

  async function signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  // Auto-login superadmin for development
  async function autoLoginSuperAdmin() {
    const { data } = await supabase.auth.getSession();
    if (!data.session) {
        await signInWithPassword('<EMAIL>', 'asdfasdf');
    }
  }

  onMounted(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data }) => {
      session.value = data.session
      user.value = data.session?.user ?? null
      loading.value = false
    })

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, newSession) => {
      session.value = newSession
      user.value = newSession?.user ?? null
      loading.value = false
    })

    // Unsubscribe on component unmount
    onBeforeUnmount(() => {
        subscription?.unsubscribe();
    });
  })

  return { user, session, loading, signInWithPassword, signOut, autoLoginSuperAdmin }
}
```

#### 3.2 Usage in a Component

```vue
<!-- src/pages/assistant/Index.vue -->
<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuth } from '@/composables/useAuth'

const { autoLoginSuperAdmin } = useAuth()

onMounted(() => {
  // Automatically log in the superadmin when the app starts
  autoLoginSuperAdmin()
})
</script>
```

---

### Step 4: Query the Database

All database operations use the standard Supabase query builder.

| Table | Operations |
|---|---|
| `notebooks` | `select`, `insert`, `update`, `delete` |
| `sources` | CRUD + file metadata |
| `notes` | CRUD |
| `n8n_chat_histories` | `select`, `insert` (via realtime) |
| `documents` | Vector search via `match_documents` |

**Example: Fetching Notebooks**

```ts
import { supabase } from '@/supabaseClient'

async function getNotebooks(userId: string) {
  const { data, error } = await supabase
    .from('notebooks')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}
```

---

### Step 5: Work with Storage Buckets

This app uses two storage buckets:

| Bucket | Purpose |
|---|---|
| `sources` | User-uploaded files (PDFs, text, etc.) |
| `audio` | Generated audio overviews for notebooks |

**Example: Uploading and Deleting Files**

```ts
import { supabase } from '@/supabaseClient'

// Upload a file to the 'sources' bucket
async function uploadSourceFile(notebookId: string, file: File) {
  const { data, error } = await supabase.storage
    .from('sources')
    .upload(`${notebookId}/${file.name}`, file)

  if (error) throw error
  return data
}

// Delete an audio file
async function deleteAudioOverview(notebookId: string) {
  const { data, error } = await supabase.storage
    .from('audio')
    .remove([`${notebookId}/overview.mp3`])
  
  if (error) throw error
  return data
}
```

---

### Step 6: Call Edge Functions

Edge Functions are invoked to perform server-side tasks.

| Function Name | Payload Shape |
|---|---|
| `generate-notebook-content` | `{ notebookId: string, filePath?: string, sourceType: string }` |
| `process-document` | `{ sourceId: string, filePath: string, sourceType: string }` |
| `send-chat-message` | `{ notebookId: string, message: string }` |
| `generate-audio-overview` | `{ notebookId: string }` |

**Example: Invoking a Function**

```ts
import { supabase } from '@/supabaseClient'

async function generateTitle(noteId: string, content: string) {
  const { data, error } = await supabase.functions.invoke('generate-note-title', {
    body: { noteId, content },
  })

  if (error) throw error
  return data
}
```

---

### Step 7: Subscribe to Realtime Updates

Realtime subscriptions keep the UI in sync with database changes without requiring manual polling.

| Channel | Event | Table | Purpose |
|---|---|---|---|
| `sources-changes` | `*` | `sources` | Refresh source list on add/update. |
| `notebooks-changes` | `*` | `notebooks` | Refresh notebook list on CRUD. |
| `chat-messages` | `INSERT` | `n8n_chat_histories` | Display new chat messages instantly. |

**Example: Subscribing to Chat Messages**

```ts
import { supabase } from '@/supabaseClient'
import { onMounted, onUnmounted } from 'vue'

const notebookId = 'some-notebook-id';

// Create a channel
const channel = supabase.channel('chat-messages')

const subscribeToMessages = () => {
  channel.on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'n8n_chat_histories',
    filter: `session_id=eq.${notebookId}`,
  }, payload => {
    console.log('New message received:', payload.new)
    // Add the new message to your local state
  }).subscribe()
}

const unsubscribeFromMessages = () => {
  supabase.removeChannel(channel)
}

onMounted(() => {
  subscribeToMessages()
})

onUnmounted(() => {
  unsubscribeFromMessages()
})
```

> Always remember to unsubscribe from channels when a component is unmounted to prevent memory leaks and unnecessary connections.

